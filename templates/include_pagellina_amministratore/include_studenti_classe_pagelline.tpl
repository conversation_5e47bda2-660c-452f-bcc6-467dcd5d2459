<table width='35%' class="sfondo_riquadro_generico contenitore_generico">
	<tr>
		<td>
			<table width='100%'>
				<tr>
                    <td class='titolo_funzione'>
						Classe: <br>{$classe} {$indirizzo}
					</td>
				{if $dati_classe.tipo_indirizzo == "2" and (($dati_classe.classe == "1") or ($dati_classe.classe == "2") or ($dati_classe.classe == "3") or ($dati_classe.classe == "4"))}
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona="ammissione"
							size=$dimensione_immagini
                            descrizione="Inserimento dati ammissione esami"
							label=AMMISSIONE
							label_bg='3c2f67'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-university sfondo_azzurro_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}Ammissione{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='dati_ammissione_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona=qualifica
							size=$dimensione_immagini
                            descrizione="Inserimento dati qualifica"
							label=QUALIFICA
							label_bg='3c2f67'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-medal sfondo_viola_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}Qualifica{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='dati_qualifica_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				{/if}
				{if $trentino_abilitato == 'SI'}
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
						<button type="submit" class="mc-icon-2 margin0 ripples">
							<div class="mc-icon-img-2">
								<i class="fa fa-fw fa-backspace sfondo_viola_old"></i>
							</div>
							<div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}Recupero{/mastercom_label}</div>
						</button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='gestione_debiti'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				<form method='post' action='{$SCRIPT_NAME}'>
					<td  width='5%'>
							<!--{mastercom_auto_button
							icona="stampa"
							size=$dimensione_immagini
                            descrizione="Modifica - Stampa Riepilogo Debiti Classe"
							label=DEBITI
							label_bg='1d8eb6'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-print sfondo_azzurro_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Debiti{/mastercom_label}</div>
                                                </button>

						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='stampa_riepilogo_debiti_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</td>
				</form>
				{/if}
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona="tabellone"
							size=$dimensione_immagini
                            descrizione="Modifica - Stampa Tabellone Pagelline"
							label=TABELLONE
							label_bg='3c2f67'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-money-check sfondo_grigioblu_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Tabellone{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='gestione_tabellone_pagelline_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona="stampa"
							size=$dimensione_immagini
                            descrizione="Stampa tutte le Pagelle/Pagelline della Classe Selezionata"
							label=PAGELLE
							label_bg='1d8eb6'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-print sfondo_azzurro_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Pagelle{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='stampa_pagelline_classe_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona="stampa"
							size=$dimensione_immagini
                            descrizione="Stampa Tabellone di Riepilogo con le medie dei voti della classe in tutte le materie"
							label=MEDIE
							label_bg='1d8eb6'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-print sfondo_azzurro_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Medie{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='stampa_tabellone_medie_voti_display'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
                                <td width='5%'>
                                    <form method='post' action='{$SCRIPT_NAME}'>
                                         <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-file-upload sfondo_grigioblu_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Documenti{/mastercom_label}</div>
                                                </button>
                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                        <input type='hidden' name='stato_secondario' value='multi_documenti'>
                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                        <input type='hidden' name='classe' value='{$classe}'>
                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                </td>
                                {if $dati_classe.tipo_indirizzo == "4" and $dati_classe.classe == "3" and $trentino_abilitato == 'NO'}
                                <td width='5%'>
                                    <form method='post' action='{$SCRIPT_NAME}'>
                                        <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-map-signs sfondo_grigioblu_old sfondo_grigioblu_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Cons.Orient.{/mastercom_label}</div>
                                        </button>
                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                        <input type='hidden' name='stato_secondario' value='multi_consiglio_orientativo'>
                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                        <input type='hidden' name='classe' value='{$classe}'>
                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                    </form>
                                </td>
                                {/if}

                {if $mostra_alternanza == 'SI'}
                    <td width='5%'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                                <!--{mastercom_auto_button
                                icona="stampa"
                                size=$dimensione_immagini
                                descrizione="Stampa i progetti di alternanza della classe"
                                label=ALTERNANZA
                                label_bg='1d8eb6'
                                }-->
                            <button type="submit" class="mc-icon-2 margin0 ripples">
                                <div class="mc-icon-img-2">
                                    <i class="fa fa-fw fa-print sfondo_azzurro_old"></i>
                                </div>
                                <div class="mc-icon-text sfondo_grigioblu_old_op20 mc-icon-shadow">{mastercom_label}Alternanza{/mastercom_label}</div>
                            </button>
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                            <input type='hidden' name='stato_secondario' value='stampa_alternanza_display'>
                            <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                            <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                            <input type='hidden' name='classe' value='{$classe}'>
                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
				{if $superutente_int eq 'SI'}
				<td width='5%'>
					<form method='post' action='{$SCRIPT_NAME}'>
							<!--{mastercom_auto_button
							icona=qualifica
							size=$dimensione_immagini
                            descrizione="Test"
							label=TEST
							label_bg='3c2f67'
							}-->
                                                <button type="submit" class="mc-icon-2 margin0 ripples">
                                                    <div class="mc-icon-img-2">
                                                        <i class="fa fa-fw fa-medal sfondo_viola_old"></i>
                                                    </div>
                                                    <div class="mc-icon-text sfondo_viola_old_op20 mc-icon-shadow">{mastercom_label}Test{/mastercom_label}</div>
                                                </button>
						<input type='hidden' name='form_stato' value='{$form_stato}'>
						<input type='hidden' name='stato_principale' value='{$stato_principale}'>
						<input type='hidden' name='stato_secondario' value='tabellone_scrutini_jqx'>
						<input type='hidden' name='indirizzo' value='{$indirizzo}'>
						<input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
						<input type='hidden' name='classe' value='{$classe}'>
						<input type='hidden' name='id_classe' value='{$id_classe}'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
				{/if}
			</tr>
			</table>
			<table width='100%'>
				{section name=cont3 loop=$elenco_studenti}
                    <tr class='{cycle values="sfondo_base_generico,sfondo_contrasto_generico"}'>
                        <td width='5%' align='center' class='divisore_studenti'>
						{$elenco_studenti[cont3][3]}
					</td>
                    <td width='90%' class='divisore_studenti'>
					{if $elenco_studenti[cont3].esito != 'Iscritto'}
					<font color='#880000'>{$elenco_studenti[cont3][2]} {$elenco_studenti[cont3][1]} ({$elenco_studenti[cont3].esito})</font>
					{else}
					<font color='#000000'>{$elenco_studenti[cont3][2]} {$elenco_studenti[cont3][1]}</font>
					{/if}
                    </td>
				</tr>
				{/section}
			</table>
		</td>
	</tr>
</table>
