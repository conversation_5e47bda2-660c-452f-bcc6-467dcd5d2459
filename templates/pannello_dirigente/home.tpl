
<script type="text/javascript">
{literal}
    var nextapiPath = "../next-api/v1/";

    function apriDettaglioCorso(idCorso) {
        apriDettaglioCorsoCall(idCorso).then((corso) => {
            $("#lista_studenti").html("");
            $("#lista_docenti").html("");
            $("#titolo_corso").html(corso.descrizione);

            var partecipanti = '';
            if (corso.partecipanti.length > 0){
                corso.partecipanti.forEach((stud) => {
                    partecipanti += "<li>" + stud.nome_completo + "</li>";
                });
            } else {
                partecipanti += "<li>Non ci sono partecipanti</li>";
            }
            $("#lista_studenti").append(partecipanti);

            var docenti = '';
            if (corso.professori.length > 0){
                corso.professori.forEach((doc) => {
                    docenti += "<li>" + doc.nome_completo + "</li>";
                });
            } else {
                docenti += "<li>Non ci sono docenti</li>";
            }

            $("#lista_docenti").append(docenti);
            apriChiudiPopup('dettaglio_corso', 'sfondo_oscurato');
        });
    }

    function apriDettaglioCorsoCall(idCorso) {
    $('#jqxGeneralLoader').jqxLoader('open');
    return new Promise((resolve, reject) => {
        $.ajax({
            type: "GET",
            data: {},
            url: nextapiPath + "courses/" + idCorso,
            headers: { 'Authorization': $("[name='current_key']").val() },
            success: function (data) {
                $('#jqxGeneralLoader').jqxLoader('close');
                resolve(data);
            },
            error: function (data) {
                $('#jqxGeneralLoader').jqxLoader('close');
                creaToast("Errore", 'error');
                reject(data);
            },
            complete: function (data) {
                if (data.responseJSON) {

                } else {
                    console.log($.parseJSON(data.responseJSON));
                    creaToast("Errore", 'error');
                }
                $('#jqxGeneralLoader').jqxLoader('close');
                resolve(data);
            }
        });
    });
}

    $(document).ready(function () {
        //--- Calendario JQX
        $("#jqxCalendar").jqxCalendar({ width: '400px', height: '400px', enableTooltips: false, theme: 'Bootstrap', culture: 'it-IT', enableViews: false, columnHeaderHeight: 40, titleHeight: 40, showOtherMonthDays: false });
        sistemaCalendario("jqxCalendar");
        $("#jqxCalendar").hide();
        $('#jqxCalendar').on('change', function (event)
        {
            var jsDate = new Date;
            jsDate = event.args.date;
            var type = event.args.type; // keyboard, mouse or null depending on how the date was selected.
            var input_hidden = $('#data_da_calendario');

            var dataIso = jsDate.getDate() + '/' + (jsDate.getMonth() + 1) + '/' + jsDate.getFullYear();
            input_hidden.val(dataIso);
            input_hidden[0].form.submit();
        });
        //---
    });
{/literal}
</script>

{if $messaggio}
    <div id="message" class="messaggio_basso_scomparsa" style="font-size: 110%">
        {$messaggio}
    </div>
{/if}
<div class="div_titolo_scheda_generica sfondo_scuro" style="width: 98%; background-color: #455A64;" align="center">
    <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap;">
        <div align="left" style="padding-left: 10px; font-size: 120%;">
            Home
        </div>
        {if $oggi == 'SI'}
            <div align="left" style="padding: 0px 10px; font-size: 120%;">
                OGGI
            </div>
        {/if}
        <div style='padding-right: 10px;'>
            <form method='post' action='{$SCRIPT_NAME}'>
                <input type="button"
                        class="btn_flat_bianco"
                        value="&#9001;"
                        title="Giorno precedente"
                        style="font-size: 110%;"
                        onclick="this.form.giorno_scelto.value='{$giorno_precedente}'; this.form.submit();"
                        >
                {* Pulsante calendario con giorno corrente *}
                <div style="position: relative; display: inline-block;">
                    <input type="button" class="btn_pieno" style="background-color: #FAFAFA; color:#455A64; border-radius: 32px" value="{$data['giorno2']} {$data['mese_tradotto']} {$data['anno']}" title="Apri il calendario" onclick="apriChiudiPopup('jqxCalendar', '');">
                    <div style="position: absolute; z-index: 1000;" id="jqxCalendar"></div>
                </div>
                {* Pulsante giorno successivo *}
                <input
                    type="button"
                    class="btn_flat_bianco"
                    value="&#9002;"
                    title="Giorno successivo"
                    style="font-size: 110%;"
                    onclick="this.form.giorno_scelto.value='{$giorno_successivo}'; this.form.submit();"
                    >
                <input type="hidden" name="giorno_scelto" value="">
                <input type="hidden" name="data_da_calendario"  id="data_da_calendario" value="">
                <input type="hidden" name="stato_secondario" value="{$stato_secondario}">
                <input type='hidden' name='form_stato' value='{$form_stato}'>
                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                <input type='hidden' name='current_user' value='{$current_user}'>
                <input type='hidden' name='current_key' value='{$current_key}'>
            </form>
        </div>
    </div>
</div>
<div class="div_corpo_scheda_generica" align="center" style="width: 98%; color: black; font-weight: normal; background-color: #e0e0e0;">
    <div style="width: 100%; display: flex; flex-wrap: wrap;">

        <div style="width: 300px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1;">
            {* DATA SANTI E COMPLEANNI *}
            <table width="100%" style="border-collapse: collapse; border-left: 3px solid red;">
                <tr>
                    <td rowspan="2" style="font-size: 60px; line-height: 60px; padding-left: 10px;">
                        {$data['giorno']}
                    </td>
                    <td width="100%" align="left">
                        <b>{$data['mese_tradotto']} {$data['anno']}</b>
                    </td>
                </tr>
                <tr>
                    <td width="100%" align="left" style="font-size: 18px; line-height: 18px;">
                        <b>{$data['giorno_tradotto']}</b>
                    </td>
                </tr>
            </table>
            <br><br>
            {if $data['santi'] != '' && $santi_registro == 'SI'}
                <table width="100%" style="border-collapse: collapse; border-left: 3px solid #4d8e4d;">
                    <tr>
                        <td align="left" style="padding-left: 10px; height: 30px;">
                            Santi del giorno
                        </td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left: 10px; height: 30px;">
                            <b><i>{$data['santi']}</i></b>
                        </td>
                    </tr>
                </table>
            {/if}
            {if $compleanni_registro == 'SI'}
                <br><br><br>
                <img src="icone/cake.png" style="width: 30px; margin: 5px;"/>
                <br><br>
                {if $data['compleanni']|@count > 0}
                    {foreach $data['compleanni'] as $festeggiato}
                        <b>{$festeggiato['cognome']} {$festeggiato['nome']}</b> oggi compie {$festeggiato['anni_compiuti']} anni <br><br>
                    {/foreach}
                {else}
                    Non ci sono compleanni oggi
                {/if}
            {/if}
        </div>

        <div style="width: 400px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1;">
            {* ASSENZE GIORNALIERE *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>ASSENZE DEL GIORNO</b></div>
            <table width="100%" align="center">
            {if $assenze_giornaliere|@count > 0}
                {foreach $assenze_giornaliere as $assenza}
                    <tr width="100%">
                        <td width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                            <b>{$assenza['tipo_assenza_tradotta']}</b>
                        </td>
                    </tr>
                    {assign var=cont value=0}
                    {foreach $assenza['studenti'] as $studente}
                    {if $cont == 5}
                        <tbody id="assenze_{$assenza['tipo_assenza_tradotta']}" style="display: none;">
                    {/if}
                        <tr width="100%">
                            <td width="100%">
                                • {$studente['cognome']} {$studente['nome']}
                                {if $studente['orario_tipologia']|trim != ''}
                                    - {$studente['orario_tipologia']} -
                                {else if $studente['orario']|trim != ''}
                                    - {$studente['orario']} -
                                {/if}
                            (<span style="font-style: italic; font-size: 90%;">{$studente['classe']}{$studente['sezione']} {$studente['codice_indirizzi']} - <span class="annotazione_leggera">{if $studente['nome_chi_inserisce'] == 'nexus nexus'}rilevazione autom.{else}{$studente['nome_chi_inserisce']}{/if}</span></span>)
                            </td>
                        </tr>
                    {assign var=cont value=$cont+1}
                    {/foreach}
                    {if $cont > 5}
                        </tbody>
                        <tr width="100%">
                            <td width="100%" align="center">
                                <button type="button"
                                        title="Mostra/Nascondi altre"
                                        class="btn_flat annotazione_leggera"
                                        onclick="apriChiudiPopup('assenze_{$assenza['tipo_assenza_tradotta']}', '');"
                                        > Mostra altri </button>
                            </td>
                        </tr>
                    {/if}
                {/foreach}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono assenze oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        <div style="width: 400px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* ORE FIRMATE FINO AD ORA *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>ORE FIRMATE</b></div>
            <table align="center">
            {if $cont_ore_firme['ore'] > 0}
                <tr>
                    <td><br></td>
                </tr>
                <tr width="100%">
                    <td class="padding_cella_generica">Ore effettuate:</td>
                    <td>
                        <b>{$cont_ore_firme['ore']}</b>
                    </td>
                    <td rowspan='3' style="padding-left: 50px;">
                        <canvas id="myCanvas"></canvas>
                        <script type="text/javascript">
                            var myCanvas = document.getElementById("myCanvas");
                            myCanvas.width = 150;
                            myCanvas.height = 150;

                            var ctx = myCanvas.getContext("2d");

                            var firme = {
                                "Firme": {$cont_ore_firme['firme']},
                                "Ore non firmate": {$cont_ore_firme['ore'] - $cont_ore_firme['firme']}
                            };

                            var myPiechart = new Piechart(
                                {
                                    canvas:myCanvas,
                                    data:firme,
                                    colors:["#82ff5e","#ff6d6d"],
                                    doughnutHoleSize:0.5,
                                    doughnutHolecolor:"#f1f1f1"
                                }
                            );
                            myPiechart.draw();
                        </script>
                    </td>
                </tr>
                <tr width="100%">
                    <td class="padding_cella_generica">Firme apposte:</td>
                    <td>
                        <div class="btn_pieno" style="color: black; border-radius: 20px; background-color: #82ff5e; padding: 5px 5px; box-shadow: none; cursor: default;">
                            <b>{$cont_ore_firme['firme']}</b>
                        </div>
                    </td>
                </tr>
                <tr width="100%">
                    <td class="padding_cella_generica">Firme mancanti:</td>
                    <td>
                        <div class="btn_pieno" style="color: black; border-radius: 20px; background-color: #ff6d6d; padding: 5px 5px; box-shadow: none; cursor: default;">
                            <b>{$cont_ore_firme['ore'] - $cont_ore_firme['firme']}</b>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" align="center" class="annotazione_leggera" style="padding-top: 20px;">
                        aggiornato alle {$ora_controllo}
                    </td>
                </tr>
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono ore oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        <div style="width: 500px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* BRUTTI VOTI DEL GIORNO *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>BRUTTI VOTI INSERITI OGGI</b></div>
            <table width="100%" align="center">
            {if $insufficienti|@count > 0}
                <tr width="100%" style="font-size: 90%;">
                    <td align="center" style="padding-top: 10px; width: 50%;" class="padding_cella_generica">
                        <i>studente</i>
                    </td>
                    <td align="center" style="padding-top: 10px; width: 50%;" class="padding_cella_generica">
                        <i>materia</i>
                    </td>
                    <td align="center" style="padding-top: 10px; width: 65px;" class="padding_cella_generica">
                        <i>data voto</i>
                    </td>
                    <td align="center" style="padding-top: 10px; width: 30px;" class="padding_cella_generica">
                        <i>voto</i>
                    </td>
                </tr>
                {assign var=cont value=0}
                {foreach $insufficienti as $insufficienza}
                    {if $cont == 5}
                    <tbody id="voti_insufficienti_nascosti" style="display: none;">
                    {/if}
                    <tr width="100%">
                        <td style="padding-top: 10px;" class="padding_cella_generica">
                            <b>{$insufficienza['studente']}</b><br>({$insufficienza['classe']}{$insufficienza['sezione']} {$insufficienza['codice_indirizzi']})
                        </td>
                        <td style="padding-top: 10px;" class="padding_cella_generica">
                            {$insufficienza['nome_materia_breve']}
                        </td>
                        <td align="center" style="padding-top: 10px;" class="padding_cella_generica">
                            {$insufficienza['data_voto_tradotta']}
                        </td>
                        <td align="center" style="padding-top: 10px;" class="padding_cella_generica">
                            <b>{$insufficienza['voto']}</b>
                        </td>
                    </tr>
                    {assign var=cont value=$cont+1}
                {/foreach}
                {if $cont > 5}
                    </tbody>
                    <tr width="100%">
                        <td width="100%" align="center" colspan="4">
                            <button type="button"
                                    title="Mostra/Nascondi altre"
                                    class="btn_flat annotazione_leggera"
                                    onclick="apriChiudiPopup('voti_insufficienti_nascosti', '');"
                                    > Mostra altri </button>
                        </td>
                    </tr>
                {/if}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non sono stati inseriti brutti voti oggi :)
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        <div style="width: 400px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* NOTE DISCIPLINARI DEL GIORNO *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>NOTE INSERITE OGGI</b></div>
            <table width="100%" align="center">
            {if $note_disciplinari|@count > 0}
                <tr>
                    <td><br></td>
                </tr>
                {assign var=cont value=0}
                {foreach $note_disciplinari as $nota}
                    {if $cont == 3}
                    <tbody id="note_inserite_nascoste" style="display: none;">
                    {/if}
                    <tr width="100%">
                        <td style="width: 60px; border-left: 2px solid #ff3030;" class="padding_cella_generica">Docente:</td>
                        <td>
                            <b>{$nota['cognome']} {$nota['nome']}</b>
                        </td>
                        <td style="width: 75px;" class="padding_cella_generica">Data e ora:</td>
                        <td>
                            {$nota['data_tradotta']}
                        </td>
                    </tr>
                    <tr width="100%">
                        <td style="border-left: 2px solid #ff3030;" class="padding_cella_generica">Classi:</td>
                        <td class="padding_cella_generica">
                            {$nota['elenco_classi']}
                        </td>
                        <td class="padding_cella_generica">Studenti:</td>
                        <td class="padding_cella_generica">
                            <b>{$nota['elenco_studenti']}</b>
                        </td>
                    </tr>
                    <tr width="100%">
                        <td style="border-left: 2px solid #ff3030;" class="padding_cella_generica">Testo:</td>
                        <td colspan="3" class="padding_cella_generica">
                            {$nota['testo']}
                        </td>
                    </tr>
                    <tr width="100%">
                        <td><br></td>
                    </tr>
                    {assign var=cont value=$cont+1}
                {/foreach}
                {if $cont > 3}
                    </tbody>
                    <tr width="100%">
                        <td width="100%" align="center" colspan="4">
                            <button type="button"
                                    title="Mostra/Nascondi altre"
                                    class="btn_flat annotazione_leggera"
                                    onclick="apriChiudiPopup('note_inserite_nascoste', '');"
                                    > Mostra altri </button>
                        </td>
                    </tr>
                {/if}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non sono state inserite note oggi ;)
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        <div style="width: 400px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* STATISTICHE NOTE DISCIPLINARI *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>STATISTICHE NOTE</b></div>
            <table style="width: 300px;" align="center">
                <tr>
                    <td><br></td>
                </tr>
                <tr>
                    <td width="100%" align="left" class="padding_cella_generica">Pubblicate:</td>
                    <td align="center" class="padding_cella_generica">
                        <form method='post' action='{$SCRIPT_NAME}'>
                            <div class="btn_pieno" style="color: black; border-radius: 20px; background-color: #82ff5e; padding: 5px 5px; box-shadow: none;"
                                    onclick="getElementById('filtro_pubblica').form.submit();"
                                    >
                                <b>{$statistiche_note['pubblicate']}</b>
                            </div>
                            <input type="hidden" name="filtro_stato" id="filtro_pubblica" value="pubblica">
                            <input type="hidden" name="stato_secondario" value="note_disciplinari">

                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="padding_cella_generica">Non pubblicate:</td>
                    <td align="center" class="padding_cella_generica">
                        <form method='post' action='{$SCRIPT_NAME}'>
                            <div class="btn_pieno" style="color: black; border-radius: 20px; background-color: #82ff5e; padding: 5px 5px; box-shadow: none;"
                                    onclick="getElementById('filtro_no_pubblica').form.submit();"
                                    >
                                <b>{$statistiche_note['non_pubblicate']}</b>
                            </div>
                            <input type="hidden" name="filtro_stato" id="filtro_no_pubblica" value="no_pubblica">
                            <input type="hidden" name="stato_secondario" value="note_disciplinari">

                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                </tr>
                <tr>
                    <td align="left" class="padding_cella_generica">Non valutate:</td>
                    <td align="center" class="padding_cella_generica">
                        <form method='post' action='{$SCRIPT_NAME}'>
                            <div class="btn_pieno" style="color: black; border-radius: 20px; background-color: #ff6d6d; padding: 5px 5px; box-shadow: none;"
                                    onclick="getElementById('filtro_non_valutata').form.submit();"
                                    >
                                <b>{$statistiche_note['non_valutate']}</b>
                            </div>
                            <input type="hidden" name="filtro_stato" id="filtro_non_valutata" value="">
                            <input type="hidden" name="stato_secondario" value="note_disciplinari">

                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                </tr>
                <tr>
                    <td class="padding_cella_generica"><br></td>
                </tr>
                <tr>
                    <td colspan="2">
                        {if $statistiche_note['non_valutate'] == 0}
                            <div style="background-color: #82ff5e; height: 30px; width: 100%; border-radius: 15px;"></div>
                        {elseif $statistiche_note['valutate'] == 0}
                            <div style="background-color: #ff6d6d; height: 30px; width: 100%; border-radius: 15px;"></div>
                        {else}
                            <table width='100%'>
                                <tr width='100%'>
                                    <td width='{$statistiche_note['percentuale_valutate']}%'>
                                        <div style="background-color: #82ff5e; height: 30px; border-top-left-radius: 15px; border-bottom-left-radius: 15px;"></div>
                                    </td>
                                    <td width='{$statistiche_note['percentuale_non_valutate']}%'>
                                        <div style="background-color: #ff6d6d; height: 30px; border-top-right-radius: 15px; border-bottom-right-radius: 15px;"></div>
                                    </td>
                                </tr>
                            </table>
                        {/if}
                    </td>
                </tr>
            </table>
        </div>

        <div style="width: 500px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* EVENTI DEL GIORNO *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>EVENTI DEL GIORNO</b></div>
            <table width="100%" align="center">
            {if $elenco_eventi|@count > 0}
                {foreach $elenco_eventi as $eventi_giorno}
                    {foreach $eventi_giorno as $evento}
                        <tr width="100%">
                            <td width="100%" style="padding-top: 10px;" class="padding_cella_generica">
                                <b>{$evento['nome']}</b>
                            </td>
                        </tr>
                        <tr width="100%">
                            <td width="100%" style="padding-bottom: 5px;">
                                <i>{$evento['data_inizio_tradotta']} - {$evento['data_fine_tradotta']}</i>
                            </td>
                        </tr>
                        <tr width="100%">
                            <td width="100%">
                                Classi: {$evento['elenco_classi']}
                            </td>
                        </tr>
                    {/foreach}
                {/foreach}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono eventi in programma o in corso oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        {if $corsi_pannello_dirigente == 'SI'}
        <div style="width: 500px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* CORSI DEL GIORNO *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>CORSI DEL GIORNO</b></div>
            <table width="100%" align="center">
            {if $elenco_corsi|@count > 0}
                {foreach $elenco_corsi as $corso}
                    <tr width="100%" class="evidenzia pointer" onclick="apriDettaglioCorso({$corso['id_classe']});">
                        <td class="padding_cella_generica bordo_destro_generico nowrap" style="width: 100px;">
                            <b>{$corso['ora_inizio']} - {$corso['ora_fine']}</b>
                        </td>
                        <td class="padding_cella_generica">
                            <b>{$corso['sezione']}</b>
                        </td>
                    </tr>
                {/foreach}
            {else}
                <tr width="100%">
                    <td colspan="2" align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono corsi oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>
        {/if}

        <div style="width: 500px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* MESSAGGI INVIATI A PARENTI *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>MESSAGGI INVIATI OGGI A PARENTI</b></div>
            <table width="100%" align="center">
            {if $messaggi_a_parenti|@count > 0}
                <tr>
                    <td><br></td>
                </tr>
                {foreach $messaggi_a_parenti as $key => $mex}
                    <tr width="100%" onclick="apriChiudiPopup('mex_a_parenti_{$key}', '');" style="cursor: pointer;">
                        <td style="border-left: 2px solid #0091ea; background-color: #e5efff;" class="padding_cella_generica">
                            <b>{$mex['name']}</b>
                        </td>
                        <td align="right" style="background-color: #e5efff; border-right: 3px solid #e5efff;" class="padding_cella_generica">
                            da {$mex['from']['fullname']}
                        </td>
                    </tr>
                    <tbody id="mex_a_parenti_{$key}" style="display: none;">
                    <tr width="100%">
                        <td colspan="2" style="border-left: 2px solid #0091ea; border-right: 3px solid #e5efff;"  class="padding_cella_generica">
                            {$mex['content']}
                            {if $mex['files'] > 0}
                                <br>
                                <i>(allegati: {$mex['files']})</i>
                            {/if}
                        </td>
                    </tr>
                    <tr width="100%">
                        <td colspan="2" width="100%" style="border-left: 2px solid #0091ea; border-right: 3px solid #e5efff; background-color: #e5efff;"  class="padding_cella_generica">
                            <table width="100%">
                                <tr width="100%">
                                    <td colspan="2" align="center">
                                        Destinatari
                                    </td>
                                </tr>
                                <tr width="100%">
                                    <td width="50%" align="center">
                                        <i>Parenti</i>
                                    </td>
                                    <td width="50%" align="center">
                                        <i>Docenti</i>
                                    </td>
                                </tr>
                                <tr>
                                <td style="padding-left: 10px; vertical-align: top;">
                                    {foreach $mex['to']['parenti'] as $parente}
                                        • {$parente['fullname']}<br>
                                    {/foreach}
                                </td>
                                <td style="padding-left: 10px; vertical-align: top;">
                                    {foreach $mex['to']['docenti'] as $docente}
                                        • {$docente['fullname']}<br>
                                    {/foreach}
                                </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                    <tr width="100%"  style="height: 10px;">
                        <td> </td>
                    </tr>
                {/foreach}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono messaggi inviati oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>

        <div style="width: 500px; margin: 5px; background-color: #fafafa; padding: 10px; box-sizing: border-box; flex-grow: 1">
            {* MESSAGGI INVIATI DA PARENTI *}
            <div style='font-size: 105%; padding-bottom: 10px;'><b>MESSAGGI INVIATI OGGI DA PARENTI</b></div>
            <table width="100%" align="center">
            {if $messaggi_da_parenti|@count > 0}
                <tr>
                    <td><br></td>
                </tr>
                {foreach $messaggi_da_parenti as $key => $mex}
                    <tr width="100%" onclick="apriChiudiPopup('mex_da_parenti_{$key}', '');" style="cursor: pointer;">
                        <td style="border-left: 2px solid #0091ea; background-color: #e5efff;" class="padding_cella_generica">
                            <b>{$mex['name']}</b>
                        </td>
                        <td align="right" style="background-color: #e5efff; border-right: 3px solid #e5efff;" class="padding_cella_generica">
                            da {$mex['from']['fullname']}
                        </td>
                    </tr>
                    <tbody id="mex_da_parenti_{$key}" style="display: none;">
                    <tr width="100%">
                        <td colspan="2" style="border-left: 2px solid #0091ea; border-right: 3px solid #e5efff;"  class="padding_cella_generica">
                            {$mex['content']}
                            {if $mex['files'] > 0}
                                <br>
                                <i>(allegati: {$mex['files']})</i>
                            {/if}
                        </td>
                    </tr>
                    <tr width="100%">
                        <td colspan="2" width="100%" style="border-left: 2px solid #0091ea; border-right: 3px solid #e5efff; background-color: #e5efff;"  class="padding_cella_generica">
                            <div align="center">
                                Destinatari
                            </div>
                            <div style="margin-left: 10px;">
                                {foreach $mex['to'] as $utente}
                                    {if $versione_messenger == 'VERSIONE_2'}
                                        {foreach $utente as $utente_singolo}
                                        • {$utente_singolo['fullname']} <br>
                                        {/foreach}
                                    {else}
                                        • {$utente['fullname']} <br>
                                    {/if}
                                {/foreach}
                            </div>
                        </td>
                    </tr>
                    </tbody>
                    <tr width="100%" style="height: 10px;">
                        <td> </td>
                    </tr>
                {/foreach}
            {else}
                <tr width="100%">
                    <td align="center" width="100%" style="padding-top: 10px; padding-bottom: 3px;">
                        Non ci sono messaggi inviati oggi
                    </td>
                </tr>
            {/if}
            </table>
        </div>

    </div>
</div>

{* Popup dettaglio corso *}
<div class="div_corpo_scheda_generica"
        id="dettaglio_corso"
        style="position: fixed;
                z-index: 3;
                width: 50%;
                min-width: 500px;
                max-height: 95vh;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                display: none;
                overflow-y: auto;"
    >
    <div align="center" style="width: 100%; height: 100%;">
        <table width="95%" align="center">
            <tr>
                <td align="center" style="padding: 15px 10px 20px 10px; font-size: 120%;">
                    <b id="titolo_corso"></b><br>
                </td>
            </tr>
            <tr>
                <td>
                    <h3>{mastercom_label}Studenti{/mastercom_label}</h3>
                    <div>
                        <ul id="lista_studenti"></ul>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h3>{mastercom_label}Docenti{/mastercom_label}</h3>
                    <div>
                        <ul id="lista_docenti"></ul>
                    </div>
                </td>
            </tr>
        </table>
        <div class="padding_cella_generica">
            <br>
            <input type="button"
                    value="Chiudi"
                    class="btn_flat_indaco"
                    onclick="apriChiudiPopup('dettaglio_corso', 'sfondo_oscurato');"
                >
        </div>
    </div>
</div>