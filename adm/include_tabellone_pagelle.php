<?php

$template->assign("db_key", $db_key);

if ($cambia_stato == 'SI') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Operazioni da fare se viene richiesto di cambiare">
    $stringa_chiusura = '';
    $chiusura = estrai_chiusura_scrutini((int) $id_classe);

    switch ($nuovo_stato) {
        case 'APRI_SCRUTINIO':
            foreach ($chiusura as $periodo_scrutinio => $stato_chiusura_scrutinio) {
                if ($periodo_scrutinio == $periodo) {
                    $stato_chiusura_scrutinio = 'NO';
                }
                $stringa_chiusura .= $periodo_scrutinio . '#' . $stato_chiusura_scrutinio . '@';
            }

            $stringa_chiusura = substr($stringa_chiusura, 0, -1);
            $azione = '---';
            $cosa = "APERTO INSERIMENTO DATI ATTIVO ALLA CLASSE CON ID :  " . $id_classe . " PER IL PERIODO " . $periodo;
            inserisci_log_storico((int) $current_user, "GESTIONE_SCRUTINI", $cosa);
            break;

        case 'APRI_CONSIGLIO':
            foreach ($chiusura as $periodo_scrutinio => $stato_chiusura_scrutinio) {
                if ($periodo_scrutinio == $periodo) {
                    $stato_chiusura_scrutinio = 'NO';
                }
                $stringa_chiusura .= $periodo_scrutinio . '#' . $stato_chiusura_scrutinio . '@';
            }

            $stringa_chiusura = substr($stringa_chiusura, 0, -1);
            $azione = 'apri';
            $cosa = "APERTO CONSIGLIO ALLA CLASSE CON ID :  " . $id_classe . " PER IL PERIODO " . $periodo;
            inserisci_log_storico((int) $current_user, "GESTIONE_SCRUTINI", $cosa);

            $abilita_inserimento_automatico_assenze_scrutinio = estrai_parametri_singoli('INSERIMENTO_AUTOMATICO_ASSENZE_SU_APRI_SCRUTINIO');
            $ammessi_tutti = estrai_parametri_singoli('AMMESSI_TUTTI_SU_APRI_SCRUTINIO');
            if ($ammessi_tutti == 'SI') {
                $elenco_studenti = estrai_studenti_classe($id_classe);
                $dati_classe = estrai_classe((int) $id_classe);
                $anno_reale_classe_covid = $dati_classe['anno_reale_classe'];
                if ($dati_classe['tipo_indirizzo'] != '4' && $dati_classe['tipo_indirizzo'] != '6') {
                    //lo faccio solo per le classi non terminali delle superiori
                    if ((int) $anno_reale_classe_covid != 5) {
                        $esito_finale = 2; // ammesso alla classe successiva
                        foreach ($elenco_studenti as $studente) {
                            modifica_esito_finale_studente($studente['id_studente'], $esito_finale, $anno_reale_classe_covid, $current_user, 'NO');
                        }
                    } else {
                        $ammesso_esame_quinta = 'SI'; // ammesso all'esame di stato
                        foreach ($elenco_studenti as $studente) {
                            //modifica_ammissione_quinta_studente($studente['id_studente'], $ammesso_esame_quinta, "", $current_user);
                            $query_ammissione = "UPDATE studenti SET
                                            ammesso_esame_quinta='$ammesso_esame_quinta'
                                          WHERE id_studente=" . $studente['id_studente'];

                            pgsql_query($query_ammissione) or die("Invalid $query_ammissione");
                        }
                    }
                } else {
                    if ($dati_classe['tipo_indirizzo'] == '6') {
                        foreach ($elenco_studenti as $studente) {
                            modifica_ammissione_elementari_studente($studente['id_studente'], 'SI', $anno_reale_classe_covid, $current_user, 'NO');
                        }
                    }
                    if ($dati_classe['tipo_indirizzo'] == '4') {
                        foreach ($elenco_studenti as $studente) {
                            modifica_ammissione_medie_studente($studente['id_studente'], 'SI', $studente['voto_ammissione_medie'], $anno_reale_classe_covid, $current_user, 'SI');
                        }
                    }
                }
            }

            if (($abilita_inserimento_automatico_assenze_scrutinio == "INSERISCI_E_SOVRASCRIVI") OR ( $abilita_inserimento_automatico_assenze_scrutinio == "INSERISCI_SENZA_SOVRASCRIVI")) {
                $periodo_utilizzabile = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO', $id_classe, 'classe');

                // if($dati_classe['tipo_indirizzo'] == '4')   --> $dati classe qui non e' ancora presente
                // {
                //     $periodo_utilizzabile = $periodo_utilizzabile + 20;
                // }

                if ($periodo_utilizzabile == $periodo || ($periodo_utilizzabile + 20) == $periodo) {
                    $data_inizio_voti = 0;
                    $data_fine_voti = 0;
                    $data_inizio_assenze = estrai_parametri_singoli('DATA_INIZIO_CALCOLO_ASSENZE', $id_classe, 'classe');
                    $data_fine_assenze = estrai_parametri_singoli('DATA_FINE_CALCOLO_ASSENZE', $id_classe, 'classe');
                    $inserisci_voto = 'NO';
                    $sovrascrivi_voto = 'NO';
                    $inserisci_assenze = 'SI';
                    if ($abilita_inserimento_automatico_assenze_scrutinio == "INSERISCI_E_SOVRASCRIVI") {
                        $sovrascrivi_assenze = 'SI';
                        $sovrascrivi_monteore = 'SI';
                    } else {
                        $sovrascrivi_assenze = 'NO';
                        $sovrascrivi_monteore = 'SI';
                    }
                    $arrotondamento_ins_aut = estrai_parametri_singoli('ARROTONDAMENTO_VOTI', $id_classe, 'classe');
                    $funzione_abilita_proposte_totali = '1';
                    $consiglio_classe_attivo = 'SI';
                    $forza_inserimento_singola_materia = 'NO';
                    $pagellina_da_usare_per_pre_inserimento = null;
                    $corsi_abbinati = 'NO';
                    $calcola_monteore_template = 'NO';
                    $numero_settimane_template = '0';
                    $durata_periodo_scolastico = '0';
                    $inserisci_assenze_DAD = 'SI';
                    $tipo_inserimento = $form_stato;
                    if ($form_stato == 'amministratore') {
                        $id_professore_per_inserimento = -1;
                    } elseif ($form_stato == 'professore') {
                        $id_professore_per_inserimento = $current_user;
                    }


                    $inserisci_voti = inserisci_proposte_voti_assenze_pagellina_classe(
                            (int) $id_classe, $periodo, $data_inizio_voti, $data_fine_voti, $inserisci_voto, $sovrascrivi_voto, $data_inizio_assenze, $data_fine_assenze, $inserisci_assenze, $sovrascrivi_assenze, $arrotondamento_ins_aut, $tipo_inserimento, $id_professore_per_inserimento, $funzione_abilita_proposte_totali, $current_user, $consiglio_classe_attivo, $forza_inserimento_singola_materia, $pagellina_da_usare_per_pre_inserimento, $sovrascrivi_monteore, $corsi_abbinati, $calcola_monteore_template, $numero_settimane_template, $durata_periodo_scolastico, $inserisci_assenze_DAD);
                }
            }


            break;

        case 'CHIUDI_CONSIGLIO':
            foreach ($chiusura as $periodo_scrutinio => $stato_chiusura_scrutinio) {
                if ($periodo_scrutinio == $periodo) {
                    $stato_chiusura_scrutinio = 'NO';
                }
                $stringa_chiusura .= $periodo_scrutinio . '#' . $stato_chiusura_scrutinio . '@';
            }

            $stringa_chiusura = substr($stringa_chiusura, 0, -1);
            $azione = 'chiudi';
            $cosa = "CHIUSO CONSIGLIO ALLA CLASSE CON ID :  " . $id_classe . " PER IL PERIODO " . $periodo;
            inserisci_log_storico((int) $current_user, "GESTIONE_SCRUTINI", $cosa);
            break;

        case 'CHIUDI_SCRUTINIO':
            foreach ($chiusura as $periodo_scrutinio => $stato_chiusura_scrutinio) {
                if ($periodo_scrutinio == $periodo) {
                    $stato_chiusura_scrutinio = 'SI';
                }
                $stringa_chiusura .= $periodo_scrutinio . '#' . $stato_chiusura_scrutinio . '@';
            }

            $stringa_chiusura = substr($stringa_chiusura, 0, -1);
            $azione = '---';
            $cosa = "CHIUSO SCRUTINIO ALLA CLASSE CON ID :  " . $id_classe . " PER IL PERIODO " . $periodo;
            inserisci_log_storico((int) $current_user, "GESTIONE_SCRUTINI", $cosa);
            break;
    }

    $result = aggiorna_chiusura_scrutini((int) $id_classe, $stringa_chiusura, (int) $current_user);
    cambia_stato_consiglio_di_classe((int) $id_classe, $azione, (int) $current_user);
    //}}} </editor-fold>
}

$template->assign("mostra_anomalie", $mostra_anomalie);

$anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
$anno = explode('/', $anno_scolastico);
$inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
//$primo_settembre = mktime(0, 0, 0, 8, 1, $anno[0]);

$data_inizio_lezioni = estrai_parametri_singoli("DATA_INIZIO_LEZIONI", (int) $id_classe, 'classe');
$data_fine_lezioni = estrai_parametri_singoli("DATA_FINE_LEZIONI", (int) $id_classe, 'classe');

$peso_voti = estrai_parametri_singoli("PESO_VOTI", $id_classe, 'classe');
$primo_periodo_scolastico = estrai_parametri_singoli("PRIMO_PERIODO_SCOLASTICO", $id_classe, 'classe');
$data_inizio_secondo_quadrimestre = estrai_parametri_singoli("DATA_INIZIO_SECONDO_QUADRIMESTRE", $id_classe, 'classe');
$data_inizio_terzo_trimestre = estrai_parametri_singoli("DATA_INIZIO_TERZO_TRIMESTRE", $id_classe, 'classe');

$visualizzazione_recuperi_tabellone = leggi_variabile_sessione((int) $current_user, "VISUALIZZAZIONE_RECUPERI_TABELLONE");
$template->assign("visualizzazione_recuperi_tabellone", $visualizzazione_recuperi_tabellone);

$mat_moduli = estrai_elenco_modelli_documenti_autoselect('moduli');
$mat_verbali = estrai_elenco_modelli_documenti_autoselect('verbali');
$mat_verbali_storici = estrai_elenco_documenti_storici_autoselect('verbali', $data_inizio_lezioni, 0, 0, (int) $id_classe);
if ($form_stato == 'amministratore'){
    $mat_moduli_storici = estrai_elenco_documenti_storici_autoselect('moduli', $data_inizio_lezioni, 0, 0, (int) $id_classe);
} else {
    $mat_moduli_storici = estrai_elenco_documenti_storici_autoselect('moduli', $data_inizio_lezioni, 0, $current_user);
}
$template->assign("mat_moduli", $mat_moduli);
$template->assign("mat_verbali", $mat_verbali);
$template->assign("mat_verbali_storici", $mat_verbali_storici);
$template->assign("mat_moduli_storici", $mat_moduli_storici);

$periodi_scolastici = estrai_parametri_singoli("PERIODI_SCOLASTICI", $id_classe, 'classe');
$template->assign("periodi_scolastici", $periodi_scolastici);

$alto_contrasto = estrai_parametri_singoli("ALTO_CONTRASTO_PAGELLE");
$template->assign("alto_contrasto", $alto_contrasto);

$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

$tipi_recupero = estrai_tipi_recupero();
$template->assign("tipi_recupero", $tipi_recupero);

$data_inizio_calcolo_giudizi_sospesi = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_GIUDIZI_SOSPESI", $id_classe, 'classe');
$template->assign("data_inizio_calcolo_giudizi_sospesi", $data_inizio_calcolo_giudizi_sospesi);

$default_mostra_solo_giudizi_sospesi = estrai_parametri_singoli("DEFAULT_MOSTRA_SOLO_GIUDIZI_SOSPESI");
$template->assign("default_mostra_solo_giudizi_sospesi", $default_mostra_solo_giudizi_sospesi);

$parametro_estendi_info_pagelle = estrai_parametri_singoli("PAGELLE_ESTENDI_VALUTAZIONI");
$template->assign("parametro_estendi_info_pagelle", $parametro_estendi_info_pagelle);

$parametro_estendi_info_pagelle_periodi = explode('#', estrai_parametri_singoli("PAGELLE_ESTENDI_VALUTAZIONI_PERIODI_SCELTI"));

$template->assign("parametro_estendi_info_pagelle_periodi", $parametro_estendi_info_pagelle_periodi);

$parametro_estendi_info_pagelle_curriculum = estrai_parametri_singoli("PAGELLE_ESTENDI_CURRICULUM");
$template->assign("parametro_estendi_info_pagelle_curriculum", $parametro_estendi_info_pagelle_curriculum);

$parametro_estendi_info_pagelle_crediti = estrai_parametri_singoli("PAGELLE_ESTENDI_CREDITI");
$template->assign("parametro_estendi_info_pagelle_crediti", $parametro_estendi_info_pagelle_crediti);

$parametro_estendi_info_pagelle_voti = estrai_parametri_singoli("PAGELLE_ESTENDI_VOTI");
$template->assign("parametro_estendi_info_pagelle_voti", $parametro_estendi_info_pagelle_voti);

$parametro_estendi_giorni_assenza_ritardi_note = estrai_parametri_singoli("ESTENDI_GIORNI_ASSENZA_RITARDI_NOTE");
$template->assign("parametro_estendi_giorni_assenza_ritardi_note", $parametro_estendi_giorni_assenza_ritardi_note);

$giudizio_sospeso_6_in_condotta = estrai_parametri_singoli("ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA");
$template->assign("giudizio_sospeso_6_in_condotta", $giudizio_sospeso_6_in_condotta);

if (strlen($id_classe) > 0) {
    $dati_classe = estrai_classe((int) $id_classe);

    $anno_reale_classe = $dati_classe['anno_reale_classe'];
    $template->assign("dati_classe", $dati_classe);

    $verbalizzatore = verifica_verbalizzatore_classe((int) $id_classe, (int) $current_user);
    $coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);

    $crediti_visibili = (
            ($dati_classe["tipo_indirizzo"] == 0 && in_array($dati_classe["classe"], ['3', '4', '5'])) ||
            ($dati_classe["tipo_indirizzo"] == 1 && in_array($dati_classe["classe"], ['1', '2', '3'])) ||
            ($dati_classe["tipo_indirizzo"] == 2 && in_array($dati_classe["classe"], ['3', '4', '5'])) ||
            ($dati_classe["tipo_indirizzo"] == 3 && in_array($dati_classe["classe"], ['3', '4', '5'])) ||
            ($dati_classe["tipo_indirizzo"] == 5 && in_array($dati_classe["classe"], ['2', '3', '4']))
            );

    if ($parametro_estendi_info_pagelle_crediti == 'SI' && $salva == 'studente' && ($periodo == 9 || $periodo == 29) && $crediti_visibili) {
        $crediti_integrazione_terza = intval($crediti_integrazione_terza);
        $crediti_integrazione_quarta = intval($crediti_integrazione_quarta);

        if (($crediti_integrazione_terza == 0) || ($crediti_integrazione_terza == 1)) {
            modifica_crediti_studente((int) $id_studente, 3, $crediti_integrazione_terza, '', (int) $current_user, 'integrazione');
        }
        if (($crediti_integrazione_quarta == 0) || ($crediti_integrazione_quarta == 1)) {
            modifica_crediti_studente((int) $id_studente, 4, $crediti_integrazione_quarta, '', (int) $current_user, 'integrazione');
        }

        modifica_crediti_studente((int) $id_studente, intval($dati_classe["anno_reale_classe"]), $form_crediti, $form_motivazione_crediti, (int) $current_user);

        if (is_object($logger)) {
            $dati_log = [
                'id_studente'                => $id_studente,
                'crediti'                    => $form_crediti,
                'motivazione_crediti'        => $form_motivazione_crediti,
                'crediti_reintegrati_terza'  => $crediti_integrazione_terza,
                'crediti_reintegrati_quarta' => $crediti_integrazione_quarta
            ];

            $logger->log([
                'Level'   => 0,
                'Type'    => 'INFO',
                'Event'   => __FUNCTION__,
                'Scope'   => __FILE__,
                'Message' => 'Modificati crediti studente',
                'Extra'   => $dati_log
            ]);
        }
    }

    if ($salva == 'crediti') {
        /* {{{ */
        $arr_log_salva['operazione'] = 'salva';

        foreach ($crediti as $key => $credito) {
            // la chiave è in questo formato: 4_1003052, dove 4 indica l'anno e il 1003052 l'id dello studente
            // l'array delle motivazioni ha le stesse chiavi
            $dati = explode("_", $key);
            $anno = $dati[0];
            $id_studente = $dati[1];
            $integrazione = $dati[2];
            if ($integrazione != 'integrazione') {
                $integrazione = '';
            }
            modifica_crediti_studente(intval($id_studente), intval($anno), $credito, $motivazioni_crediti[$key], $current_user, $integrazione);

            $arr_log_salva[$id_studente] = [
                'crediti_' . $anno . '_anno'                   => $credito,
                'motivazioni_crediti_' . $anno . '_anno'       => $motivazioni_crediti[$key],
                'crediti_reintegrati_terza' . $anno . '_anno'  => $credito,
                'crediti_reintegrati_quarta' . $anno . '_anno' => $credito
            ];
        }

        // Log salvataggio pagellina
        if (is_object($logger)) {
            $logger->log([
                'Level'   => 0,
                'Type'    => 'INFO',
                'Event'   => __FUNCTION__,
                'Scope'   => __FILE__,
                'Message' => 'Salvataggio crediti da materia - Periodo: ' . $periodo . ' Materia: ' . $id_materia . ' Classe: ' . $classe,
                'Extra'   => $arr_log_salva
            ]);
        }

        $cosa = 'modificati crediti in modalità test';
        inserisci_log_storico((int) $current_user, "GESTIONE_SCRUTINI", $cosa);
        /* }}} */
    }

    // Sezione per debiti/carenze Trentino: deve attivarsi solo per scrutini finali non delle classi quinte, solo per il dettaglio studente
    //Fra 01/06/2017: tolto il filtro per le quinte, il Da vinci TN ha chiesto di mostrare le carenze anche per le quinte
    if (
            $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && $trentino_abilitato == 'SI'
    ) {
        $parametro_debiti = 'SI';
        $template->assign("parametro_debiti", $parametro_debiti);
        $template->assign("trentino_abilitato", $trentino_abilitato);
    }

    if ($id_studente > 0 && $torna_a == 'studente') {
        $elenco_studenti = [0 => estrai_dati_studente((int) $id_studente)];
        $mat_campi_liberi = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente);
        $mat_campi_liberi_figli = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente, 'figli');

        //{{{ <editor-fold defaultstate="collapsed" desc="Divisione campi liberi normali e padre-figlio">
        $mat_campi_liberi_base = [];
        $mat_campi_liberi_parenti = [];

        foreach ($mat_campi_liberi as $campo_libero) {
            $trovato = false;
            $trovato_figlio = false;
            foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
                if ($campo_libero_figlio['id_padre'] == $campo_libero['id_campo_libero']) {
                    $mat_campi_liberi_parenti[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
                    $trovato = true;
                }
                if ($campo_libero_figlio['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                    $trovato_figlio = true;
                }
            }
            if (!$trovato && !$trovato_figlio) {
                $mat_campi_liberi_base[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
            }
        }

        foreach ($mat_campi_liberi_parenti as $key_parenti => $campo_libero_parente) {
            foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
                if ($campo_libero_figlio['id_padre'] == $campo_libero_parente['id_campo_libero']) {
                    $mat_campi_liberi_parenti[$key_parenti]['figli'][] = $campo_libero_figlio;
                }
            }
        }
        //}}} </editor-fold>

        $template->assign("mat_campi_liberi", $mat_campi_liberi_base);
        $template->assign("mat_campi_liberi_parenti", $mat_campi_liberi_parenti);

        if ($dati_classe["tipo_indirizzo"] != '4') {
            $dati_competenze['cmp_val_ita'] = $elenco_studenti[0]['cmp_sup_val_ita'];
            $dati_competenze['cmp_txt_ita'] = $elenco_studenti[0]['cmp_sup_txt_ita'];
            $dati_competenze['cmp_val_ing'] = $elenco_studenti[0]['cmp_sup_val_ing'];
            $dati_competenze['cmp_txt_ing'] = $elenco_studenti[0]['cmp_sup_txt_ing'];
            $dati_competenze['cmp_val_altri'] = $elenco_studenti[0]['cmp_sup_val_altri'];
            $dati_competenze['cmp_txt_altri'] = $elenco_studenti[0]['cmp_sup_txt_altri'];
            $dati_competenze['cmp_val_mat'] = $elenco_studenti[0]['cmp_sup_val_mat'];
            $dati_competenze['cmp_txt_mat'] = $elenco_studenti[0]['cmp_sup_txt_mat'];
            $dati_competenze['cmp_val_sci_tec'] = $elenco_studenti[0]['cmp_sup_val_sci_tec'];
            $dati_competenze['cmp_txt_sci_tec'] = $elenco_studenti[0]['cmp_sup_txt_sci_tec'];
            $dati_competenze['cmp_val_sto_soc'] = $elenco_studenti[0]['cmp_sup_val_sto_soc'];
            $dati_competenze['cmp_txt_sto_soc'] = $elenco_studenti[0]['cmp_sup_txt_sto_soc'];
        } else {
            $dati_competenze['cmp_val_ita'] = $elenco_studenti[0]['cmp_med_val_ita'];
            $dati_competenze['cmp_txt_ita'] = $elenco_studenti[0]['cmp_med_txt_ita'];
            $dati_competenze['cmp_val_ing'] = $elenco_studenti[0]['cmp_med_val_ing'];
            $dati_competenze['cmp_txt_ing'] = $elenco_studenti[0]['cmp_med_txt_ing'];
            $dati_competenze['cmp_val_l2'] = $elenco_studenti[0]['cmp_med_val_l2'];
            $dati_competenze['cmp_txt_l2'] = $elenco_studenti[0]['cmp_med_txt_l2'];
            $dati_competenze['cmp_val_l3'] = $elenco_studenti[0]['cmp_med_val_l3'];
            $dati_competenze['cmp_txt_l3'] = $elenco_studenti[0]['cmp_med_txt_l3'];
            $dati_competenze['cmp_val_altri'] = $elenco_studenti[0]['cmp_med_val_altri'];
            $dati_competenze['cmp_txt_altri'] = $elenco_studenti[0]['cmp_med_txt_altri'];
            $dati_competenze['cmp_val_arte'] = $elenco_studenti[0]['cmp_med_val_arte'];
            $dati_competenze['cmp_txt_arte'] = $elenco_studenti[0]['cmp_med_txt_arte'];
            $dati_competenze['cmp_val_mus'] = $elenco_studenti[0]['cmp_med_val_mus'];
            $dati_competenze['cmp_txt_mus'] = $elenco_studenti[0]['cmp_med_txt_mus'];
            $dati_competenze['cmp_val_mot'] = $elenco_studenti[0]['cmp_med_val_mot'];
            $dati_competenze['cmp_txt_mot'] = $elenco_studenti[0]['cmp_med_txt_mot'];
            $dati_competenze['cmp_val_mat'] = $elenco_studenti[0]['cmp_med_val_mat'];
            $dati_competenze['cmp_txt_mat'] = $elenco_studenti[0]['cmp_med_txt_mat'];
            $dati_competenze['cmp_val_sci_tec'] = $elenco_studenti[0]['cmp_med_val_sci_tec'];
            $dati_competenze['cmp_txt_sci_tec'] = $elenco_studenti[0]['cmp_med_txt_sci_tec'];
            $dati_competenze['cmp_val_sto_soc'] = $elenco_studenti[0]['cmp_med_val_sto_soc'];
            $dati_competenze['cmp_txt_sto_soc'] = $elenco_studenti[0]['cmp_med_txt_sto_soc'];
        }

        $dati_tmp_portfolio = estrai_portfolio_studente((int) $id_studente);
        $template->assign("dati_competenze", $dati_competenze);

        // Sezione per debiti/carenze Trentino
        if ($parametro_debiti == 'SI') {
            $dati_debiti = estrai_debiti_studente((int) $id_studente);
            $template->assign("dati_debiti", $dati_debiti);
        }

        // Elenco dei valori possibili per il consiglio orientativo del trentino
        $valori_consiglio_orientativo_trentino = estrai_valori_consiglio_orientativo_trentino();
        $template->assign("valori_consiglio_orientativo_trentino", $valori_consiglio_orientativo_trentino);

        if ($parametro_estendi_info_pagelle_voti == 'SI' or true) {
            //estrae tutti i voti assegnati allo studente durante l'anno, divisi per materia e per mese.
            $elenco_voti_anno = estrai_voti_studente_materie((int) $id_studente, $data_inizio_lezioni, $data_fine_lezioni, 0, "TUTTE_CON_CORSI", $current_key);
            foreach ($elenco_voti_anno as $id_materia_voto_corrente => $voti_materia_anno) {
                $nome_materia = $voti_materia_anno['ignora']['descrizione_materia'];

                $totale_voti['tot'] = 0;
                $totale_voti['primo'] = 0;
                $totale_voti['secondo'] = 0;
                $totale_voti['terzo'] = 0;
                $numero_tot_voti['tot'] = 0;
                $numero_tot_voti['primo'] = 0;
                $numero_tot_voti['secondo'] = 0;
                $numero_tot_voti['terzo'] = 0;
                foreach ($voti_materia_anno as $id_voto_anno => $singolo_voto) {
                    if (is_numeric($id_voto_anno) and $singolo_voto['voto'] != 'A') {
                        $mese_voto = intval(date('m', $singolo_voto['data']));

                        switch ($singolo_voto['tipo']) {
                            case 0:
                                $tipo_voto = '(S)';
                                break;
                            case 1:
                                $tipo_voto = '(O)';
                                break;
                            case 2:
                                $tipo_voto = '(P)';
                                break;
                            default:
                                $tipo_voto = '';
                                break;
                        }
                        $mat_voti_anno[$nome_materia][$mese_voto][] = array('valore' => $singolo_voto['codice_voto'], 'data' => date('d', $singolo_voto['data']), 'tipo' => $tipo_voto);
                        if (($singolo_voto['peso'] != '') AND ( $peso_voti == 'SI')) {
                            $peso = $singolo_voto['peso'];
                        } else {
                            $peso = 1;
                        }
                        switch ($periodi_scolastici) {
                            case 'trimestri':
                                $totale_voti['tot'] += ($singolo_voto['voto'] * $peso);
                                $numero_tot_voti['tot'] += $peso;
                                if ($singolo_voto['data'] < $data_inizio_secondo_quadrimestre) {
                                    $totale_voti['primo'] += ($singolo_voto['voto'] * $peso);
                                    $numero_tot_voti['primo'] += $peso;
                                } elseif ($singolo_voto['data'] < $data_inizio_terzo_trimestre) {
                                    $totale_voti['secondo'] += ($singolo_voto['voto'] * $peso);
                                    $numero_tot_voti['secondo'] += $peso;
                                } else {
                                    $totale_voti['terzo'] += ($singolo_voto['voto'] * $peso);
                                    $numero_tot_voti['terzo'] += $peso;
                                }
                                break;
                            case 'quadrimestri':
                            default:
                                $totale_voti['tot'] += ($singolo_voto['voto'] * $peso);
                                $numero_tot_voti['tot'] += $peso;
                                if ($singolo_voto['data'] < $data_inizio_secondo_quadrimestre) {
                                    $totale_voti['primo'] += ($singolo_voto['voto'] * $peso);
                                    $numero_tot_voti['primo'] += $peso;
                                } else {
                                    $totale_voti['secondo'] += ($singolo_voto['voto'] * $peso);
                                    $numero_tot_voti['secondo'] += $peso;
                                }
                                break;
                        }
                    }
                    //da fare per tutti i periodi
                    if ($numero_tot_voti['tot'] > 0) {
                        $mat_voti_anno[$nome_materia][7][0] = array('valore' => number_format(round($totale_voti['tot'] / $numero_tot_voti['tot'], 2), 2, '.', ''), 'data' => '', 'tipo' => 'T=');
                    } else {
                        $mat_voti_anno[$nome_materia][7][0] = array('valore' => '', 'data' => '', 'tipo' => '');
                    }
                    if ($numero_tot_voti['primo'] > 0) {
                        $mat_voti_anno[$nome_materia][7][1] = array('valore' => number_format(round($totale_voti['primo'] / $numero_tot_voti['primo'], 2), 2, '.', ''), 'data' => '', 'tipo' => '1°=');
                    } else {
                        $mat_voti_anno[$nome_materia][7][1] = array('valore' => '', 'data' => '', 'tipo' => '');
                    }
                    if ($numero_tot_voti['secondo'] > 0) {
                        $mat_voti_anno[$nome_materia][7][2] = array('valore' => number_format(round($totale_voti['secondo'] / $numero_tot_voti['secondo'], 2), 2, '.', ''), 'data' => '', 'tipo' => '2°=');
                    } else {
                        $mat_voti_anno[$nome_materia][7][2] = array('valore' => '', 'data' => '', 'tipo' => '');
                    }
                    //da fare solo per trimestri
                    if ($periodi_scolastici == 'trimestri') {
                        if ($numero_tot_voti['terzo'] > 0) {
                            $mat_voti_anno[$nome_materia][7][3] = array('valore' => number_format(round($totale_voti['terzo'] / $numero_tot_voti['terzo'], 2), 2, '.', ''), 'data' => '', 'tipo' => '3°=');
                        } else {
                            $mat_voti_anno[$nome_materia][7][3] = array('valore' => '', 'data' => '', 'tipo' => '');
                        }
                    }
                }
            }
            // Spostato il template assign di "voti_anno" dopo l'estrazione delle materie a cui accede l'utente
        }
    } else {
        //questa prima parte mi serve per recuperare gli studenti selezionati
        $elenco_studenti_temp = estrai_studenti_classe_con_abbinamenti((int) $id_classe);
        $elenco_studenti = $elenco_studenti_temp['elenco_studenti'];
    }

    // Sezione per debiti/carenze Trentino
    if ($parametro_debiti == 'SI' && $salva == 'studente') {
        foreach ($elimina_debito as $id_debito) {
            elimina_debito($id_debito, $current_user);
        }

        foreach ($recupero_debito as $id_debito) {
            $giorno_select = ${'data_recupero_' . $id_debito . "_Day"};
            $mese_select = ${'data_recupero_' . $id_debito . "_Month"};
            $anno_select = ${'data_recupero_' . $id_debito . "_Year"};
            $data_selezionata = mktime(1, 1, 1, intval($mese_select), intval($giorno_select), intval($anno_select));

            modifica_debito((int) $id_debito, 'SI', ${"tipo_appello_debito_" . $id_debito}, $data_selezionata, (int) $current_user);
        }

        for ($cont = 1; $cont < 6; $cont++) {
            if (strlen(${'descrizione_materia_debito_new_' . $cont}) > 0) {
                $dati_materia_debito = explode('##@@##', ${'descrizione_materia_debito_new_' . $cont});
                inserisci_debito((int) $dati_materia_debito[0], $dati_materia_debito[1], (int) $id_studente, ${'tipo_debito_new_' . $cont}, 'NO', $anno_inizio . '/' . $anno_fine, (int) $current_user, ${'tipo_carenza_new_' . $cont});
            }
        }
    }
    //fine prima parte
}

$template->assign("torna_a", $torna_a);

$parametro_competenze_sup = estrai_parametri_singoli("COMPETENZE_SUPERIORI_ABILITATE");
$parametro_competenze_med = estrai_parametri_singoli("COMPETENZE_MEDIE_ABILITATE");
$parametro_competenze_ele = estrai_parametri_singoli("COMPETENZE_ELEMENTARI_ABILITATE");

if (
        (
        $parametro_competenze_sup == 'SI' && $dati_classe["tipo_indirizzo"] != 4 && $dati_classe["tipo_indirizzo"] != 6 && $dati_classe["tipo_indirizzo"] != 7 && $dati_classe["tipo_indirizzo"] != 8 && $periodo == 9 && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        ) ||
        (
        $parametro_competenze_med == 'SI' && $dati_classe["tipo_indirizzo"] == 4 && $anno_reale_classe == 3 && ($periodo == 26 || $periodo == 29) && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        ) ||
        (
        $parametro_competenze_ele == 'SI' && $dati_classe["tipo_indirizzo"] == 6 && $anno_reale_classe == 5 && $periodo == 9 && ($form_stato == 'amministratore' || $coordinatore == 'SI')
        )
) {
    $parametro_competenze = 'SI';
} else {
    $parametro_competenze = 'NO';
}

$template->assign("parametro_competenze", $parametro_competenze);

$parametro_portfolio_mat = estrai_parametri_singoli("PORTFOLIO_ABILITATO");

$array_classi_portfolio = [];

if (strlen($parametro_portfolio_mat) > 0) {
    $classi_portfolio = explode('@', $parametro_portfolio_mat);

    foreach ($classi_portfolio as $classe_portfolio) {
        $dettaglio_classe_portfolio = explode('#', $classe_portfolio);
        $array_classi_portfolio[$dettaglio_classe_portfolio[0]] = $dettaglio_classe_portfolio[1];
    }
}

if (
        count($array_classi_portfolio) > 0 && $dati_classe["tipo_indirizzo"] != '4' && $dati_classe["tipo_indirizzo"] != '6' && $dati_classe["tipo_indirizzo"] != '7' && $dati_classe["tipo_indirizzo"] != '8' && $dati_classe["tipo_indirizzo"] != '8' && $periodo == '9' && ($form_stato == 'amministratore' || $coordinatore == 'SI')
) {
    $dati_portfolio = [];

    foreach ($array_classi_portfolio as $anno_classe_portfolio => $numero_righe_portfolio) {
        if ($anno_classe_portfolio == $anno_reale_classe) {
            $parametro_portfolio = 'SI';
            $numero_righe_portfolio_definitivo = $numero_righe_portfolio;
            $tabelle_portfolio = estrai_tabelle_portfolio();

            $as_attuale = explode("/", estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));
            $as_inizio = 2006;

            for ($a = $as_attuale[1]; $a > $as_inizio; $a--) {
                $tabelle_portfolio['anno_scolastico'][] = ($a - 1) . "/" . ($a);
            }

            $numero_portfolio = count($dati_tmp_portfolio);

            for ($cont_port = 0; $cont_port < $numero_righe_portfolio; $cont_port++) {
                if ($cont_port < $numero_portfolio) {
                    $dati_portfolio[$cont_port] = $dati_tmp_portfolio[$cont_port];
                } else {
                    $dati_portfolio[$cont_port]['descrizione'] = '';
                }
            }
        }
    }
} else {
    $parametro_portfolio = 'NO';
}

$template->assign("parametro_portfolio", $parametro_portfolio);
$template->assign("dati_portfolio", $dati_portfolio);
$template->assign("tabelle_portfolio", $tabelle_portfolio);

if ($cambia_stato == 'SI' && $nuovo_stato == 'CHIUDI_SCRUTINIO') {
    foreach ($elenco_studenti as $studente) {
        aggiorna_curriculum_studente((int) $studente['id_studente'], 'chiusura_scrutini', (int) $current_user);
    }
}

if ($cambia_stato == 'SI' && $nuovo_stato == 'CHIUDI_SCRUTINIO') {

}


//{{{ <editor-fold defaultstate="collapsed" desc="Scala voti">
$scala_voti = estrai_parametri_singoli('SCALA_VOTI');
$dati_scala_voti = estrai_scala_voti($scala_voti, 'estesa');

$voto_minimo = $dati_scala_voti['voto_minimo'];
$voto_massimo = $dati_scala_voti['voto_massimo'];
$voto_minimo_suff = $dati_scala_voti['voto_minimo_suff'];
$salto_6 = $dati_scala_voti['salto_6'];
$salto_7 = $dati_scala_voti['salto_7'];
$salto_8 = $dati_scala_voti['salto_8'];
$salto_10 = $dati_scala_voti['salto_10'];
$arrotondamento = $dati_scala_voti['arrotondamento'];
$aggiustamento_media = $dati_scala_voti['aggiustamento_media'];
$aggiustamento_ammissione = $dati_scala_voti['aggiustamento_ammissione'];

$parametri_voto = [];
$parametri_voto['voto_minimo'] = $voto_minimo;
$parametri_voto['voto_massimo'] = $voto_massimo;
$parametri_voto['voto_minimo_suff'] = $voto_minimo_suff;
$parametri_voto['salto_6'] = $salto_6;
$parametri_voto['salto_7'] = $salto_7;
$parametri_voto['salto_8'] = $salto_8;
$parametri_voto['salto_10'] = $salto_10;
$parametri_voto['arrotondamento'] = $arrotondamento;
$parametri_voto['aggiustamento_media'] = $aggiustamento_media;
$parametri_voto['aggiustamento_ammissione'] = $aggiustamento_ammissione;

$template->assign("parametri_voto", $parametri_voto);
//}}} </editor-fold>

$campi_liberi_visualizzazione = estrai_parametri_singoli('CAMPI_LIBERI_VISUALIZZAZIONE');
$template->assign("campi_liberi_visualizzazione", $campi_liberi_visualizzazione);
$template->assign("screen_width", $screen_width);
$template->assign("classi_ind", $elenco_classi_ind);
$template->assign("indirizzo", $indirizzo);
$template->assign("id_indirizzo", $id_indirizzo);
$template->assign("valore_classe", $valore_classe);
$template->assign("classe", $classe);
$template->assign("id_classe", $id_classe);
$template->assign("periodo", $periodo);
$template->assign("codice_descrizione", $codice_descrizione);
$template->assign("visualizza_assenze", $visualizza_assenze);
$template->assign("tipo_visualizzazione", $tipo_visualizzazione_voti);

$multi_classe_pagelle = estrai_parametri_singoli("MULTI_CLASSE_PAGELLE", $id_classe, 'classe');

if ($form_stato == 'amministratore' || ($coordinatore == 'SI' && $dati_classe['consiglio_classe_attivo'] == 'SI')) {
    $stato = 'amministratore';
} else {
    $stato = 'professore';
}

if ($salva == 'materia') {
    foreach ($elenco_studenti as $studente) {
        if (${'modificato_' . $studente['id_studente']} == 'SI') {
            //{{{ <editor-fold defaultstate="collapsed">
            if (${'id_voto_pagellina_' . $studente['id_studente']} > 0) {
                modifica_voto_pagellina(
                        (int) ${'id_voto_pagellina_' . $studente['id_studente']}, ${'voto_pagellina_' . $studente['id_studente']}, '', intval(${'ore_assenza_' . $studente['id_studente']}), time(), 'SI', 'SI', 'SI', ${'voto_scritto_pagella_' . $studente['id_studente']}, ${'voto_orale_pagella_' . $studente['id_studente']}, ${'voto_pratico_pagella_' . $studente['id_studente']}, (int) $current_user, ${'tipo_recupero_' . $studente['id_studente']}, ${'esito_recupero_' . $studente['id_studente']}, ${'giudizio_analitico_' . $studente['id_studente']}, ${'modificato_da_amministratore_' . $studente['id_studente']}, $stato, intval(${'monteore_totale_' . $studente['id_studente']}), 'SI'
                );
                $arr_log_salva[$studente['id_studente']]['operazione'] = 'modifica';
                $id_voto_pagellina = ${'id_voto_pagellina_' . $studente['id_studente']};
            } else {
                $id_pagellina = inserisci_pagellina($periodo, $studente['id_studente'], $current_user);
                $id_voto_pagellina = inserisci_voto_pagellina(
                        ${'voto_pagellina_' . $studente['id_studente']}, '', (int) $id_materia, (int) $id_pagellina, intval(${'ore_assenza_' . $studente['id_studente']}), time(), ${'voto_scritto_pagella_' . $studente['id_studente']}, ${'voto_orale_pagella_' . $studente['id_studente']}, ${'voto_pratico_pagella_' . $studente['id_studente']}, (int) $current_user, ${'tipo_recupero_' . $studente['id_studente']}, ${'esito_recupero_' . $studente['id_studente']}, ${'giudizio_analitico_' . $studente['id_studente']}, $stato, intval(${'monteore_totale_' . $studente['id_studente']}), 'SI'
                );
                $arr_log_salva[$studente['id_studente']]['operazione'] = 'inserimento';
            }

            $arr_log_salva[$studente['id_studente']] = [
                'id_pagellina'       => $id_pagellina,
                'id_voto_pagellina'  => $id_voto_pagellina,
                'studente'           => $studente['id_studente'],
                'voto_pagellina'     => ${'voto_pagellina_' . $studente['id_studente']},
                'ore_assenza'        => intval(${'ore_assenza_' . $studente['id_studente']}),
                'monteore_totale'    => intval(${'monteore_totale_' . $studente['id_studente']}),
                'voto_scritto'       => ${'voto_scritto_pagella_' . $studente['id_studente']},
                'voto_orale'         => ${'voto_orale_pagella_' . $studente['id_studente']},
                'voto_pratico'       => ${'voto_pratico_pagella_' . $studente['id_studente']},
                'tipo_recupero'      => ${'tipo_recupero_' . $studente['id_studente']},
                'esito_recupero'     => ${'esito_recupero_' . $studente['id_studente']},
                'giudizio_analitico' => ${'giudizio_analitico_' . $studente['id_studente']},
            ];

            $elenco_campi_liberi = estrai_codici_campi_liberi($periodo);

            if (is_array($elenco_campi_liberi)) {
                foreach ($elenco_campi_liberi as $campo_libero) {
                    switch ($campo_libero['tipo_valore']) {
                        case 'PRECOMPILATO':
                            $valore_campo = ${'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']};
                            break;
                        case "PRECOMPILATO_TESTO":
                            $valore_campo = [
                                'id_valore_precomp' => ${'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']},
                                'valore_testuale'   => ${'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']}
                            ];
                            break;
                        case "NUMERO":
                            $valore_campo = ${'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']};
                            break;
                        default:
                            $valore_campo = ${'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']};
                            break;
                    }
                    inserisci_valore_campo_libero(
                            ${'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente']}, (int) $id_voto_pagellina, $campo_libero['id_campo_libero'], $valore_campo, $campo_libero['tipo_valore'], $current_user
                    );
                }
            }
            //}}} </editor-fold>
        }
    }

    // Log salvataggio pagellina
    if (is_object($logger)) {
        $logger->log([
            'Level'   => 0,
            'Type'    => 'INFO',
            'Event'   => __FUNCTION__,
            'Scope'   => __FILE__,
            'Message' => 'Salvataggio pagelle da materia - Periodo: ' . $periodo . ' Materia: ' . $id_materia,
            'Extra'   => $arr_log_salva
        ]);
    }
}

if ($id_materia > 0 && $torna_a == 'materia') {
    $elenco_materie = estrai_materie_multi_classe((int) $id_classe, (int) $id_materia);
    $mat_campi_liberi = estrai_elenco_campi_liberi_materia((int) $id_classe, $periodo, (int) $id_materia);
    $mat_campi_liberi_figli = estrai_elenco_campi_liberi_materia((int) $id_classe, $periodo, (int) $id_materia, 'figli');

    //{{{ <editor-fold defaultstate="collapsed" desc="Divisione campi liberi normali e padre-figlio">
    $mat_campi_liberi_base = [];
    $mat_campi_liberi_parenti = [];

    foreach ($mat_campi_liberi as $campo_libero) {
        $trovato = false;
        $trovato_figlio = false;
        foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
            if ($campo_libero_figlio['id_padre'] == $campo_libero['id_campo_libero']) {
                $mat_campi_liberi_parenti[$campo_libero['id_campo_libero']] = $campo_libero;
                $trovato = true;
            }
            if ($campo_libero_figlio['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                $trovato_figlio = true;
            }
        }
        if (!$trovato && !$trovato_figlio) {
            $mat_campi_liberi_base[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
        }
    }

    foreach ($mat_campi_liberi_parenti as $key_parenti => $campo_libero_parente) {
        foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
            if ($campo_libero_figlio['id_padre'] == $campo_libero_parente['id_campo_libero']) {
                $mat_campi_liberi_parenti[$key_parenti]['figli'][] = $campo_libero_figlio;
            }
        }
    }
    //}}} </editor-fold>

    $template->assign("mat_campi_liberi", $mat_campi_liberi_base);
    $template->assign("mat_campi_liberi_parenti", $mat_campi_liberi_parenti);
} else {
    if ($form_stato == 'amministratore') {
        $elenco_materie = [];

        if ($multi_classe_pagelle == "SI") {
            if ($id_studente > 0 && $torna_a == 'studente') {
                $elenco_materie = estrai_materie_multi_classe_studente((int) $id_studente);
            } else {
                $elenco_materie = estrai_materie_multi_classe((int) $id_classe);
            }
        } else {
            $elenco_materie = estrai_materie_classe((int) $id_classe);
        }
    } elseif ($form_stato == 'professore') {
        if ($coordinatore == "NO") {
            $liv_ab = estrai_livello_abilitazione_funzioni_professore((int) $id_professore);

            foreach ($liv_ab as $liv) {
                if ($liv['codice_funzione'] == 'VISUALIZZA TABELLONE SCRUTINI COMPLETO') {
                    if ($liv['livello_abilitazione'] == 1) {
                        $elenco_materie = [];

                        if ($multi_classe_pagelle == "SI") {
                            if ($id_studente > 0 && $torna_a == 'studente') {
                                $elenco_materie = estrai_materie_multi_classe_studente((int) $id_studente);
                            } else {
                                $elenco_materie = estrai_materie_multi_classe((int) $id_classe);
                            }
                        } else {
                            $elenco_materie = estrai_materie_classe((int) $id_classe);
                        }
                    } else {
                        $elenco_materie = [];

                        if ($multi_classe_pagelle == "SI") {
                            if ($id_studente > 0 && $torna_a == 'studente') {
                                $elenco_materie = estrai_materie_multi_classe_studente_del_professore((int) $id_studente, (int) $current_user);
                            } else {
                                $elenco_materie = estrai_materie_multi_classe_del_professore((int) $id_classe, (int) $current_user);
                            }
                        } else {
                            $elenco_materie = estrai_materie_classe_del_professore((int) $id_classe, (int) $current_user);
                        }
                    }
                }
            }
        } else {
            $elenco_materie = [];

            if ($multi_classe_pagelle == "SI") {
                if ($id_studente > 0 && $torna_a == 'studente') {
                    $elenco_materie = estrai_materie_multi_classe_studente((int) $id_studente);
                } else {
                    $elenco_materie = estrai_materie_multi_classe((int) $id_classe);
                }
            } else {
                $elenco_materie = estrai_materie_classe((int) $id_classe);
            }
        }
    }
}

//{{{ <editor-fold defaultstate="collapsed" desc="Filtro le materie di cui visualizzare i voti dell'anno se non sei amministratore o coordinatore">
$mat_voti_anno_filtrati = [];
foreach ($elenco_materie as $singola_materia) {
    foreach ($mat_voti_anno as $nome => $voti_materia) {
        if (encode($descrizione) == encode($name)) {
            $mat_voti_anno_filtrati[$nome] = $voti_materia;
        }
    }
}
//}}} </editor-fold>

$template->assign("voti_anno", $mat_voti_anno_filtrati);


//{{{ <editor-fold defaultstate="collapsed" desc="Salvataggio dati modifica voti studente">

$arr_log_salva = [];

if ($salva == 'studente') {
    foreach ($elenco_materie as $materia) {
        if (${'modificato_' . $materia['id_materia']} == 'SI') {
            //{{{ <editor-fold defaultstate="collapsed">
            if (${'id_voto_pagellina_' . $materia['id_materia']} > 0) {
                modifica_voto_pagellina(
                        (int) ${'id_voto_pagellina_' . $materia['id_materia']}, ${'voto_pagellina_' . $materia['id_materia']}, '', intval(${'ore_assenza_' . $materia['id_materia']}), time(), 'SI', 'SI', 'SI', ${'voto_scritto_pagella_' . $materia['id_materia']}, ${'voto_orale_pagella_' . $materia['id_materia']}, ${'voto_pratico_pagella_' . $materia['id_materia']}, $current_user, ${'tipo_recupero_' . $materia['id_materia']}, ${'esito_recupero_' . $materia['id_materia']}, ${'giudizio_analitico_' . $materia['id_materia']}, ${'modificato_da_amministratore_' . $materia['id_materia']}, $stato, intval(${'monteore_totale_' . $materia['id_materia']}), 'SI'
                );
                $arr_log_salva[$materia['id_materia']]['operazione'] = 'modifica';

                $id_voto_pagellina = ${'id_voto_pagellina_' . $materia['id_materia']};
            } else {
                if (!($id_pagellina > 0)) {
                    $id_pagellina = inserisci_pagellina($periodo, (int) $id_studente, (int) $current_user);
                }
                $id_voto_pagellina = inserisci_voto_pagellina(
                        ${'voto_pagellina_' . $materia['id_materia']}, '', (int) $materia['id_materia'], (int) $id_pagellina, intval(${'ore_assenza_' . $materia['id_materia']}), time(), ${'voto_scritto_pagella_' . $materia['id_materia']}, ${'voto_orale_pagella_' . $materia['id_materia']}, ${'voto_pratico_pagella_' . $materia['id_materia']}, (int) $current_user, ${'tipo_recupero_' . $materia['id_materia']}, ${'esito_recupero_' . $materia['id_materia']}, ${'giudizio_analitico_' . $materia['id_materia']}, $stato, intval(${'monteore_totale_' . $materia['id_materia']}), 'SI'
                );
                $arr_log_salva[$materia['id_materia']]['operazione'] = 'inserimento';
            }

            $arr_log_salva[$materia['id_materia']] = [
                'id_pagellina'       => $id_pagellina,
                'id_voto_pagellina'  => $id_voto_pagellina,
                'materia'            => $materia['id_materia'],
                'voto_pagellina'     => ${'voto_pagellina_' . $materia['id_materia']},
                'ore_assenza'        => intval(${'ore_assenza_' . $materia['id_materia']}),
                'monteore_totale'    => intval(${'monteore_totale_' . $materia['id_materia']}),
                'voto_scritto'       => ${'voto_scritto_pagella_' . $materia['id_materia']},
                'voto_orale'         => ${'voto_orale_pagella_' . $materia['id_materia']},
                'voto_pratico'       => ${'voto_pratico_pagella_' . $materia['id_materia']},
                'tipo_recupero'      => ${'tipo_recupero_' . $materia['id_materia']},
                'esito_recupero'     => ${'esito_recupero_' . $materia['id_materia']},
                'giudizio_analitico' => ${'giudizio_analitico_' . $materia['id_materia']},
            ];

            $elenco_campi_liberi = estrai_codici_campi_liberi($periodo);

            if (is_array($elenco_campi_liberi)) {
                foreach ($elenco_campi_liberi as $campo_libero) {
                    switch ($campo_libero['tipo_valore']) {
                        case 'PRECOMPILATO':
                            $valore_campo = ${'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']};
                            break;
                        case "PRECOMPILATO_TESTO":
                            $valore_campo = [
                                'id_valore_precomp' => ${'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']},
                                'valore_testuale'   => encode(${'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']})
                            ];
                            break;
                        case "NUMERO":
                            $valore_campo = ${'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']};
                            break;
                        case "RAGGRUPPAMENTO":
                        default:
                            $valore_campo = ${'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']};
                            break;
                    }

                    inserisci_valore_campo_libero(
                            ${'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $materia['id_materia']}, (int) $id_voto_pagellina, $campo_libero['id_campo_libero'], $valore_campo, $campo_libero['tipo_valore'], (int) $current_user
                    );
                }
            }
            //}}} </editor-fold>
        }
    }

    // Log salvataggio pagellina
    if (is_object($logger)) {
        $logger->log([
            'Level'   => 0,
            'Type'    => 'INFO',
            'Event'   => __FUNCTION__,
            'Scope'   => __FILE__,
            'Message' => 'Salvataggio pagelle da studente - Pagellina: ' . $id_pagellina . ' Periodo: ' . $periodo . ' Studente: ' . $id_studente,
            'Extra'   => $arr_log_salva
        ]);
    }

    if ($parametro_competenze == 'SI') {
        //{{{ <editor-fold defaultstate="collapsed" desc="salvataggio vecchie compentenze">
        $dati_competenze = [];

        if ($dati_classe["tipo_indirizzo"] == '4') {
            $dati_competenze['cmp_med_val_ita'] = $valore_linguaggi_italiano;
            $dati_competenze['cmp_med_txt_ita'] = $testo_linguaggi_italiano;
            $dati_competenze['cmp_med_val_ing'] = $valore_linguaggi_inglese;
            $dati_competenze['cmp_med_txt_ing'] = $testo_linguaggi_inglese;
            $dati_competenze['cmp_med_val_l2'] = $valore_linguaggi_lingua2;
            $dati_competenze['cmp_med_txt_l2'] = $testo_linguaggi_lingua2;
            $dati_competenze['cmp_med_val_l3'] = $valore_linguaggi_lingua3;
            $dati_competenze['cmp_med_txt_l3'] = $testo_linguaggi_lingua3;
            $dati_competenze['cmp_med_val_altri'] = $valore_linguaggi_altri;
            $dati_competenze['cmp_med_txt_altri'] = $testo_linguaggi_altri;
            $dati_competenze['cmp_med_val_arte'] = $valore_linguaggi_altri_arte;
            $dati_competenze['cmp_med_txt_arte'] = $testo_linguaggi_altri_arte;
            $dati_competenze['cmp_med_val_mus'] = $valore_linguaggi_altri_musica;
            $dati_competenze['cmp_med_txt_mus'] = $testo_linguaggi_altri_musica;
            $dati_competenze['cmp_med_val_mot'] = $valore_linguaggi_altri_motoria;
            $dati_competenze['cmp_med_txt_mot'] = $testo_linguaggi_altri_motoria;
            $dati_competenze['cmp_med_val_mat'] = $valore_matematico;
            $dati_competenze['cmp_med_txt_mat'] = $testo_matematico;
            $dati_competenze['cmp_med_val_sci_tec'] = $valore_scientifico_tecno;
            $dati_competenze['cmp_med_txt_sci_tec'] = $testo_scientifico_tecno;
            $dati_competenze['cmp_med_val_sto_soc'] = $valore_storico_sociale;
            $dati_competenze['cmp_med_txt_sto_soc'] = $testo_storico_sociale;
        } else {
            $dati_competenze['cmp_sup_val_ita'] = $valore_linguaggi_italiano;
            $dati_competenze['cmp_sup_txt_ita'] = $testo_linguaggi_italiano;
            $dati_competenze['cmp_sup_val_ing'] = $valore_linguaggi_inglese;
            $dati_competenze['cmp_sup_txt_ing'] = $testo_linguaggi_inglese;
            $dati_competenze['cmp_sup_val_altri'] = $valore_linguaggi_altri;
            $dati_competenze['cmp_sup_txt_altri'] = $testo_linguaggi_altri;
            $dati_competenze['cmp_sup_val_mat'] = $valore_matematico;
            $dati_competenze['cmp_sup_txt_mat'] = $testo_matematico;
            $dati_competenze['cmp_sup_val_sci_tec'] = $valore_scientifico_tecno;
            $dati_competenze['cmp_sup_txt_sci_tec'] = $testo_scientifico_tecno;
            $dati_competenze['cmp_sup_val_sto_soc'] = $valore_storico_sociale;
            $dati_competenze['cmp_sup_txt_sto_soc'] = $testo_storico_sociale;
        }

        aggiorna_competenze_studente($dati_competenze, (int) $id_studente, (int) $current_user);
        //}}} </editor-fold>
        // Nuove competenze
        switch ($dati_classe["tipo_indirizzo"]) {
            case '4':
                $filtro_ordine = 'MM';
                break;
            case '6':
                $filtro_ordine = 'EE';
                break;
            case '0':
            case '1':
            case '2':
            case '3':
            case '5':
                $filtro_ordine = 'SS';
                break;
            default:
                // in futuro da gestise SS
                $filtro_ordine = '';
                break;
        }

        // Non salvo in caso non serva
        if ($filtro_ordine != '') {
            $competenze = estrai_competenze_scolastiche_studente($id_studente, $filtro_ordine, explode('/', $anno_scolastico)[0]);
            $competenze_da_salvare = [];
            foreach ($competenze as $competenza) {
                $ordine = strtolower($competenza['ordine_scolastico']);
                $id_competenza = $competenza['id_competenza_scolastica'];
                $competenze_da_salvare[$id_competenza]['id_competenze_valore'] = $competenza['id_competenze_valore'];
                $competenze_da_salvare[$id_competenza]['valore'] = ${$ordine . '_valore_' . $id_competenza};
                $competenze_da_salvare[$id_competenza]['testo'] = ${$ordine . '_testo_' . $id_competenza};
            }
            salva_valori_competenze_scolastiche($competenze_da_salvare, $id_studente, $current_user);
        }
    }

    if ($parametro_portfolio == 'SI') {
        //{{{ <editor-fold defaultstate="collapsed" desc="salvataggio portfolio">
        for ($cont_righe_port = 0; $cont_righe_port < $numero_righe_portfolio_definitivo; $cont_righe_port++) {
            $anno_portfolio = ${"anno_scolastico_" . $cont_righe_port};
            $tipo_portfolio = ${"tipo_" . $cont_righe_port};
            $descrizione_portfolio = ${"descrizione_" . $cont_righe_port};
            $giudizio_portfolio = ${"giudizio_" . $cont_righe_port};
            $id_portfolio = ${"id_portfolio_" . $cont_righe_port};

            if (!(strlen($giudizio_portfolio) > 0)) {
                $giudizio_portfolio = -1;
            }

            if ($id_portfolio > 0) {
                if (strlen($tipo_portfolio) > 0) {
                    modifica_portfolio((int) $id_portfolio, $anno_portfolio, $tipo_portfolio, $descrizione_portfolio, $giudizio_portfolio, (int) $current_user);
                } else {
                    elimina_portfolio($id_portfolio, $current_user);
                }
            } elseif (strlen($tipo_portfolio) > 0) {
                inserisci_portfolio((int) $id_studente, $anno_portfolio, $tipo_portfolio, $descrizione_portfolio, $giudizio_portfolio, (int) $current_user);
            }
        }
        //}}} </editor-fold>
    }

    // Aggiorno (se presente), il valore del consiglio orientativo del Trentino

    aggiorna_consiglio_orientativo_trentino($id_studente, $consiglio_orientativo_trentino);
}


//}}} </editor-fold>
//sezione dedicata a OSDB per il campo libero applicazione
$nome_scuola = strtoupper(trim(estrai_parametri_singoli("MASTERCOM_ID")));
$mostra_OSDB = 'NO';

if ($nome_scuola == "OSDB") {
    $dati_classe_osdb = estrai_classe($id_classe);
    if ($dati_classe_osdb['tipo_indirizzo'] != '4') {
        $mostra_OSDB = 'SI';
    }
    $mat_valori_applicazione_OSDB = estrai_valori_campo_libero_classe_periodo(1, (int) $id_classe, $periodo);
    $template->assign("mat_valori_applicazione_OSDB", $mat_valori_applicazione_OSDB);
}
$template->assign("mostra_OSDB", $mostra_OSDB);

$elenco_voti_pagelline = [];

if ($id_studente > 0 && $torna_a == 'studente') {
    $elenco_studenti = [0 => estrai_dati_studente((int) $id_studente)];

    $mat_campi_liberi = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente);
    $mat_campi_liberi_figli = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente, 'figli');

    //{{{ <editor-fold defaultstate="collapsed" desc="Divisione campi liberi normali e padre-figlio">
    $mat_campi_liberi_base = [];
    $mat_campi_liberi_parenti = [];

    foreach ($mat_campi_liberi as $campo_libero) {
        $trovato = false;
        $trovato_figlio = false;
        foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
            if ($campo_libero_figlio['id_padre'] == $campo_libero['id_campo_libero']) {
                $mat_campi_liberi_parenti[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
                $trovato = true;
            }
            if ($campo_libero_figlio['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                $trovato_figlio = true;
            }
        }
        if (!$trovato && !$trovato_figlio) {
            $mat_campi_liberi_base[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
        }
    }

    foreach ($mat_campi_liberi_parenti as $key_parenti => $campo_libero_parente) {
        foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
            if ($campo_libero_figlio['id_padre'] == $campo_libero_parente['id_campo_libero']) {
                $mat_campi_liberi_parenti[$key_parenti]['figli'][] = $campo_libero_figlio;
            }
        }
    }
    //}}} </editor-fold>

    $template->assign("mat_campi_liberi", $mat_campi_liberi_base);
    $template->assign("mat_campi_liberi_parenti", $mat_campi_liberi_parenti);

    $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_studente, $periodo, (int) $current_user, 'studente');

    if ($parametro_estendi_giorni_assenza_ritardi_note == 'SI') {
        $data_inizio_lezioni = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
        $data_fine_lezioni = estrai_parametri_singoli('DATA_FINE_LEZIONI');

        $riepilogo_assenze = estrai_numero_assenze_studente_periodo($id_studente, $data_inizio_lezioni, $data_fine_lezioni);
        $template->assign("riepilogo_assenze", $riepilogo_assenze);

        $riepilogo_note = estrai_note_disciplinari_studente($id_studente, $data_inizio_lezioni, $data_fine_lezioni);
        $template->assign("numero_note", count($riepilogo_note));
    }

    if ($parametro_estendi_info_pagelle == 'SI') {
        foreach ($parametro_estendi_info_pagelle_periodi as $periodo_selezionato) {
            if ($periodo_selezionato <> $periodo) {
                $esiste_valorizzato_periodo = true;

                if ($esiste_valorizzato_periodo == true) {
                    $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'] = estrai_voti_pagellina_studente_multi_classe((int) $id_classe, $periodo_selezionato, (int) $id_studente);

                    foreach ($elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'] as $id_mat_int => $materia_sel) {
                        $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'][$id_mat_int]['ore_assenza_tradotto'] = stampa_ore_o_minuti($materia_sel['ore_assenza']);
                        $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'][$id_mat_int]['monteore_totale_tradotto'] = stampa_ore_o_minuti($materia_sel['monteore_totale']);
                        $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'][$id_mat_int]['stampa_campi_liberi_tradotti'] = 'NO';
                        $testo_finale_campi_liberi = '';

                        if (is_array($materia_sel['campi_liberi'])) {
                            foreach ($materia_sel['campi_liberi'] as $campo_libero) {
                                if (is_array($campo_libero['valori_precomp'])) {
                                    foreach ($campo_libero['valori_precomp'] as $key_val_precom => $valore) {
                                        if ($valore['selezionato'] == 'SI') {
                                            if ($key_val_precom == '-1') {
                                                $testo_finale_campi_liberi .= $campo_libero['descrizione'] . ': ' . $valore['valore_testuale'] . '<BR>';
                                            } else {
                                                $testo_finale_campi_liberi .= $campo_libero['descrizione'] . ': ' . $valore['descrizione'] . '<BR>';
                                            }
                                        }
                                    }
                                }
                            }
                            if (strlen($testo_finale_campi_liberi) > 0) {
                                $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'][$id_mat_int]['stampa_campi_liberi_tradotti'] = 'SI';
                            }
                            $elenco_voti_pagelline_storiche[$periodo_selezionato]['dati'][$id_mat_int]['campi_liberi_tradotti'] = $testo_finale_campi_liberi;
                        }
                    }
                    switch ($periodo_selezionato) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Traduttore periodi">
                        case '1':
                        case '21':
                            $descrizione_periodo = '1^ pagellina infra-periodale';
                            break;
                        case '2':
                        case '22':
                            $descrizione_periodo = '2^ pagellina infra-periodale';
                            break;
                        case '3':
                        case '23':
                            $descrizione_periodo = '3^ pagellina infra-periodale';
                            break;
                        case '4':
                        case '24':
                            $descrizione_periodo = '4^ pagellina infra-periodale';
                            break;
                        case '5':
                        case '25':
                            $descrizione_periodo = '5^ pagellina infra-periodale';
                            break;
                        case '6':
                        case '26':
                            $descrizione_periodo = '6^ pagellina infra-periodale';
                            break;
                        case '7':
                        case '27':
                            $descrizione_periodo = 'Pagella fine primo trimestre/quadrimestre';
                            break;
                        case '8':
                        case '28':
                            $descrizione_periodo = 'Pagella fine secondo trimestre';
                            break;
                        case '9':
                        case '29':
                            $descrizione_periodo = 'Pagella fine anno';
                            break;
                        case '10':
                            $descrizione_periodo = 'Prove strutturate';
                            break;
                        case '11':
                            $descrizione_periodo = 'Tabellone voti esame di licenza';
                            break;
                        //}}} </editor-fold>
                    }
                    $elenco_voti_pagelline_storiche[$periodo_selezionato]['parametri']['descrizione'] = $descrizione_periodo;
                    $elenco_voti_pagelline_storiche[$periodo_selezionato]['parametri']['tipo_visualizzazione'] = identifica_periodo_tipo_voto($periodo_selezionato, (int) $id_classe);
                }
            }
        }
        $template->assign("elenco_voti_pagelline_storiche", $elenco_voti_pagelline_storiche);
    }

    if ($parametro_estendi_info_pagelle_curriculum == 'SI') {
        $curriculum_studente = estrai_curriculum_studente((int) $id_studente, $compattazione = 'no_iscritto');
        $template->assign("curriculum_studente", $curriculum_studente);
    }

    $mostra_alternanza = 'NO';
    if ($dati_classe['flag_alternanza'] == 1 && in_array($periodo, [9, 29])) {
        //{{{ <editor-fold defaultstate="collapsed" desc="Chiamata API">
        $utenza = estrai_utenza_api_alternanza();

        $autenticazione = [
            'username' => $utenza['username'],
            'password' => $utenza['password'],
        ];

        $parametri = [
            'id_studente' => (int) $id_studente,
        ];

        $url = $utenza['url'] . "/api/stage/";

        $response = chiamata_api($url, $parametri, 'GET', $autenticazione);
        //}}} </editor-fold>

        $progetti = json_decode($response);

        $elenco_progetti = [];
        $totale_monteore = 0;
        $totale_presente = 0;
        foreach ($progetti as $oggetto_progetto) {
            $id_progetto = $oggetto_progetto->id;
            $elenco_progetti['progetti'][$id_progetto]['nome'] = $oggetto_progetto->progetto->nome;
            $elenco_progetti['progetti'][$id_progetto]['data_inizio'] = $oggetto_progetto->data_inizio;
            $elenco_progetti['progetti'][$id_progetto]['data_fine'] = $oggetto_progetto->data_fine;
            $elenco_progetti['progetti'][$id_progetto]['monteore'] = $oggetto_progetto->numero_ore;
            $elenco_progetti['progetti'][$id_progetto]['ore_effettuate'] = $oggetto_progetto->ore_effettuate;

            $ore = explode(":", $oggetto_progetto->ore_effettuate);
            $secondi_presenza = ($ore[0] * 60 * 60) + ($ore[1] * 60);

            $totale_monteore += (int) $oggetto_progetto->numero_ore;
            $totale_presente += $secondi_presenza;
        }

        if (!empty($elenco_progetti)) {
            $mostra_alternanza = 'SI';

            $hrs = intval(intval($totale_presente) / 3600);
            $mns = intval(($$totale_presentetotale / 60) % 60);
            if ($mns >= 0 && $mns <= 9) {
                $mns = '0' . $mns;
            }
            $elenco_progetti['totale_presenze'] = $hrs . ":" . $mns;
            $elenco_progetti['totale_monteore'] = $totale_monteore;

            $template->assign("elenco_progetti", $elenco_progetti);
        }
    }
    $template->assign("mostra_alternanza", $mostra_alternanza);

    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione Corsi">
    $corsi_abilitati = estrai_parametri_singoli('ABILITAZIONE_GESTIONE_CORSI');
    if ($corsi_abilitati == 'SI') {
        $template->assign("corsi_abilitati", $corsi_abilitati);

        $corsi_studente = estrai_corsi_per_studente($id_studente);

        // Gestione valutazioni
        $codice_competenze_corsi = estrai_parametri_singoli('CODICE_COMPETENZE_CORSI');

        $elenco_corsi = [];
        foreach ($corsi_studente as $corso) {
            $id_classe = $corso['id_classe'];

            // Estraggo le annotazioni studente con cui vengono inserite le valutazioni del PARINI
            // ----

            $elenco_annotazioni = estrai_annotazioni_studente($id_studente, 'studente', $corso['id_materia']);

            $valutazione = $valutazioni_corso = [];
            foreach ($elenco_annotazioni as $annotazione) {
                if ($annotazione['codice'] == $codice_competenze_corsi) {
                    $valutazione = $annotazione;
                }
            }

            if (!empty($valutazione)) {
                $elenco_campi_liberi = estrai_valori_campi_liberi_parini($valutazione['id_annotazione_studente']);
            } else {
                $elenco_campi_liberi = [];
            }

            // Impostazione campi liberi specifici
            $partecipazione = $impegno = $apprendimento = [];
            foreach ($elenco_campi_liberi as $campo_libero) {
                $arr_valore = explode($campo_libero['separatore_descrizione_valore'], $campo_libero['valore_assegnato']);
                switch ($arr_valore[0]) {
                    case 1:
                        $livello = 'Iniziale';
                        break;
                    case 2:
                        $livello = 'Base';
                        break;
                    case 3:
                        $livello = 'Intermedio';
                        break;
                    case 4:
                        $livello = 'Avanzato';
                        break;
                    default:
                        $livello = 'Errore';
                        break;
                }

                $testo_tmp = "";
                if ($livello != 'Errore') {
                    $testo_tmp = "<b>" . $campo_libero['descrizione'] . "</b>: " . $arr_valore[1] . " - <b>Livello</b>: " . $livello . " " . $arr_valore[0];
                }

                $valori_tmp = [
                    'nome'        => $campo_libero['descrizione'],
                    'descrittore' => $arr_valore[1],
                    'livello'     => $livello,
                    'valore'      => $arr_valore[0],
                ];

                if (strtolower($campo_libero['nome']) == "collaborare e partecipare") {
                    $partecipazione[] = $valori_tmp;
                    $valutazioni_corso[] = $testo_tmp;
                } else if (strtolower($campo_libero['nome']) == "agire in modo autonomo e responsabile") {
                    $impegno[] = $valori_tmp;
                    $valutazioni_corso[] = $testo_tmp;
                } else if ($campo_libero['valore_assegnato'] != "") {
                    $apprendimento[] = $valori_tmp;
                    $valutazioni_corso[] = $testo_tmp;
                }
            }
            // ----
            $elenco_corsi[$id_classe] = $corso;

            $monteore_assenze = estrai_monteore_assenze_corso($id_studente, $corso['id_classe'], $data_inizio_lezioni, $data_fine_lezioni);

            $elenco_corsi[$id_classe]['lezioni'] = $monteore_assenze['lezioni'];
            $elenco_corsi[$id_classe]['monteore'] = $monteore_assenze['monteore'];
            $elenco_corsi[$id_classe]['presente'] = $monteore_assenze['presenze'];
            $elenco_corsi[$id_classe]['assenze'] = $monteore_assenze['monteore'] - $monteore_assenze['presenze'];
            $elenco_corsi[$id_classe]['monteore_tradotto'] = intval(intval($monteore_assenze['monteore']) / 3600) . " ore, " . intval(($monteore_assenze['monteore'] / 60) % 60) . " minuti";
            $elenco_corsi[$id_classe]['assenze_tradotte'] = intval(intval($elenco_corsi[$id_classe]['assenze']) / 3600) . " ore, " . intval(($elenco_corsi[$id_classe]['assenze'] / 60) % 60) . " minuti";
            $elenco_corsi[$id_classe]['valutazioni_corso'] = implode('<br>', $valutazioni_corso);
        }
        $template->assign("corsi_abilitati", $corsi_abilitati);
        $template->assign("elenco_corsi", $elenco_corsi);
        $template->assign("numero_corsi", count($elenco_corsi));
    }
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="Estraggo le valutazioni delle competenze per le materie">
    $riepilogo_competenze = [];

    if ($optional_competenze == 'OK' || $optional_competenze == '1') {
        $parametri = [
            "uid"  => $current_user,
            "ukey" => $current_key
        ];
        $token = chiamata_api('localhost/next-api/v1/login', $parametri);
        $parametri = [];
        $url = 'localhost/next-api/v1/competenze_studente/' . $id_studente;
        $result = chiamata_api($url, $parametri, 'GET', '', $token);

        $elenco_competenze_studente = json_decode($result, true);
        //file_put_contents('/tmp/temporaneo', print_r($elenco_competenze_studente['competenze'], true));

        if (isset($elenco_competenze_studente['competenze'])) {
            foreach ($elenco_materie as $key => $materia) {
                //$riepilogo_competenze_singola_materia_studente[$materia['id_materia']] = estrai_riepilogo_valutazioni_competenze_materia($elenco_competenze_studente['competenze'], $materia['id_materia']);
                $elenco_materie[$key]['riepilogo_valutazioni_competenze'] = estrai_riepilogo_valutazioni_competenze_materia($elenco_competenze_studente['competenze'], $materia['id_materia'], $periodo);

                // competenze e media relativa alle valutazioni della materia corrente (l'array in ingresso contiene gia' le competenze filtrate)
                // $elenco_valutazioni_competenze_materia = estrai_elenco_valutazioni_competenze($elenco_materie[$key]['riepilogo_valutazioni_competenze']);
                // $elenco_materie[$key]['media_generale_competenze_materia'] = $elenco_materie[$key]['media_generale_competenze_trasversale'] = '';
                // $cont_comp = $somma_comp = 0;
                // if (count($elenco_valutazioni_competenze_materia) > 0)
                // {
                //     foreach ($elenco_valutazioni_competenze_materia as $valutazione) {
                //         if ($valutazione['tipo'] == 'generale'){
                //             $cont_comp++;
                //             $somma_comp += $valutazione['valore_numerico'];
                //         }
                //     }
                //     $elenco_materie[$key]['media_generale_competenze_materia'] = round($somma_comp / $cont_comp, 2);
                // }
                //media trasversale generale di tutte le valutazioni delle competenze che valgono per la materia in oggetto
                //$elenco_valutazioni_generali_trasversali = estrai_elenco_valutazioni_competenze_trasversali_materia($elenco_competenze_studente['competenze'], $materia['id_materia']);
                $elenco_valutazioni_generali_trasversali = estrai_elenco_valutazioni_competenze($elenco_competenze_studente['competenze'], $materia['id_materia'], 0, true, 0, $periodo);
                if (count($elenco_valutazioni_generali_trasversali) > 0) {
                    $somma_tr_tmp = $somma_mat_tmp = $cont_tr_tmp = $cont_mat_tmp = 0;
                    foreach ($elenco_valutazioni_generali_trasversali as $valutazione) {
                        if ($valutazione['tipo'] == 'generale') {
                            if ($valutazione['valore_numerico'] > 0) {
                                $cont_tr_tmp++;
                                $somma_tr_tmp += $valutazione['valore_numerico'];

                                if ($valutazione['id_materia'] == $materia['id_materia']) {
                                    $cont_mat_tmp++;
                                    $somma_mat_tmp += $valutazione['valore_numerico'];
                                }
                            }
                        }
                    }
                    $elenco_materie[$key]['media_generale_competenze_trasversale'] = round($somma_tr_tmp / $cont_tr_tmp, 2);
                    $elenco_materie[$key]['media_generale_competenze_materia'] = round($somma_mat_tmp / $cont_mat_tmp, 2);
                }
            }
        }
    }
    //}}} </editor-fold>
} else {
    $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo, (int) $current_user, 'classe');
}

//le assenze per ora le visualizziamo a priori
$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

$parametro_visualizza_materie_tabellone = estrai_parametri_singoli("VISUALIZZA_MATERIE_TABELLONE");

$elenco_materie_ind = [];

if (is_array($elenco_materie)) {
    $cont_col = 0;
    $elenco_docenti_sostegno = estrai_docenti_sostegno_classe($id_classe);
    foreach ($elenco_materie as $indice => $materia) {

        // Preparo la lista dei professori da usare sulla base delle materie scelte:
        if (is_array($materia['professori'])) {
            foreach ($materia['professori'] as $professore_corrente) {
                $presenza = controlla_presenza_professore_consiglio((int) $id_classe, $periodo, (int) $professore_corrente);
                $dati_professore = estrai_dati_professore((int) $professore_corrente);

                if (is_array($presenza)) {
                    $mat_professori[$professore_corrente] = array('nome' => $dati_professore['cognome'] . ' ' . $dati_professore['nome'], 'presente' => $presenza['id_professore_sostituto']);
                } else {
                    $mat_professori[$professore_corrente] = array('nome' => $dati_professore['cognome'] . ' ' . $dati_professore['nome'], 'presente' => 0);
                }
            }
        }

        // Aggiungo alla lista dei docenti anche quelli di sostegno che non sono abbinati
        if (!empty($elenco_docenti_sostegno)) {
            foreach ($elenco_docenti_sostegno as $docente_sostegno) {
                $professore_corrente = $docente_sostegno['id_professore'];
                $presenza = controlla_presenza_professore_consiglio((int) $id_classe, $periodo, (int) $professore_corrente);
                $dati_professore = estrai_dati_professore((int) $professore_corrente);

                if (is_array($presenza)) {
                    $mat_professori[$professore_corrente] = array('nome' => $dati_professore['cognome'] . ' ' . $dati_professore['nome'], 'presente' => $presenza['id_professore_sostituto']);
                } else {
                    $mat_professori[$professore_corrente] = array('nome' => $dati_professore['cognome'] . ' ' . $dati_professore['nome'], 'presente' => 0);
                }
            }
        }

        //controllo se nel tabellone si vogliono visualizzare tutte le materie o solo quelle con "in_media_pagelle" a si o a no
        $usa_materia = true;

        if ($periodo == "11") {
            if ($elenco_materie[$indice]['in_media_pagelle'] != 'SI') {
                $usa_materia = false;
            }
        } else {
            if ($parametro_visualizza_materie_tabellone == 'SOLO_SI_NO' && $elenco_materie[$indice]['in_media_pagelle'] == 'NV') {
                $usa_materia = false;
            }
        }

        if ($usa_materia) {
            $elenco_materie_ind[$materia["id_materia"]] = $elenco_materie[$indice];
            $elenco_materie_ind[$materia["id_materia"]]['colonna'] = $cont_col;
            $significati_voto = estrai_significati_voti_pagelle($materia["tipo_valutazione"], "solo_abilitati", $periodo);
            $elenco_materie_ind[$materia["id_materia"]]['schema_voti'] = $significati_voto;

            if ($tipo_visualizzazione_voti == 'voto_singolo') {
                $elenco_materie_ind[$materia["id_materia"]]['colonne'] = 1;
            } elseif ($tipo_visualizzazione_voti == 'scritto_orale') {
                $elenco_materie_ind[$materia["id_materia"]]['colonne'] = 2;
            } elseif ($tipo_visualizzazione_voti == 'scritto_orale_pratico') {
                $elenco_materie_ind[$materia["id_materia"]]['colonne'] = 3;
            } else {
                $found = false;
                $elenco_materie_ind[$materia["id_materia"]]['colonne'] = 0;

                //if($elenco_materie_ind[$materia["id_materia"]]['cpm_scritto'] == '1')

                if ($tipo_visualizzazione_voti == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'], [2, 3])) {
                    $col_scritto = '1';
                } else {
                    $col_scritto = in_array($elenco_materie_ind[$materia["id_materia"]]['cpm_scritto'], ['0', '1']) ? $elenco_materie_ind[$materia["id_materia"]]['cpm_scritto'] : $elenco_materie_ind[$materia["id_materia"]]['scritto'];
                }
                if ($col_scritto == '1') {
                    $found = true;
                    $elenco_materie_ind[$materia["id_materia"]]['colonne'] ++;
                }

                //if($elenco_materie_ind[$materia["id_materia"]]['cpm_orale'] == '1')
                if ($tipo_visualizzazione_voti == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'], [2, 3])) {
                    $col_orale = '1';
                } else {
                    $col_orale = in_array($elenco_materie_ind[$materia["id_materia"]]['cpm_orale'], ['0', '1']) ? $elenco_materie_ind[$materia["id_materia"]]['cpm_orale'] : $elenco_materie_ind[$materia["id_materia"]]['orale'];
                }
                if ($col_orale == '1') {
                    $found = true;
                    $elenco_materie_ind[$materia["id_materia"]]['colonne'] ++;
                }

                //if($elenco_materie_ind[$materia["id_materia"]]['cpm_pratico'] == '1')
                if ($tipo_visualizzazione_voti == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'], [3])) {
                    $col_pratico = '1';
                } else {
                    $col_pratico = in_array($elenco_materie_ind[$materia["id_materia"]]['cpm_pratico'], ['0', '1']) ? $elenco_materie_ind[$materia["id_materia"]]['cpm_pratico'] : $elenco_materie_ind[$materia["id_materia"]]['pratico'];
                }
                if ($col_pratico == '1') {
                    $found = true;
                    $elenco_materie_ind[$materia["id_materia"]]['colonne'] ++;
                }

                if (!$found) {
                    $elenco_materie_ind[$materia["id_materia"]]['colonne'] = 1;
                }
            }

            $cont_col += $elenco_materie_ind[$materia["id_materia"]]['colonne'];
        }
    }
    uasort($mat_professori, "sort_by_nome");

    $template->assign("mat_professori", $mat_professori);
    $elenco_professori = estrai_elenco_professori_select();
    $template->assign("elenco_professori", $elenco_professori);
}

if (
        (in_array($periodo, ['9', '10', '11', '29'])) && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore')
) {
    //sezione percentuale assenze
    $elenco_materie_ind["perc_assenze"]["id_materia"] = "perc_assenze";
    $elenco_materie_ind["perc_assenze"]["codice"] = "ASS";
    $elenco_materie_ind["perc_assenze"]["descrizione"] = "% ASS.";
    $elenco_materie_ind["perc_assenze"]["campo_aggiuntivo"] = "perc_assenze";
    $elenco_materie_ind["perc_assenze"]["colonna"] = $cont_col;
    $elenco_materie_ind["perc_assenze"]["colonne"] = 1;
    $cont_col++;
}

if (
        (in_array($periodo, ['9', '10', '11', '29'])) && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore')
) {
    //sezione media voti
    $elenco_materie_ind["media_voti"]["id_materia"] = "media_voti";
    $elenco_materie_ind["media_voti"]["codice"] = "MED";
    $elenco_materie_ind["media_voti"]["descrizione"] = "MEDIA";
    $elenco_materie_ind["media_voti"]["campo_aggiuntivo"] = "media_voti";
    $elenco_materie_ind["media_voti"]["colonna"] = $cont_col;
    $elenco_materie_ind["media_voti"]["colonne"] = 1;
    $cont_col++;
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && ((intval($dati_classe["classe"]) == 5 && $dati_classe["tipo_indirizzo"] != "1" && $dati_classe["tipo_indirizzo"] != "5" && $dati_classe["tipo_indirizzo"] != "6"
        ) || (intval($dati_classe["classe"]) == 3 && $dati_classe["tipo_indirizzo"] == "1") || (intval($dati_classe["classe"]) == 4 && $dati_classe["tipo_indirizzo"] == "5")
        )
) {
    //sezione ammissione quinte
    $elenco_materie_ind["ammissione"]["id_materia"] = "ammissione";
    $elenco_materie_ind["ammissione"]["codice"] = "AMM";
    $elenco_materie_ind["ammissione"]["descrizione"] = "AMMISSIONE";
    $elenco_materie_ind["ammissione"]["campo_aggiuntivo"] = "ammissione";
    $elenco_materie_ind["ammissione"]["colonna"] = $cont_col;
    $elenco_materie_ind["ammissione"]["colonne"] = 1;

    $cont_col++;

    $parametro_id_materia_per_compilazione_giudizio_ammissione = estrai_parametri_singoli("GIUDIZIO_DA_AMMISSIONE_ID_MATERIA");

    if (intval($parametro_id_materia_per_compilazione_giudizio_ammissione) > 0) {
        $parametro_template_per_compilazione_giudizio_ammissione = estrai_parametri_singoli("GIUDIZIO_DA_AMMISSIONE_TEMPLATE");
        $template->assign("parametro_id_materia_per_compilazione_giudizio_ammissione", $parametro_id_materia_per_compilazione_giudizio_ammissione);
        $template->assign("parametro_template_per_compilazione_giudizio_ammissione", $parametro_template_per_compilazione_giudizio_ammissione);
    }
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && ((intval($dati_classe["classe"]) == 3 || intval($dati_classe["classe"]) == 4) && $dati_classe["tipo_indirizzo"] == "2")
) {
    //sezione voto ammissione 3° classi ipsia
    $elenco_materie_ind["ammissione_qualifica"]["id_materia"] = "ammissione_qualifica";
    $elenco_materie_ind["ammissione_qualifica"]["codice"] = "AMM";
    $elenco_materie_ind["ammissione_qualifica"]["descrizione"] = "AMMISSIONE";
    $elenco_materie_ind["ammissione_qualifica"]["campo_aggiuntivo"] = "ammissione_qualifica";
    $elenco_materie_ind["ammissione_qualifica"]["colonna"] = $cont_col;
    $elenco_materie_ind["ammissione_qualifica"]["colonne"] = 1;
    $cont_col++;

    //sezione voto qualifica 3° classi ipsia
    $elenco_materie_ind["qualifica"]["id_materia"] = "qualifica";
    $elenco_materie_ind["qualifica"]["codice"] = "QUAL";
    $elenco_materie_ind["qualifica"]["descrizione"] = "QUALIFICA";
    $elenco_materie_ind["qualifica"]["campo_aggiuntivo"] = "qualifica";
    $elenco_materie_ind["qualifica"]["colonna"] = $cont_col;
    $elenco_materie_ind["qualifica"]["colonne"] = 1;

    $cont_col++;

    //Devo estrarre i dati aggiuntivi relativi al calcolo degli esami di ammissione
    if ($id_studente > 0 && $torna_a == 'studente') {
        $elenco_risultati_ammissione = calcola_voto_ammissione_esame_qualifica((int) $id_studente, 'studente', (int) $current_user);
    } else {
        $elenco_risultati_ammissione = calcola_voto_ammissione_esame_qualifica((int) $id_classe, 'classe', (int) $current_user);
    }

    $template->assign("elenco_risultati_ammissione", $elenco_risultati_ammissione);
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && (intval($dati_classe["classe"]) == 3 && $dati_classe["tipo_indirizzo"] == "3")
) {
    //sezione voto ammissione 3° classi istituto arte
    $elenco_materie_ind["ammissione_maestro"]["id_materia"] = "ammissione_maestro";
    $elenco_materie_ind["ammissione_maestro"]["codice"] = "AMM";
    $elenco_materie_ind["ammissione_maestro"]["descrizione"] = "AMMISSIONE";
    $elenco_materie_ind["ammissione_maestro"]["campo_aggiuntivo"] = "ammissione_maestro";
    $elenco_materie_ind["ammissione_maestro"]["colonna"] = $cont_col;
    $elenco_materie_ind["ammissione_maestro"]["colonne"] = 1;
    $cont_col++;
}

if (
        $periodo == "29" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && $dati_classe["tipo_indirizzo"] == "4"
) {
    //sezione voto ammissione per 1°, 2° e 3° delle medie
    if ($anno_fine == 2020) {
        if (intval($dati_classe["classe"]) == 3) {
            //sezione voto finale per 3° delle medie
            $elenco_materie_ind["voto_finale_medie"]["id_materia"] = "voto_finale_medie";
            $elenco_materie_ind["voto_finale_medie"]["codice"] = "ESA";
            $elenco_materie_ind["voto_finale_medie"]["descrizione"] = "ESAME";
            $elenco_materie_ind["voto_finale_medie"]["campo_aggiuntivo"] = "voto_finale_medie";
            $elenco_materie_ind["voto_finale_medie"]["colonna"] = $cont_col;
            $elenco_materie_ind["voto_finale_medie"]["colonne"] = 1;
            $cont_col++;
        } else {
            $elenco_materie_ind["ammissione_medie"]["id_materia"] = "ammissione_medie";
            $elenco_materie_ind["ammissione_medie"]["codice"] = "AMM";
            $elenco_materie_ind["ammissione_medie"]["descrizione"] = "AMMISSIONE";
            $elenco_materie_ind["ammissione_medie"]["campo_aggiuntivo"] = "ammissione_medie";
            $elenco_materie_ind["ammissione_medie"]["colonna"] = $cont_col;
            $elenco_materie_ind["ammissione_medie"]["colonne"] = 1;
            $cont_col++;
        }
    } else {
        $elenco_materie_ind["ammissione_medie"]["id_materia"] = "ammissione_medie";
        $elenco_materie_ind["ammissione_medie"]["codice"] = "AMM";
        $elenco_materie_ind["ammissione_medie"]["descrizione"] = "AMMISSIONE";
        $elenco_materie_ind["ammissione_medie"]["campo_aggiuntivo"] = "ammissione_medie";
        $elenco_materie_ind["ammissione_medie"]["colonna"] = $cont_col;
        $elenco_materie_ind["ammissione_medie"]["colonne"] = 1;
        $cont_col++;
    }
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && $dati_classe["tipo_indirizzo"] == "6"
) {
    $elenco_materie_ind["ammissione_medie"]["id_materia"] = "ammissione_elementari";
    $elenco_materie_ind["ammissione_medie"]["codice"] = "AMM";
    $elenco_materie_ind["ammissione_medie"]["descrizione"] = "AMMISSIONE";
    $elenco_materie_ind["ammissione_medie"]["campo_aggiuntivo"] = "ammissione_elementari";
    $elenco_materie_ind["ammissione_medie"]["colonna"] = $cont_col;
    $elenco_materie_ind["ammissione_medie"]["colonne"] = 1;
    $cont_col++;
}

if (
        $periodo == "11" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && (intval($dati_classe["classe"]) == 3 && $dati_classe["tipo_indirizzo"] == "3")
) {
    //sezione voto ammissione 3° classi istituto arte
    $elenco_materie_ind["licenza"]["id_materia"] = "licenza";
    $elenco_materie_ind["licenza"]["codice"] = "LIC";
    $elenco_materie_ind["licenza"]["descrizione"] = "LICENZA";
    $elenco_materie_ind["licenza"]["campo_aggiuntivo"] = "licenza";
    $elenco_materie_ind["licenza"]["colonna"] = $cont_col;
    $elenco_materie_ind["licenza"]["colonne"] = 1;
    $cont_col++;

    //sezione crediti
    $elenco_materie_ind["crediti"]["id_materia"] = "crediti";
    $elenco_materie_ind["crediti"]["codice"] = "CR";
    $elenco_materie_ind["crediti"]["descrizione"] = "CREDITI";
    $elenco_materie_ind["crediti"]["campo_aggiuntivo"] = "crediti";
    $elenco_materie_ind["crediti"]["colonna"] = $cont_col;
    $elenco_materie_ind["crediti"]["colonne"] = 1;
    $cont_col++;
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') &&
        (
        (
        intval($dati_classe["classe"]) >= 3 && $dati_classe["tipo_indirizzo"] != "1" && $dati_classe["tipo_indirizzo"] != "2" && $dati_classe["tipo_indirizzo"] != "3" && $dati_classe["tipo_indirizzo"] != "5" && $dati_classe["tipo_indirizzo"] != "6"
        ) || (intval($dati_classe["classe"]) < 4 && $dati_classe["tipo_indirizzo"] == "1") || (intval($dati_classe["classe"]) > 3 && $dati_classe["tipo_indirizzo"] == "2") || (intval($dati_classe["classe"]) > 3 && $dati_classe["tipo_indirizzo"] == "3") || (intval($dati_classe["classe"]) >= 2 && $dati_classe["tipo_indirizzo"] == "5")
        )
) {
    //sezione crediti
    $elenco_materie_ind["crediti"]["id_materia"] = "crediti";
    $elenco_materie_ind["crediti"]["codice"] = "CR";
    $elenco_materie_ind["crediti"]["descrizione"] = "CREDITI";
    $elenco_materie_ind["crediti"]["campo_aggiuntivo"] = "crediti";
    $elenco_materie_ind["crediti"]["colonna"] = $cont_col;
    $elenco_materie_ind["crediti"]["colonne"] = 1;
    $cont_col++;
}

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') && (intval($dati_classe["classe"]) == 3 && $dati_classe["tipo_indirizzo"] == "2")
) {
    //sezione crediti
    $elenco_materie_ind["crediti_qual"]["id_materia"] = "crediti_qual";
    $elenco_materie_ind["crediti_qual"]["codice"] = "CR";
    $elenco_materie_ind["crediti_qual"]["descrizione"] = "CREDITI";
    $elenco_materie_ind["crediti_qual"]["campo_aggiuntivo"] = "crediti";
    $elenco_materie_ind["crediti_qual"]["colonna"] = $cont_col;
    $elenco_materie_ind["crediti_qual"]["colonne"] = 1;
    $cont_col++;
}

$parametro_visualizza_esito_finale = estrai_parametri_singoli("ESITO_MANUALE_SUPERIORI");

if (
        $periodo == "9" && (($form_stato == 'professore' && $coordinatore == "SI") || $form_stato == 'amministratore') &&
        (
        (intval($dati_classe["classe"]) != 5 && $dati_classe["tipo_indirizzo"] == "0") ||
        (intval($dati_classe["classe"]) != 3 && $dati_classe["tipo_indirizzo"] == "1") ||
        (intval($dati_classe["classe"]) != 5 && $dati_classe["tipo_indirizzo"] == "2") ||
        (intval($dati_classe["classe"]) != 3 && intval($dati_classe["classe"]) != 5 && $dati_classe["tipo_indirizzo"] == "3") ||
        (intval($dati_classe["classe"]) != 4 && intval($dati_classe["classe"]) != 5 && $dati_classe["tipo_indirizzo"] == "5")
        ) &&
        (
        $parametro_visualizza_esito_finale == 'SI' || (((intval($dati_classe["classe"]) == 1 || intval($dati_classe["classe"]) == 2) && $dati_classe["tipo_indirizzo"] == "2"))
        )
) {
    //sezione crediti
    $elenco_materie_ind["esito_finale"]["id_materia"] = "esito_finale";
    $elenco_materie_ind["esito_finale"]["codice"] = "EF";
    $elenco_materie_ind["esito_finale"]["descrizione"] = "ESITO FINALE";
    $elenco_materie_ind["esito_finale"]["campo_aggiuntivo"] = "esito_finale";
    $elenco_materie_ind["esito_finale"]["colonna"] = $cont_col;
    $elenco_materie_ind["esito_finale"]["colonne"] = 1;
    $cont_col++;

    if ($id_classe > 0) {
        $elenco_esiti = estrai_esiti_disponibili($id_classe);

        $template->assign("elenco_esiti", $elenco_esiti);
    }
}

/* }}} */


$cont_stud = 0;
$giudizio_sospeso_6_in_condotta = estrai_parametri_singoli("ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA");

if (is_array($elenco_studenti)) {
    foreach ($elenco_studenti as $key => $studente) {
        //{{{ <editor-fold defaultstate="collapsed">
        $anno_scolastico = $anno_inizio . "/" . $anno_fine;

        $elenco_debiti = estrai_debiti_studente((int) $studente['id_studente'], $anno_scolastico);

        if (is_array($elenco_debiti)) {
            foreach ($elenco_debiti as $debito) {
                if ($debito["id_materia"] != "-1") {
                    $elenco_voti_pagelline[$studente['id_studente']][$debito["id_materia"]]["debito"] = "*";
                }
            }
        }

        // estraggo la fascia dei crediti
        $somma_valori = 0;
        $cont_media = 0;
        $credito_massimo_ammesso = false;

        if ($trentino_abilitato == 'SI' || $giudizio_sospeso_6_in_condotta != 'SI') {
            $credito_massimo_ammesso = true;
        }

        foreach ($elenco_materie_ind as $materia_per_media) {
            $voto_per_media = $elenco_voti_pagelline[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina'];
            //se c'è una carenza, per il trentino, trasformo il voto negativo in 6
            if ($trentino_abilitato == 'SI' && in_array(intval($elenco_voti_pagelline[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']), [1, 2, 3, 4, 5]) && verifica_presenza_debito($studente['id_studente'], $elenco_voti_pagelline[$studente['id_studente']][$materia_per_media['id_materia']]['id_materia'], $anno_scolastico)) {
                $voto_per_media = 6;
            }

            if ($materia_per_media['in_media_pagelle'] == 'SI' && intval($elenco_voti_pagelline[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']) > 0) {
                $somma_valori += floatval($voto_per_media);
                $cont_media++;
            }

            if ($materia_per_media['tipo_materia'] == 'CONDOTTA') {
                if ($elenco_voti_pagelline[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina'] >= 9) {
                    $credito_massimo_ammesso = true;
                }
            }
        }

        if ($cont_media > 0) {
            $risultato_media = round($somma_valori / $cont_media, 2);
        } else {
            $risultato_media = '';
        }

        $lista_crediti = calcola_crediti_ammessi($risultato_media, $dati_classe['anno_reale_classe'], 'NO', $studente, $studente['pei'], $studente['bes']);
        if (!$credito_massimo_ammesso){
            $lista_crediti = array_slice($lista_crediti, 0, 1);
        }
        $elenco_studenti[$key]['fascia_crediti'] = $lista_crediti;
        $elenco_studenti[$key]['media_voti'] = $risultato_media;
        //}}} </editor-fold>
    }
}

$template->assign("mat_materie", $elenco_materie_ind);
$template->assign("mat_studenti", $elenco_studenti);
$template->assign("mat_voti", $elenco_voti_pagelline);
$template->assign("periodo", $periodo);
$template->assign("coordinatore", $coordinatore);
$template->assign("verbalizzatore", $verbalizzatore);
$template->assign("id_studente", $id_studente);
$template->assign("id_materia", $id_materia);

$motivazioni_crediti = estrai_motivazioni_crediti();
$template->assign("motivazioni_crediti", $motivazioni_crediti);
