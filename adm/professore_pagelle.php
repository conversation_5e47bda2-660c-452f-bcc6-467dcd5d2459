<?php

if($id_classe > 0)
{
	//estraggo i dati della classe
	$dati_classe = estrai_classe((int) $id_classe);
	$template->assign("dati_classe", $dati_classe);

	$periodo_pagella_in_uso = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO",(int) $id_classe, 'classe');

    if($dati_classe['tipo_indirizzo'] == '4')
	{
		$periodo_pagella_in_uso = $periodo_pagella_in_uso + 20;
    }
}
elseif($id_indirizzo > 0)
{
    $dati_indirizzo = estrai_indirizzo_singolo($id_indirizzo);

	$periodo_pagella_in_uso = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO",(int) $id_indirizzo, 'indirizzo');

    if($dati_indirizzo['tipo_indirizzo'] == '4')
	{
		$periodo_pagella_in_uso = $periodo_pagella_in_uso + 20;
    }
}
else
{
	$periodo_pagella_in_uso = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO");
}

switch ($periodo_pagella_in_uso) {
    case 1:
        $periodo_pagella_descrittivo = '1° pagellina infraquadrimestrale';
        break;
    case 2:
        $periodo_pagella_descrittivo = '2° pagellina infraquadrimestrale';
        break;
    case 3:
        $periodo_pagella_descrittivo = '3° pagellina infraquadrimestrale';
        break;
    case 4:
        $periodo_pagella_descrittivo = '4° pagellina infraquadrimestrale';
        break;
    case 5:
        $periodo_pagella_descrittivo = '5° pagellina infraquadrimestrale';
        break;
    case 6:
        $periodo_pagella_descrittivo = '6° pagellina infraquadrimestrale';
        break;
    case 7:
    case 27:
        $periodo_pagella_descrittivo = 'Pagella fine 1° quadrimestre/trimestre';
        break;
    case 8:
    case 28:
        $periodo_pagella_descrittivo = 'Pagella fine 2° trimestre';
        break;
    case 9:
    case 29:
        $periodo_pagella_descrittivo = 'Pagella fine anno';
        break;
    case '9_P':
        $periodo_pagella_descrittivo = 'Pagella fine anno - Anno precedente';
        break;
    default:
        $periodo_pagella_descrittivo = '';
        break;
    }

$template->assign("periodo_pagella_in_uso",$periodo_pagella_descrittivo);

if ($periodo_pagella_in_uso == '9_P') {
    $periodo_provenienza = '9_P';
    $periodo_pagella_in_uso = '9';
    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
    $periodo_pagella_descrittivo = 'Pagella fine anno - Anno precedente';
    $anno = explode('/', $anno_scolastico);
    $db_key = "mastercom_".($anno[0] - 1)."_".($anno[1] - 1);

    $template->assign("titolo_as_precedente", "ATTENZIONE! <br> Questa sezione fa riferimento all'anno scolastico precedente in quanto è stato impostato il periodo \"Pagella fine anno - Anno precedente\"");
    $template->assign("msg_as_precedente", "Andare in Setup D02 - Parametri Generali Pagelle e Pagelline per disabilitare tale impostazione");
    $template->assign("periodo_pagella_in_uso",$periodo_pagella_descrittivo);

}
$elenco_classi_accessibili_generale = estrai_classi_plugin((int) $current_user, 'pagelle');

if ($periodo_provenienza === '9_P'
    &&
    date('m') >= 10
    &&
    date('d') >= 01
) {
    $template->assign("periodo_errato", 'SI');
}
$template->assign("elenco_classi_accessibili_generale",$elenco_classi_accessibili_generale);

$prove_strutturate = estrai_parametri_singoli("PROVE_STRUTTURATE");
$template->assign("prove_strutturate", $prove_strutturate);

if(strlen($periodo) == 0)
{
	$periodo = $periodo_pagella_in_uso;
}

$elenco_indirizzi = estrai_indirizzi_del_professore((int) $id_professore);
$template->assign("indirizzi", $elenco_indirizzi);

$aree_disciplinari = estrai_parametri_singoli("ABILITA_AREE_DISCIPLINARI");
$template->assign("aree_disciplinari", $aree_disciplinari);

$id_classe_aree = (!$id_classe) ? 0 : $id_classe;
$aree_presenti = count(estrai_aree_disciplinari($id_classe_aree));
$template->assign("aree_presenti", $aree_presenti);

switch($stato_secondario)
{
	case "classi":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_indirizzo != "")
		{
			$stato_espansione = 1;
            $elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
		}
		//}}} </editor-fold>
		break;
	case "studenti":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			$stato_espansione = 1;
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
			$template->assign("coordinatore", $coordinatore);

			//estraggo i dati della classe
			$dati_classe = estrai_classe((int) $id_classe);
			$template->assign("dati_classe", $dati_classe);


            if ($dati_classe['flag_alternanza'] == 1)
            {
                $mostra_alternanza = 'SI';
            }
            else
            {
                $mostra_alternanza = 'NO';
            }
            $template->assign("mostra_alternanza",$mostra_alternanza);
		}
		//}}} </editor-fold>
		break;
	case "seleziona_dati_per_pagellina_classe":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);
			$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
			$template->assign("pagelle_stato", $pagelle_stato);

			//estraggo le materie della classe del professore
			$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
			$template->assign("elenco_materie", $elenco_materie_classe);
		}
		//}}} </editor-fold>
		break;
	case "visualizza_pagellina_classe_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			$tipi_recupero = estrai_tipi_recupero();
			$template->assign("tipi_recupero", $tipi_recupero);

			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);

			//estraggo le materie della classe del professore a seconda che sia un coordinatore oppure un prof semplice
			if($coordinatore == "SI")
			{
				$elenco_materie_classe = estrai_materie_classe((int) $id_classe);
				$template->assign("elenco_materie", $elenco_materie_classe);
			}
			else
			{
				$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
				$template->assign("elenco_materie", $elenco_materie_classe);
			}

			$elenco_voti_pagelline = estrai_voti_pagellina_classe((int) $id_classe, $periodo, (int) $id_materia);
			$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline);

			$dati_materia = estrai_dati_materia((int) $id_materia);
			$template->assign("dati_materia", $dati_materia);

			$significati_voto = estrai_significati_voti_pagelle($dati_materia[13], "solo_abilitati", $periodo);
			$template->assign("significati_voto", $significati_voto);

			//creo l'array per la combo(select) delle ore di assenza
			$ore_assenza = [];

			for($cont = 0; $cont < 300; $cont++)
			{
				$ore_assenza[$cont] = $cont;
			}

			$template->assign("ore_assenza", $ore_assenza);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);

			$chiusura = estrai_chiusura_scrutini((int) $id_classe);

            $blocco_scrutini = $chiusura[$periodo] == "SI" ? "SI" : "NO";

			$template->assign("blocco_scrutini", $blocco_scrutini);
		}
		//}}} </editor-fold>
		break;
	case "visualizza_pagellina_classe_update":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);

			//estraggo le materie della classe del professore a seconda che sia un coordinatore oppure un prof semplice
			if($coordinatore == "SI")
			{
				$elenco_materie_classe = estrai_materie_classe((int) $id_classe);
				$template->assign("elenco_materie", $elenco_materie_classe);
			}
			else
			{
				$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
				$template->assign("elenco_materie", $elenco_materie_classe);
			}

			$elenco_voti_pagelline = estrai_voti_pagellina_classe((int) $id_classe, $periodo, (int) $id_materia);
			$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline);

			$today = getdate();
			$data = mktime(1, 1, 1, intval($today["mon"]), $today["mday"], $today["year"]);

            $arr_log_salva = [];
            for($cont = 0; $cont < count($elenco_voti_pagelline); $cont++)
			{
				$voto = ${"voto_" . $cont};
				$debito = ${"debito_" . $cont};
				$ore_ass = ${"ore_ass_" . $cont};
				$id_voto_pagellina = ${"id_voto_pagellina_" . $cont};
				$id_pagellina = ${"id_pagellina_" . $cont};
				$id_studente = ${"id_studente_" . $cont};

				if($voto != "")
				{
					if($id_pagellina == "X")
					{
						$id_pagellina = inserisci_pagellina($periodo, (int) $id_studente, (int) $current_user);
					}

					if ($id_voto_pagellina == "X") {
                        $result = inserisci_voto_pagellina($voto, $debito, (int) $id_materia, (int) $id_pagellina, $ore_ass, $data, "", "", "", (int) $current_user);
                    } else {
                        $result = modifica_voto_pagellina((int) $id_voto_pagellina, $voto, $debito, $ore_ass, $data, "SI", "SI", "SI", "", "", "", (int) $current_user);
                    }
                }
			                $arr_log_salva[$id_materia] = [
                    'id_pagellina'          => $id_pagellina,
                    'id_voto_pagellina'     => $id_voto_pagellina,
                    'materia'               => $id_materia,
                    'voto_pagellina'        => $voto,
                    'ore_assenza'           => $ore_ass,
                    'debito'                => $debito
                ];
			}

            if (is_object($logger)) {
                $logger->log([
                    'Level' => 0,
                    'Type' => 'INFO',
                    'Event' => __FUNCTION__,
                    'Scope' => __FILE__,
                    'Message' => 'Salvataggio Pagellina: ' . $id_pagellina . ' Periodo: ' . $periodo . ' Studente: ' . $id_studente,
                    'Extra' => $arr_log_salva
                ]);
            }

			$cosa = "MODIFICATI VOTI IN PAGELLA CON ID: " . $id_pagellina . " DELLA CLASSE CON ID: " . $id_classe . " NEL PERIODO " . $periodo . " NELLA MATERIA CON ID: " . $id_materia;
			inserisci_log_storico((int) $current_user, "GESTIONE_PAGELLE", $cosa);

			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "dati_ammissione_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			//e di far chiudere l'elenco degli studenti
			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti_classe", $elenco_studenti_classe);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "dati_ammissione_update":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			//e di far chiudere l'elenco degli studenti
			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			foreach($elenco_studenti_classe as $studente)
			{
				$id_studente = $studente["id_studente"];
				$curriculum_prima = floatval(${'curriculum_prima_' . $id_studente});
				$curriculum_seconda = floatval(${'curriculum_seconda_' . $id_studente});
				$stage_professionali = floatval(${'stage_professionali_' . $id_studente});
				$stato = modifica_dati_curriculum_e_stage((int) $id_studente, $curriculum_prima, $curriculum_seconda, $stage_professionali, (int) $current_user);
				$messaggio = "Valori inseriti correttamente!";
			}
			//e di far chiudere l'elenco degli studenti
			$template->assign("elenco_studenti_classe", $elenco_studenti_classe);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
			$template->assign("messaggio", $messaggio);
			$stato_secondario = "dati_ammissione_display";
		}
		//}}} </editor-fold>
		break;
	case "dati_qualifica_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			//e di far chiudere l'elenco degli studenti
			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti_classe", $elenco_studenti_classe);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "dati_qualifica_update":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//e di far chiudere l'elenco degli studenti
			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			foreach($elenco_studenti_classe as $studente)
			{
				$id_studente = $studente["id_studente"];
				$voto_esame_sc1_qual = str_replace(",", ".", ${'voto_esame_sc1_qual_' . $id_studente});
				$voto_esame_sc2_qual = str_replace(",", ".", ${'voto_esame_sc2_qual_' . $id_studente});
				$voto_esame_or_qual = str_replace(",", ".", ${'voto_esame_or_qual_' . $id_studente});
				$giudizio_esame_sc1_qual = ${'giudizio_esame_sc1_qual_' . $id_studente};
				$giudizio_esame_sc2_qual = ${'giudizio_esame_sc2_qual_' . $id_studente};
				$giudizio_esame_or_qual = ${'giudizio_esame_or_qual_' . $id_studente};
				$giudizio_complessivo_esame_qual = ${'giudizio_complessivo_esame_qual_' . $id_studente};

				if(!is_numeric($voto_esame_sc1_qual))
				{
					$voto_esame_sc1_qual = 0;
				}

				if(!is_numeric($voto_esame_sc2_qual))
				{
					$voto_esame_sc2_qual = 0;
				}

				if(!is_numeric($voto_esame_or_qual))
				{
					$voto_esame_or_qual = 0;
				}

				$media_somma_esami = estrai_parametri_singoli("MEDIA_SOMMA_ESAMI");

				if($media_somma_esami == "SOMMA")
				{
					$voto_qualifica = round(($studente["voto_ammissione"]
							+
							$voto_esame_sc1_qual
							+
							$voto_esame_sc2_qual
							+
							$voto_esame_or_qual), 0);

					$stato = modifica_dati_esame_qualifica((int) $id_studente, $voto_esame_sc1_qual, $voto_esame_sc2_qual, $voto_esame_or_qual, $voto_qualifica, (int) $current_user, $giudizio_esame_sc1_qual, $giudizio_esame_sc2_qual, $giudizio_esame_or_qual, $giudizio_complessivo_esame_qual);
					$messaggio = "Valori inseriti correttamente!";
				}
				else
				{
					$percentuale_voto_esame_sc1_qual = estrai_parametri_singoli("PERCENTUALE_VOTO_ESAME_SC1_QUAL");
					$percentuale_voto_esame_sc2_qual = estrai_parametri_singoli("PERCENTUALE_VOTO_ESAME_SC2_QUAL");

					$media_voti_esami_pesata = round(
						(
							(
								( $voto_esame_sc1_qual * $percentuale_voto_esame_sc1_qual )
								+
								( $voto_esame_sc2_qual * $percentuale_voto_esame_sc2_qual )
							) / 100
						), 0
					);
					//$traduzione_voto_esami = traduci_voti_esami_qualifica_in_punti($media_voti_esami_pesata);
					$voto_qualifica = $studente["voto_ammissione"] + $media_voti_esami_pesata + $voto_esame_or_qual;
					$stato = modifica_dati_esame_qualifica((int) $id_studente, $voto_esame_sc1_qual, $voto_esame_sc2_qual, $voto_esame_or_qual, $voto_qualifica, (int) $current_user, $giudizio_esame_sc1_qual, $giudizio_esame_sc2_qual, $giudizio_esame_or_qual, $giudizio_complessivo_esame_qual);
					$messaggio = "Valori inseriti correttamente!";
				}
			}
			//e di far chiudere l'elenco degli studenti
			$template->assign("elenco_studenti_classe", $elenco_studenti_classe);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
			$template->assign("messaggio", $messaggio);
			$stato_secondario = "dati_qualifica_display";
		}
		//}}} </editor-fold>
		break;
	case "stampa_tabellone_pagelline_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			$periodo = $periodo_pagella_in_uso;
            $template->assign("param_tabellone_periodo", $periodo_pagella_in_uso);

			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
			$template->assign("coordinatore", $coordinatore);

			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
			$template->assign("periodo", $periodo);
			$template->assign("stato_tabellone", $stato_tabellone);

			$dati_classe = estrai_classe((int) $id_classe);
			$template->assign("dati_classe", $dati_classe);
			$template->assign("stato_ipsia_interno", $stato_ipsia_interno);

            $param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
            $stampa_tabellone_competenze = 'NO';
            if ($dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI')
            {
                $stampa_tabellone_competenze = 'SI';
            }
            $template->assign("stampa_tabellone_competenze", $stampa_tabellone_competenze);
            file_put_contents("/tmp/ll", print_r($stampa_tabellone_competenze, true));
			//estrazione dei parametri memorizzati a seconda del caso qualifiche o standard
			if($stato_ipsia_interno == "qualifiche")
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="per qualifiche e ammissioni">
				$tipo_stampa = "TABELLONE_TERZE_QUALIFICHE";
				$stringa_stampa = estrai_parametri_stampa($tipo_stampa);

				$parametri_stampa = explode("@", $stringa_stampa);
				$cont = 0;
				$array_parametri_finale = [];
				if(is_array($parametri_stampa))
				{
					foreach($parametri_stampa as $singolo_parametro)
					{
						$temp_array = explode("#", $singolo_parametro);

						$array_parametri_finale[$cont]["nome"] = $temp_array[0];
						$array_parametri_finale[$cont]["valore"] = $temp_array[1];

						$template->assign("param_tabellone_qualifiche_" . $array_parametri_finale[$cont]["nome"], $array_parametri_finale[$cont]["valore"]);
						$cont++;
					}
				}
				//}}} </editor-fold>
			}
			else
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="per tutti gli altri casi (tabelloni pagelle generali)">
				switch($dati_classe["classe"])
				{
					case "1":
						$tipo_stampa = "TABELLONE_PRIME";
						break;
					case "2":
						$tipo_stampa = "TABELLONE_SECONDE";
						break;
					case "3":
						$tipo_stampa = "TABELLONE_TERZE";
						break;
					case "4":
						$tipo_stampa = "TABELLONE_QUARTE";
						break;
					case "5":
						$tipo_stampa = "TABELLONE_QUINTE";
						break;
					default:
						$tipo_stampa = "TABELLONE_GENERICO";
						break;
				}

				$stringa_stampa = estrai_parametri_stampa($tipo_stampa);

				$parametri_stampa = explode("@", $stringa_stampa);
				$cont = 0;
				$array_parametri_finale = [];
				if(is_array($parametri_stampa))
				{
					foreach($parametri_stampa as $singolo_parametro)
					{
						$temp_array = explode("#", $singolo_parametro);
						$array_parametri_finale[$cont]["nome"] = $temp_array[0];
						$array_parametri_finale[$cont]["valore"] = $temp_array[1];
						$template->assign("param_tabellone_" . $array_parametri_finale[$cont]["nome"], $array_parametri_finale[$cont]["valore"]);
						$cont++;
					}
				}
				//}}} </editor-fold>
			}
		}
		//}}} </editor-fold>
		break;
	case "stampa_tabellone_pagelline_update":
		//{{{ <editor-fold defaultstate="collapsed">
		//estraggo i dati da passare alla stampa
		if($id_classe >= 0)
		{
			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);

			if($stato_tabellone == "coordinatore")
			{
				$periodo = $periodo_selezionato;
				require_once 'adm/include_stampa_tabellone.php';
			}
			else
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="caso di professore normale">
				$periodo = $periodo_selezionato;
				$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);

				$dati_scuola = estrai_dati_istituto();

				$commento_al_non_voto = estrai_parametri_singoli("COMMENTO_AL_NON_VOTO");

				$campi_liberi_stampa = estrai_parametri_singoli('CAMPI_LIBERI_STAMPA');

				$dati_classe = estrai_classe((int) $id_classe);

				if($formato_pagina == "A4")
				{
					if($orientamento_pagina == "P")
					{
						$larghezza_pagina = 210;
						$altezza_pagina = 290;
					}
					else
					{
						$larghezza_pagina = 290;
						$altezza_pagina = 203;
					}
				}
				else
				{
					if($orientamento_pagina == "P")
					{
						$larghezza_pagina = 290;
						$altezza_pagina = 405;
					}
					else
					{
						$larghezza_pagina = 420;
						$altezza_pagina = 290;
					}
				}

				//estraggo le materie della classe del professore a seconda che sia un coordinatore oppure un prof semplice
				$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe,(int) $id_professore);
				$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

                $altezza_cella = intval(count($elenco_studenti_classe)) > 0 ? 8 : 1;

				$dati_professore = estrai_dati_professore((int) $id_professore);

				$pdf = new NEXUS_PDF($orientamento_pagina, 'mm', $formato_pagina);
				$pdf->SetFillColor(224, 235, 255);
				$pdf->SetFont('helvetica', '', 8);

                $testo_excel = '';

                foreach($elenco_materie_classe as $materia_professore)
				{
					//{{{ <editor-fold defaultstate="collapsed">
					//estraggo la descrizione della materia che sto guardando
					$dati_materia = estrai_dati_materia((int) $materia_professore["id_materia"]);

					$significati_voto = estrai_significati_voti_pagelle($dati_materia[13], "solo_abilitati", $periodo);
					$elenco_voti_pagelline = estrai_voti_tabellone_pagellina_materia((int) $id_classe, $periodo, $elenco_studenti_classe, (int) $materia_professore["id_materia"]);
					$debiti_classe_materia = estrai_debiti_classe_materia((int) $id_classe, (int) $materia_professore["id_materia"], $anno_inizio . "/" . $anno_fine);

					$elenco_campi = estrai_codici_campi_liberi_con_valori($periodo);

					if(is_array($debiti_classe_materia))
					{
						foreach($debiti_classe_materia as $debito)
						{
							$elenco_voti_pagelline[$debito["id_studente"]]["debito"] = $debito["tipo_debito"];
						}
					}

					//Data loading
					$pdf->AddPage();
                    $testo_excel .= chr(13) . chr(10);
					//stampo l'intestazione della scuola
					if($orientamento_pagina == 'L')
					{
						inserisci_intestazione_pdf($pdf, (int) $id_classe, 37);
					}
					else
					{
						inserisci_intestazione_pdf($pdf, (int) $id_classe);
					}

					$pdf->ln(15);
					$pdf->SetFont('helvetica', '', 12);

                    if((intval($periodo) >= 1 && intval($periodo) <= 6) || (intval($periodo) >= 21 && intval($periodo) <= 26))
					{
						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI INFRA-QUADRIMESTRALE', 0, 1, 'L');
                        $testo_excel .= 'TABELLONE VOTI INFRA-QUADRIMESTRALE' . chr(13) . chr(10);
					}

					if(intval($periodo) == 7 || intval($periodo) == 27)
					{
						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI PRIMO QUADRIMESTRE/TRIMESTRE', 0, 1, 'L');
                        $testo_excel .= 'TABELLONE VOTI PRIMO QUADRIMESTRE/TRIMESTRE' . chr(13) . chr(10);
					}

					if(intval($periodo) == 8 || intval($periodo) == 28)
					{
						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI SECONDO TRIMESTRE', 0, 1, 'L');
                        $testo_excel .= 'TABELLONE VOTI SECONDO TRIMESTRE' . chr(13) . chr(10);
					}

					if(intval($periodo) == 9 || intval($periodo) == 29)
					{
						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI DI FINE ANNO', 0, 1, 'L');
                        $testo_excel .= 'TABELLONE VOTI DI FINE ANNO' . chr(13) . chr(10);
					}

					$pdf->CellFitScale($larghezza_pagina - 60, 10, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
					$pdf->CellFitScale(0, 10, 'A.S. ' . $anno_inizio . "/" . $anno_fine, 0, 1, 'R');
					$pdf->ln(2);
					$pdf->CellFitScale($larghezza_pagina - 40, 6, 'Docente: ' . $dati_professore[2] . " " . $dati_professore[1], 0, 1, 'L');
					$pdf->CellFitScale($larghezza_pagina - 40, 6, 'Materia: ' . $dati_materia['descrizione'], 0, 1, 'L');

                    $testo_excel .= 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
                    $testo_excel .= 'A.S. ' . $anno_inizio . "/" . $anno_fine . chr(13) . chr(10);
                    $testo_excel .= chr(13) . chr(10);
                    $testo_excel .= 'Docente: ' . $dati_professore[2] . " " . $dati_professore[1] . chr(13) . chr(10);
                    $testo_excel .= 'Materia: ' . $dati_materia['descrizione'] . chr(13) . chr(10);

					$larghezza = $larghezza_pagina - 20;
					$conta_campi = 0;
					$larghezza_studente = 30;
					$larghezza_registro = 5;
					$larghezza_voti = 30;
					$larghezza_assenze = 15;
					$larghezza_recuperi = 50;

					if(is_array($elenco_campi))
					{
						foreach($elenco_campi as $singolo_campo)
						{
							if($singolo_campo['visibile'] == 'SI' && (in_array($materia_professore["id_materia"], $singolo_campo['abbinamenti']['materie']) || isset($singolo_campo['abbinamenti']['materie'][0])))
                            {
								$conta_campi++;
							}
						}
					}

					if($conta_campi > 0)
					{
						$larghezza_campi_liberi = round((($larghezza - ($larghezza_studente + $larghezza_registro + $larghezza_voti + $larghezza_recuperi)) / ($conta_campi + 1)), 3);
					}

					$pdf->SetFont('helvetica', 'B', $dimensione_font);
                    $pdf->CellFitScale($larghezza_registro, 8, 'N°', 1, 0, 'C', $fill);
					$pdf->CellFitScale($larghezza_studente, 8, 'STUDENTE', 1, 0, 'C', $fill);
					$pdf->CellFitScale($larghezza_voti, 8, 'VOTI', '1', 0, 'C', $fill);
					$pdf->CellFitScale($larghezza_assenze, 8, 'ASS.', '1', 0, 'C', $fill);
					$pdf->CellFitScale($larghezza_recuperi, 8, 'TIPO RECUPERO', '1', 0, 'C', $fill);

                    $testo_excel .= 'N°' . chr(9) . 'STUDENTE' .chr(9) . 'VOTI' . chr(9) . 'ASS' . chr(9) . 'TIPO RECUPERO';

					if(is_array($elenco_campi))
					{
						foreach($elenco_campi as $singolo_campo)
						{
							if($singolo_campo['visibile'] == 'SI' && (in_array($materia_professore["id_materia"], $singolo_campo['abbinamenti']['materie']) || isset($singolo_campo['abbinamenti']['materie'][0])))
                            {
								$pdf->CellFitScale($larghezza_campi_liberi, 8, decode($singolo_campo["nome"]), '1', 0, 'C', $fill);
                                $testo_excel .= chr(9) . decode($singolo_campo["nome"]);
							}
						}
					}

					$pdf->CellFitScale(0, 8, '', '0', 1);
                    $testo_excel .= chr(13) . chr(10);

					foreach($elenco_voti_pagelline as $studente)
					{
                        $altezza_cella_tmp = $altezza_cella;
                        // calcolo l'altezza della cella maggiorata se necessario
                        if(is_array($studente["campi_liberi"]))
						{
							foreach($studente["campi_liberi"] as $campi_liberi)
							{
								if ($campi_liberi['visibile'] == 'SI' && is_array($campi_liberi["valori_precomp"]) && (in_array($materia_professore["id_materia"], $campi_liberi['abbinamenti']['materie']) || isset($campi_liberi['abbinamenti']['materie'][0]))) {

                                    foreach ($campi_liberi["valori_precomp"] as $dati_valori_precomp) {
                                        if ($dati_valori_precomp["selezionato"] == "SI") {

                                            if ($dati_valori_precomp["id_valore_precomp"] == "-1") {
                                                if (strlen($campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]) > 50) {
                                                    if($formato_pagina == "A4") {
                                                        $pdf->SetFont('helvetica', '', 4);
                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"])*2;
                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
                                                    } else {
                                                        $pdf->SetFont('helvetica', '', 6);
                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"])*3;
                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
                                                    }
                                                }
                                            } else {
                                                if (strlen($dati_valori_precomp[$campi_liberi_stampa]) > 50) {
                                                    if($formato_pagina == "A4") {
                                                        $pdf->SetFont('helvetica', '', 4);
                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa])*2;
                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
                                                    } else {
                                                        $pdf->SetFont('helvetica', '', 6);
                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa])*3;
                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
							}
						}

						$fill = !$fill;
						$pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
                        $pdf->CellFitScale($larghezza_registro, $altezza_cella, $studente["registro"], 1, 0, 'C', $fill);
						$pdf->CellFitScale($larghezza_studente, $altezza_cella, $studente["cognome"] . " " . $studente["nome"], 1, 0, 'L', $fill);
                        $testo_excel .= $studente["registro"] . chr(9) . $studente["cognome"] . " " . $studente["nome"] . chr(9);
						$voto_valore = "";
						$voto_scritto_pagella = "";
						$voto_orale_pagella = "";
						$voto_pratico_pagella = "";

						if($tipo_visualizzazione_voti == 'personalizzato')
						{
							if($studente['tipo_voto_personalizzato'] == '1')
							{
								$check_tipo = "voto_singolo";
							}

							if($studente['tipo_voto_personalizzato'] == '3')
							{
								$check_tipo = "scritto_orale_pratico";
							}

							if($studente['tipo_voto_personalizzato'] == '2')
							{
								$check_tipo = "scritto_orale";
							}
						}
						else
						{
							$check_tipo = $tipo_visualizzazione_voti;
						}

						if($check_tipo == "voto_singolo")
						{
							foreach($significati_voto as $significato)
							{
								if($significato["voto"] == $studente["voto_pagellina"])
								{
									switch($tipo_voto_stampa)
									{
										case "scheda":
											$voto_valore = $studente["voto_pagellina"];
											break;
										case "codice":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_valore = $significati_tmp["codice_pagella"];
											break;
										case "descrizione":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_valore = $significati_tmp["valore_pagella"];
											break;
										default:
											$voto_valore = $studente["voto_pagellina"];
											break;
									}
								}
							}
						}
						else
						{
							foreach($significati_voto as $significato)
							{
								if($significato["voto"] == $studente["voto_scritto_pagella"])
								{
									switch($tipo_voto_stampa)
									{
										case "scheda":
											$voto_scritto_pagella = $studente["voto_scritto_pagella"];
											break;
										case "codice":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_scritto_pagella = $significati_tmp["codice_pagella"];
											break;
										case "descrizione":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_scritto_pagella = $significati_tmp["valore_pagella"];
											break;
										default:
											$voto_scritto_pagella = $studente["voto_scritto_pagella"];
											break;
									}
								}

								if($significato["voto"] == $studente["voto_orale_pagella"])
								{
									switch($tipo_voto_stampa)
									{
										case "scheda":
											$voto_orale_pagella = $studente["voto_orale_pagella"];
											break;
										case "codice":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_orale_pagella = $significati_tmp["codice_pagella"];
											break;
										case "descrizione":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_orale_pagella = $significati_tmp["valore_pagella"];
											break;
										default:
											$voto_orale_pagella = $studente["voto_scritto_pagella"];
											break;
									}
								}

								if($significato["voto"] == $studente["voto_pratico_pagella"])
								{
									switch($tipo_voto_stampa)
									{
										case "scheda":
											$voto_pratico_pagella = $studente["voto_pratico_pagella"];
											break;
										case "codice":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_pratico_pagella = $significati_tmp["codice_pagella"];
											break;
										case "descrizione":
											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
											$voto_pratico_pagella = $significati_tmp["valore_pagella"];
											break;
										default:
											$voto_pratico_pagella = $studente["voto_pratico_pagella"];
											break;
									}
								}
							}

							if(strlen($voto_scritto_pagella) > 0)
							{
								$voto_valore .= "Scr. " . $voto_scritto_pagella;
							}

							if(strlen($voto_orale_pagella) > 0)
							{
								$voto_valore .= "  -  " . "Or. " . $voto_orale_pagella;
							}

							if(strlen($voto_pratico_pagella) > 0)
							{
								$voto_valore .= "  -  " . "Pr. " . $voto_pratico_pagella;
							}
						}

						$pdf->SetFont('helvetica', '', $dimensione_font);
                        $pdf->CellFitScale($larghezza_voti, $altezza_cella, $voto_valore, '1', 0, 'C', $fill);
						$pdf->CellFitScale($larghezza_assenze, $altezza_cella, stampa_ore_o_minuti($studente["ore_assenza"]), '1', 0, 'C', $fill);
						$pdf->CellFitScale($larghezza_recuperi, $altezza_cella, estrai_tipo_recupero_singolo($studente["tipo_recupero"]), '1', 0, 'C', $fill);
                        $testo_excel .= $voto_valore . chr(9) . stampa_ore_o_minuti($studente["ore_assenza"]) . chr(9) . estrai_tipo_recupero_singolo($studente["tipo_recupero"]);

                        if(is_array($studente["campi_liberi"]))
						{
							foreach($studente["campi_liberi"] as $campi_liberi)
							{
								if ($campi_liberi['visibile'] == 'SI' && is_array($campi_liberi["valori_precomp"]) && (in_array($materia_professore["id_materia"], $campi_liberi['abbinamenti']['materie']) || isset($campi_liberi['abbinamenti']['materie'][0]))) {
                                    $trovato_campo = false;

                                    foreach ($campi_liberi["valori_precomp"] as $dati_valori_precomp) {
                                        if ($dati_valori_precomp["selezionato"] == "SI") {
                                            $trovato_campo = true;

                                            if ($dati_valori_precomp["id_valore_precomp"] == "-1") {
                                                if (strlen($campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]) > 50) {
                                                    if($formato_pagina == "A4") {
                                                        $pdf->SetFont('helvetica', '', 4);
                                                    } else {
                                                        $pdf->SetFont('helvetica', '', 6);
                                                    }
                                                    $tot_righe = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]);
                                                    $pdf->MultiCell($larghezza_campi_liberi, $altezza_cella, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"], 1, 'L', false, 0);
                                                    $testo_excel .= chr(9) . $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"];
                                                } else {
                                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"], '1', 0, 'C', $fill);
                                                    $testo_excel .= chr(9) . $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"];
                                                }
                                            } else {
                                                if (strlen($dati_valori_precomp[$campi_liberi_stampa]) > 50) {
                                                    if($formato_pagina == "A4") {
                                                        $pdf->SetFont('helvetica', '', 4);
                                                    } else {
                                                        $pdf->SetFont('helvetica', '', 6);
                                                    }
                                                    $tot_righe = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa]);
                                                    $pdf->MultiCell($larghezza_campi_liberi, $altezza_cella, $dati_valori_precomp[$campi_liberi_stampa], 1, 'L', false, 0);
                                                    $testo_excel .= chr(9) . $dati_valori_precomp[$campi_liberi_stampa];
                                                }
                                                else
                                                {
                                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, $dati_valori_precomp[$campi_liberi_stampa], '1', 0, 'C', $fill);
                                                    $testo_excel .= chr(9) . $dati_valori_precomp[$campi_liberi_stampa];
                                                }
                                                $valore_totale_campi_liberi_studente += $dati_valori_precomp['valore'];
                                            }
                                        }
                                    }

                                    if(!$trovato_campo)
                                    {
                                        $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, "", '1', 0, 'C', $fill);
                                        $testo_excel .= chr(13) . chr(10);
                                    }
                                }

//                                if(!$trovato_campo)
//                                {
//                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, "", '1', 0, 'C', $fill);
//                                }
							}
						}

						$pdf->CellFitScale(0, $altezza_cella, '', '0', 1, 'C');
                        $testo_excel .= chr(13) . chr(10);

                        $altezza_cella = $altezza_cella_tmp;
					}
					//}}} </editor-fold>
				}
				//}}} </editor-fold>
			}

			switch($tipo_file_esportato)
			{
                //{{{ <editor-fold defaultstate="collapsed" desc="Tipo file esportato">
				case "pdf":
					$pdf->Output($stato_secondario . '_' . date('Y-m-d_H-i') . ".pdf", "D");
					exit;
					break;
				case "xls":
					//cancello tutte i file temporanei fatti da più di un'ora
					$dir = "tmp_xls";
					CleanFiles($dir);
					//creo i nuovi file temporanei
					$file = basename(tempnam($dir, 'tmp'));
					rename($dir . "/" . $file, $dir . "/" . $file . '.xls');
					$file.='.xls';
					//Salva il file xls come file
					$nuovo_nome = $dir . '/' . $file;
					$handle = fopen($nuovo_nome, "w");
					fwrite($handle, $testo_excel);
					fclose($handle);
					//Reindirizzamento JavaScript
					echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
					break;
                //}}} </editor-fold>
			}
		}
		//}}} </editor-fold>
		break;
    case "stampa_tabellone_competenze_pagelline_update":
		//{{{ <editor-fold defaultstate="collapsed">
                    file_put_contents("/tmp/id", print_r($id_classe,true));

		if($id_classe != '')
		{
			$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
			if($coordinatore == "SI")
			{
				$periodo = $periodo_selezionato;
				require_once 'adm/include_stampa_tabellone_competenze.php';
			}
            else
            {
                $formato_pagina = "A3";
                //$formato_pagina = "A4";
                $orientamento_pagina = "L";
                //$orientamento_pagina = "P";
                $f = 'helvetica';
                //$fd = 8;
                $w_col_competenze = 65;
                //$w_col = 20;
                $h_intestazione = 40;
                $color_header = [67,113,155];
                $color_materie = [187, 203, 219];
                $color_row = [244, 239, 234];
                $periodo = $param_tabellone_periodo;
                $data_selezionata_finale = mktime(1,1,1,intval($data_finale_Month),intval($data_finale_Day),intval($data_finale_Year));

                $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
                $definizione_dirigente = estrai_parametri_singoli('DEFINIZIONE_DIRIGENTE');
                $dati_classe = estrai_classe((int) $id_classe);
                if(intval($dati_classe["id_sede"]) > 0)
                {
                    $dati_sede = estrai_sede_singola($dati_classe["id_sede"]);
                    $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];
                    $indirizzo_scuola = $dati_sede["indirizzo"];
                    $dati_classe['descrizione_comuni'] = $dati_sede["descrizione_comune"];
                    $telefono = $dati_sede["telefono"];
                    $dirigente_scolastico = $dati_sede["nome_dirigente"];
                }

                $array_generale = estrai_tabellone_classe_competenze($id_classe, $periodo, $current_user, $current_key);
                $elenco_materie_prof = estrai_materie_classe_del_professore((int) $id_classe,(int) $id_professore);

                if($formato_pagina == "A4")
                {
                    if($orientamento_pagina == "P")
                    {
                        $larghezza_pagina = 210;
                        $altezza_pagina = 290;
                        $divisione_cella = 5;
                    }
                    else
                    {
                        $larghezza_pagina = 290;
                        $altezza_pagina = 203;
                        $divisione_cella = 7;
                    }
                }
                else
                {
                    if($orientamento_pagina == "P")
                    {
                        $larghezza_pagina = 290;
                        $altezza_pagina = 405;
                        $divisione_cella = 8;
                    }
                    else
                    {
                        $larghezza_pagina = 405;
                        $altezza_pagina = 290;
                        $divisione_cella = 9;
                    }
                }

                $titolo_stampa = 'TABELLONE VOTI COMPETENZE';
                if(((intval($periodo) >= 1) && (intval($periodo) <= 6)) || ((intval($periodo) >= 21) && (intval($periodo) <= 26)))
                {
                    $titolo_stampa .= ' INFRA-QUADRIMESTRALE';
                }
                if((intval($periodo) == 7) || (intval($periodo) == 27))
                {
                    $titolo_stampa .= ' PRIMO QUADRIMESTRE/TRIMESTRE';
                }
                if((intval($periodo) == 8) || (intval($periodo) == 28))
                {
                    $titolo_stampa .= ' SECONDO TRIMESTRE';
                }
                if((intval($periodo) == 9) || (intval($periodo) == 29))
                {
                    $titolo_stampa .= ' FINE ANNO';
                }
                $as = 'A.S. ' . $anno_scolastico;

                //{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
                $pdf = new MASTERCOM_PDF($orientamento_pagina, 'mm', $formato_pagina);
                //$dimensioni_stampa_intestazione = estrai_parametri_singoli('SCALA_INTESTAZIONE_STAMPE');
                //$logo = estrai_parametri_singoli('LOGO_NOME', $id_classe, 'classe');
                //$target_width = 1.8 * intval($dimensioni_stampa_intestazione == 'NON_ESISTE' ? 100 : $dimensioni_stampa_intestazione);
                //if($logo != '' && file_exists("/var/www-source/mastercom/immagini_scuola/" . $logo))
                //{
                //    $logo = "/var/www-source/mastercom/immagini_scuola/" . $logo;
                //    $img_estensione = pathinfo($logo)['extension'];
                //    $img_estensione = stripos($img_estensione, 'jpg') !== false ?  '' :$img_estensione;
                //    $pdf->Image($logo, 0, 0, $target_width, 50, $img_estensione,'', 'N', false, 300, 'C', false, false, 0, 'CM');
                //    $pdf->SetHeaderData($logo, $target_width, 50, 'C', $ht='', $htfn='', $htfd='', $hs='', $hsfn='', $hsfd='', $tc=array(0,0,0), $ld=0.25, $lc=array(0,0,0), true);
                //}
                //else
                //{
                //    $pdf->SetHeaderData('', 0, 25, 'C', $ht=$intestazione_stampa, $htfn=$f, $htfd=12, $hs=$dati_sede["indirizzo"] . " -- " . $dati_classe['descrizione_comuni'] . " -- " . $telefono, $hsfn='', $hsfd=12, $tc=array(0,0,0), $ld=0.25, $lc=array(0,0,0), true);
                //}
                $pdf->AddPage();
                inserisci_intestazione_pdf($pdf, $id_classe);
                $pdf->SetAutoPageBreak(true, 0);

                $pdf->SetFont($f, '', $fd+4);
                $pdf->Cell(0,8,$titolo_stampa,0,1,'C');
                $pdf->Cell(0,8,'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5],0,0,'L');
                $pdf->Cell(0,8,$as,0,1,'R');
                //}}} </editor-fold>

                //{{{ <editor-fold defaultstate="collapsed" desc="tabellone">
                // header
                $pdf->SetFont($f, 'B', $fd);
                $pdf->SetFillColor($color_header[0], $color_header[1], $color_header[2]);
                $pdf->MultiCell($w_col_competenze, $h_intestazione,"Competenze / Obiettivi", 1, 'L', $fill=true, 0, '', '', true, 1, false, true, $h_intestazione, 'M', true);
                foreach ($array_generale['elenco_studenti'] as $studente) {
                    $x_base = $pdf->GetX();
                    $y_base = $pdf->GetY();
                    $testo = decode("{$studente['cognome']} {$studente['nome']}");
                    $pdf->StartTransform();
                    $pdf->Rotate(90);
                    $pdf->SetXY($x_base- $h_intestazione, $y_base);
                    $pdf->MultiCell($h_intestazione, $w_col, $testo, 1, 'L', $fill=true, 1, '', '', true, 1, false, true, $w_col, 'M', true);
                    $pdf->StopTransform();
                    $pdf->SetXY($x_base + $w_col, $y_base);
                }
                $pdf->CellFitScale(0,$h_intestazione,'',0,1);
                //voti
                foreach ($array_generale['elenco_materie'] as $materia)
                {
                    echo_debug($materia);
                    foreach ($elenco_materie_prof as $materia_prof)
                    {
                        if($materia['id_materia'] == $materia_prof['id_materia'] )
                        {
                            $desc_materia = decode($materia['descrizione']);
                            $h_row = $pdf->getStringHeight($w_col_competenze, $desc_materia, false, true, '', 0 );
                            if ($pdf->GetY()+$h_row > $altezza_pagina)
                            {
                                $pdf->AddPage();
                                inserisci_intestazione_pdf($pdf, $id_classe);

                                // header
                                $pdf->SetFont($f, 'B', $fd);
                                $pdf->SetFillColor($color_header[0], $color_header[1], $color_header[2]);
                                $pdf->MultiCell($w_col_competenze, $h_intestazione,"Competenze / Obiettivi", 1, 'L', $fill=true, 0, '', '', true, 1, false, true, $h_intestazione, 'M', true);
                                foreach ($array_generale['elenco_studenti'] as $studente)
                                {
                                    $x_base = $pdf->GetX();
                                    $y_base = $pdf->GetY();
                                    $testo = decode("{$studente['cognome']} {$studente['nome']}");
                                    $pdf->StartTransform();
                                    $pdf->Rotate(90);
                                    $pdf->SetXY($x_base- $h_intestazione, $y_base);
                                    $pdf->MultiCell($h_intestazione, $w_col, $testo, 1, 'L', $fill=true, 1, '', '', true, 1, false, true, $w_col, 'M', true);
                                    $pdf->StopTransform();
                                    $pdf->SetXY($x_base + $w_col, $y_base);
                                }
                                $pdf->CellFitScale(0,$h_intestazione,'',0,1);
                            }

                            $pdf->SetFillColor($color_materie[0], $color_materie[1], $color_materie[2]);
                            $pdf->SetFont($f, 'B', $fd);
                            $pdf->MultiCell($w_col_competenze, $h_row, $desc_materia, 1, 'L', $fill=true, 0, '', '', true, 1, false, true, $h_row, 'M', true);
                            foreach ($array_generale['elenco_studenti'] as $studente) {
                                $perc_assenze = '';
                                if ($materia['voti_pagelline'][$studente['id_studente']][$periodo]['percentuale_assenze']) {
                                    $perc_assenze = $materia['voti_pagelline'][$studente['id_studente']][$periodo]['percentuale_assenze'];
                                }
                                $pdf->MultiCell($w_col, $h_row, $perc_assenze, 1, 'C', $fill=true, 0, '', '', true, 1, false, true, $h_row, 'M', true);
                            }
                            $pdf->CellFitScale(0,$h_row,'',0,1);
                            $pdf->SetFont($f, '', $fd);

                            $pdf->SetFillColor($color_row[0], $color_row[1], $color_row[2]);
                            $fl = false;
                            if (count($materia['elenco_competenze']) > 0)
                            {
                                foreach($materia['elenco_competenze'] as $competenze_template)
                                {
                                    foreach ($competenze_template as $competenza) {
                                        $desc_comp = "{$competenza['codice']}";
                        //                $desc_comp = "{$competenza['codice']}\n{$competenza['descrizione']}";
                                        $nest = str_repeat(' ', $competenza['nesting']);
                                        $desc_comp = decode("$nest $desc_comp");
                                        $h_row = $pdf->getStringHeight($w_col_competenze, $desc_comp, false, true, '', 0 );

                                        if ($pdf->GetY()+$h_row > $altezza_pagina) {
                                            $pdf->AddPage();
                                            inserisci_intestazione_pdf($pdf, $id_classe);

                                            // header
                                            $pdf->SetFont($f, 'B', $fd);
                                            $pdf->SetFillColor($color_header[0], $color_header[1], $color_header[2]);
                                            $pdf->MultiCell($w_col_competenze, $h_intestazione,"Competenze / Obiettivi", 1, 'L', $fill=true, 0, '', '', true, 1, false, true, $h_intestazione, 'M', true);
                                            foreach ($array_generale['elenco_studenti'] as $studente) {
                                                $x_base = $pdf->GetX();
                                                $y_base = $pdf->GetY();
                                                $testo = decode("{$studente['cognome']} {$studente['nome']}");
                                                $pdf->StartTransform();
                                                $pdf->Rotate(90);
                                                $pdf->SetXY($x_base- $h_intestazione, $y_base);
                                                $pdf->MultiCell($h_intestazione, $w_col, $testo, 1, 'L', $fill=true, 1, '', '', true, 1, false, true, $w_col, 'M', true);
                                                $pdf->StopTransform();
                                                $pdf->SetXY($x_base + $w_col, $y_base);
                                            }
                                            $pdf->CellFitScale(0,$h_intestazione,'',0,1);
                                            $pdf->SetFont($f, '', $fd);
                                            $pdf->SetFillColor($color_row[0], $color_row[1], $color_row[2]);
                                        }
                                        $pdf->MultiCell($w_col_competenze, $h_row, "$nest $desc_comp", 1, 'L', $fl, 0, '', '', true, 1, false, true, $h_row, 'M', true);

                                        foreach ($array_generale['elenco_studenti'] as $studente) {
                                            $val = '';
                                            if ($competenza['elenco_studenti'][$studente['id_studente']]) {
                                                $val = decode($competenza['elenco_studenti'][$studente['id_studente']]['scrutinio'][$periodo]['voto']['codice']);
                                            }
                                            $pdf->MultiCell($w_col, $h_row, $val, 1, 'C', $fl, 0, '', '', true, 1, false, true, $h_row, 'M', true);
                                        }
                                        $pdf->CellFitScale(0,$h_row,'',0,1);
                                        $fl = !$fl;
                                    }
                                }
                            }
                        }
                    }
                }

                $pdf->SetFont($f, '', 8);
                //{{{ <editor-fold defaultstate="collapsed" desc="firme e pie pagina">
                if($param_tabellone_stampa_firme == 'SI_FIRME_SINGOLE')
                {
                    //{{{ <editor-fold defaultstate="collapsed">
                    $y_base = $pdf->GetY() + 3;
                    $pdf->SetXY(10,$y_base);
                    $elenchi_prof = [];
                    $elenco_classi = [];

                    if($multi_classe_pagelle == 'SI')
                    {
                        $elenco_classi = estrai_multi_classi_da_classe((int) $id_classe);
                    }
                    else
                    {
                        $elenco_classi[0]['id_classe'] = $id_classe;
                    }

                    foreach($elenco_classi as $classe)
                    {
                        $prof_classe = estrai_abbinamenti((int) $classe['id_classe'], 'classe');
                        $arr_prof = [];
                        foreach ($prof_classe as $professore_singolo)
                        {
                            if ($professore_singolo['tipo_materia'] != 'SOSTEGNO')
                            {
                                $arr_prof[] = $professore_singolo;
                        }
                        }
                        $elenchi_prof[] = $arr_prof;
                    }

                    $elenchi_prof[] = estrai_docenti_sostegno_classe($id_classe);
                    $elenco_finale_prof = [];
                    $cont_prof_int = 0;

                    foreach($elenchi_prof as $elenco_prof)
                    {
                        foreach($elenco_prof as $prof_singolo)
                        {
                            if($prof_singolo['tipo_materia'] != 'CONDOTTA')
                            {
                                $esiste_prof = 'NO';

                                foreach($elenco_finale_prof as $elemento)
                                {
                                    if($elemento['id_professore'] == $prof_singolo['id_professore'])
                                    {
                                        $esiste_prof = 'SI';
                                    }
                                }

                                if($esiste_prof == 'NO' && ($prof_singolo['in_media_pagelle'] != 'NV' || $param_tabellone_firme_materie_NV == 'SI'))
                                {
                                    $sostituzione = sostituisci_docente_consiglio($prof_singolo['id_classe'], $periodo, $prof_singolo['id_professore']);

                                    $elenco_finale_prof[$cont_prof_int]['id_classe'] = $prof_singolo['id_classe'];
                                    $elenco_finale_prof[$cont_prof_int]['id_professore'] = $prof_singolo['id_professore'];
                                    $elenco_finale_prof[$cont_prof_int]['cognome'] = $prof_singolo['cognome'];
                                    $elenco_finale_prof[$cont_prof_int]['nome'] = $prof_singolo['nome'];
                                    $elenco_finale_prof[$cont_prof_int]['in_media_pagelle'] = $prof_singolo['in_media_pagelle'];
                                    $elenco_finale_prof[$cont_prof_int]['presenza'] = $sostituzione['stato'];

                                    if ($sostituzione['stato'] == 'S')
                                    {
                                        $elenco_finale_prof[$cont_prof_int]['cognome_sostituto'] = $sostituzione['cognome'];
                                        $elenco_finale_prof[$cont_prof_int]['nome_sostituto'] = $sostituzione['nome'];
                                    }
                                    $cont_prof_int++;
                                }
                            }
                        }
                    }

                    file_put_contents('/tmp/finale', print_r($elenco_finale_prof, true));

                    $cont_prof = 0;
                    $pdf->Cell(0,2,'',0,1,'C');
                    $x_base_tmp = $pdf->GetX();
                    $y_base_tmp = $pdf->GetY();
                    $pdf->SetXY($x_base_tmp,$y_base_tmp);

                    foreach($elenco_finale_prof as $prof)
                    {
                        $nome_sost = $prof['presenza'] == 'S' ? "(Sost. " . decode($prof['cognome_sostituto']) . " " . decode($prof['nome_sostituto']) . ")" : "";
                        $nome_sost = $prof['presenza'] == 'A' ? "(Assente)" : $nome_sost;

                        $nome_prof = $prof['cognome'] . ' ' . $prof['nome'];
                        $prossima_larghezza_raggiunta = (($cont_prof + 1) * 35);

                        if($prossima_larghezza_raggiunta > ($larghezza_pagina - 20))
                        {
                            $y_base = $pdf->GetY() + 11;
                            $pdf->SetXY(10,$y_base);
                            $cont_prof = 0;
                        }

                        $cont_prof++;
                        $x_base = $pdf->GetX();
                        $y_base = $pdf->GetY();
                        $pdf->CellFitScale(33,2,'__________________________________',0,0,'C');
                        $x_finale = $pdf->GetX();
                        $y_finale = $pdf->GetY();
                        $pdf->SetXY($x_base,$y_base+3);
                        $pdf->CellFitScale(33,4,$nome_prof,0,0,'C');
                        $pdf->CellFitScale(2,4,'',0,0,'C');
                        if ($param_tabellone_stampa_presenze == 'SI') {
                            $pdf->SetXY($x_base,$y_base+6);
                            $pdf->CellFitScale(33,4,$nome_sost,0,0,'C');
                            $pdf->CellFitScale(2,4,'',0,0,'C');
                        }
                        $pdf->SetXY($x_finale,$y_finale);
                    }

                    $pdf->Cell(0,3,'',0,1,'C');
                    //}}} </editor-fold>
                }

                if($param_tabellone_visualizza_data == "SI_D")
                {
                    $y_base = $pdf->GetY() + 5;
                    $pdf->SetXY(10,$y_base);
                    //$pdf->ln(3);
                    $pdf->SetFont($f, 'B', 8);
                    $pdf->CellFitScale($larghezza_pagina - 100,4, $dati_classe['descrizione_comuni'] . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
                }
                elseif($param_tabellone_visualizza_data == "SI_D_DS")
                {
                    $y_base = $pdf->GetY() + 5;
                    $pdf->SetXY(10,$y_base);
                    //$pdf->ln(3);
                    $pdf->SetFont($f, 'B', 8);
                    $pdf->CellFitScale($larghezza_pagina - 100,4, $dati_classe['descrizione_comuni'] . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
                    $pdf->CellFitScale(0,5,$definizione_dirigente,0,1,'C');
                    $pdf->CellFitScale($larghezza_pagina - 100,4,'',0,0,'L');
                    $pdf->CellFitScale(0,5, $dati_sede["nome_dirigente"],0,0,'C');
                    if ($param_pagellina_firma_digitale == 'SI') {
                        $pdf->ln();
                        $pdf->SetFont($f, 'I', 7);
                        $pdf->CellFitScale($larghezza_pagina - 100,4,'',0,0,'L');
                        $pdf->CellFitScale(0,5, "(Documento firmato digitalmente)",0,0,'C');
                        $pdf->SetFont($f, 'B', 8);
                    }
                }
                elseif($param_tabellone_visualizza_data == "SI_D_DS_C")
                {
                    $coordinatore = estrai_coordinatore((int) $id_classe);
                    $y_base = $pdf->GetY() + 5;
                    $pdf->SetXY(10,$y_base);
                    //$pdf->ln(3);
                    $pdf->SetFont($f, 'B', 8);
                    $pdf->CellFitScale($larghezza_pagina/3,4, '',0,0,'L');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,4, $definizione_dirigente,0,0,'C');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,4, 'Il Coordinatore di Classe',0,1,'C');
                    $pdf->CellFitScale($larghezza_pagina/3,2, $dati_classe['descrizione_comuni'] . ' lì ' . date ("d/m/Y",$data_selezionata_finale),0,0,'L');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,2, '_____________________________',0,0,'C');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,2, '_____________________________',0,1,'C');
                    $pdf->CellFitScale($larghezza_pagina/3,4, '',0,0,'L');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,4, $dati_sede["nome_dirigente"],0,0,'C');
                    $pdf->CellFitScale(($larghezza_pagina - 20)/3,4, "Prof. " . $coordinatore["cognome"] . " " . $coordinatore["nome"],0,0,'C');
                    if ($param_pagellina_firma_digitale == 'SI') {
                        $pdf->ln();
                        $pdf->SetFont($f, 'I', 7);
                        $pdf->CellFitScale($larghezza_pagina/3,4, '',0,0,'L');
                        $pdf->CellFitScale(($larghezza_pagina - 20)/3,4, "(Documento firmato digitalmente)" , 0, 0, 'C');
                        $pdf->SetFont($f, 'B', 8);
                    }
                }

                if ($param_pagellina_firma_omessa == 'SI') {
                    $pdf->SetFont($f, '', $dimensione_font);
                    $pdf->ln(3);
                    $pdf->CellFitScale(0, 5, 'Firma autografa omessa ai sensi dell’art. 3 del D. Lgs. n. 39/1993', 0, 1, 'L');
                    $pdf->SetFont($f, 'B', 8);
                }
                //}}} </editor-fold>
                $pdf->Output('stampa_tabellone_competenze_'. date('Y-m-d_H-i') .".pdf","D");
exit;
            }
//			else
//			{
//				//{{{ <editor-fold defaultstate="collapsed" desc="caso di professore normale">
//				$periodo = $periodo_selezionato;
//				$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
//
//				$dati_scuola = estrai_dati_istituto();
//
//				$commento_al_non_voto = estrai_parametri_singoli("COMMENTO_AL_NON_VOTO");
//
//				$campi_liberi_stampa = estrai_parametri_singoli('CAMPI_LIBERI_STAMPA');
//
//				$dati_classe = estrai_classe((int) $id_classe);
//
//				if($formato_pagina == "A4")
//				{
//					if($orientamento_pagina == "P")
//					{
//						$larghezza_pagina = 210;
//						$altezza_pagina = 290;
//					}
//					else
//					{
//						$larghezza_pagina = 290;
//						$altezza_pagina = 203;
//					}
//				}
//				else
//				{
//					if($orientamento_pagina == "P")
//					{
//						$larghezza_pagina = 290;
//						$altezza_pagina = 405;
//					}
//					else
//					{
//						$larghezza_pagina = 420;
//						$altezza_pagina = 290;
//					}
//				}
//
//				//estraggo le materie della classe del professore a seconda che sia un coordinatore oppure un prof semplice
//				$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe,(int) $id_professore);
//				$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);
//
//                $altezza_cella = intval(count($elenco_studenti_classe)) > 0 ? 8 : 1;
//
//				$dati_professore = estrai_dati_professore((int) $id_professore);
//
//				$pdf = new NEXUS_PDF($orientamento_pagina, 'mm', $formato_pagina);
//				$pdf->SetFillColor(224, 235, 255);
//				$pdf->SetFont('helvetica', '', 8);
//
//                $testo_excel = '';
//
//                foreach($elenco_materie_classe as $materia_professore)
//				{
//					//{{{ <editor-fold defaultstate="collapsed">
//					//estraggo la descrizione della materia che sto guardando
//					$dati_materia = estrai_dati_materia((int) $materia_professore["id_materia"]);
//
//					$significati_voto = estrai_significati_voti_pagelle($dati_materia[13], "solo_abilitati", $periodo);
//					$elenco_voti_pagelline = estrai_voti_tabellone_pagellina_materia((int) $id_classe, $periodo, $elenco_studenti_classe, (int) $materia_professore["id_materia"]);
//					$debiti_classe_materia = estrai_debiti_classe_materia((int) $id_classe, (int) $materia_professore["id_materia"], $anno_inizio . "/" . $anno_fine);
//
//					$elenco_campi = estrai_codici_campi_liberi_con_valori($periodo);
//
//					if(is_array($debiti_classe_materia))
//					{
//						foreach($debiti_classe_materia as $debito)
//						{
//							$elenco_voti_pagelline[$debito["id_studente"]]["debito"] = $debito["tipo_debito"];
//						}
//					}
//
//					//Data loading
//					$pdf->AddPage();
//                    $testo_excel .= chr(13) . chr(10);
//					//stampo l'intestazione della scuola
//					if($orientamento_pagina == 'L')
//					{
//						inserisci_intestazione_pdf($pdf, (int) $id_classe, 37);
//					}
//					else
//					{
//						inserisci_intestazione_pdf($pdf, (int) $id_classe);
//					}
//
//					$pdf->ln(15);
//					$pdf->SetFont('helvetica', '', 12);
//
//                    if((intval($periodo) >= 1 && intval($periodo) <= 6) || (intval($periodo) >= 21 && intval($periodo) <= 26))
//					{
//						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI INFRA-QUADRIMESTRALE', 0, 1, 'L');
//                        $testo_excel .= 'TABELLONE VOTI INFRA-QUADRIMESTRALE' . chr(13) . chr(10);
//					}
//
//					if(intval($periodo) == 7 || intval($periodo) == 27)
//					{
//						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI PRIMO QUADRIMESTRE/TRIMESTRE', 0, 1, 'L');
//                        $testo_excel .= 'TABELLONE VOTI PRIMO QUADRIMESTRE/TRIMESTRE' . chr(13) . chr(10);
//					}
//
//					if(intval($periodo) == 8 || intval($periodo) == 28)
//					{
//						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI SECONDO TRIMESTRE', 0, 1, 'L');
//                        $testo_excel .= 'TABELLONE VOTI SECONDO TRIMESTRE' . chr(13) . chr(10);
//					}
//
//					if(intval($periodo) == 9 || intval($periodo) == 29)
//					{
//						$pdf->CellFitScale($larghezza_pagina - 40, 10, 'TABELLONE VOTI DI FINE ANNO', 0, 1, 'L');
//                        $testo_excel .= 'TABELLONE VOTI DI FINE ANNO' . chr(13) . chr(10);
//					}
//
//					$pdf->CellFitScale($larghezza_pagina - 60, 10, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 0, 'L');
//					$pdf->CellFitScale(0, 10, 'A.S. ' . $anno_inizio . "/" . $anno_fine, 0, 1, 'R');
//					$pdf->ln(2);
//					$pdf->CellFitScale($larghezza_pagina - 40, 6, 'Docente: ' . $dati_professore[2] . " " . $dati_professore[1], 0, 1, 'L');
//					$pdf->CellFitScale($larghezza_pagina - 40, 6, 'Materia: ' . $dati_materia['descrizione'], 0, 1, 'L');
//
//                    $testo_excel .= 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5] . chr(13) . chr(10);
//                    $testo_excel .= 'A.S. ' . $anno_inizio . "/" . $anno_fine . chr(13) . chr(10);
//                    $testo_excel .= chr(13) . chr(10);
//                    $testo_excel .= 'Docente: ' . $dati_professore[2] . " " . $dati_professore[1] . chr(13) . chr(10);
//                    $testo_excel .= 'Materia: ' . $dati_materia['descrizione'] . chr(13) . chr(10);
//
//					$larghezza = $larghezza_pagina - 20;
//					$conta_campi = 0;
//					$larghezza_studente = 30;
//					$larghezza_registro = 5;
//					$larghezza_voti = 30;
//					$larghezza_assenze = 15;
//					$larghezza_recuperi = 50;
//
//					if(is_array($elenco_campi))
//					{
//						foreach($elenco_campi as $singolo_campo)
//						{
//							if($singolo_campo['visibile'] == 'SI' && (in_array($materia_professore["id_materia"], $singolo_campo['abbinamenti']['materie']) || isset($singolo_campo['abbinamenti']['materie'][0])))
//                            {
//								$conta_campi++;
//							}
//						}
//					}
//
//					if($conta_campi > 0)
//					{
//						$larghezza_campi_liberi = round((($larghezza - ($larghezza_studente + $larghezza_registro + $larghezza_voti + $larghezza_recuperi)) / ($conta_campi + 1)), 3);
//					}
//
//					$pdf->SetFont('helvetica', 'B', $dimensione_font);
//                    $pdf->CellFitScale($larghezza_registro, 8, 'N°', 1, 0, 'C', $fill);
//					$pdf->CellFitScale($larghezza_studente, 8, 'STUDENTE', 1, 0, 'C', $fill);
//					$pdf->CellFitScale($larghezza_voti, 8, 'VOTI', '1', 0, 'C', $fill);
//					$pdf->CellFitScale($larghezza_assenze, 8, 'ASS.', '1', 0, 'C', $fill);
//					$pdf->CellFitScale($larghezza_recuperi, 8, 'TIPO RECUPERO', '1', 0, 'C', $fill);
//
//                    $testo_excel .= 'N°' . chr(9) . 'STUDENTE' .chr(9) . 'VOTI' . chr(9) . 'ASS' . chr(9) . 'TIPO RECUPERO';
//
//					if(is_array($elenco_campi))
//					{
//						foreach($elenco_campi as $singolo_campo)
//						{
//							if($singolo_campo['visibile'] == 'SI' && (in_array($materia_professore["id_materia"], $singolo_campo['abbinamenti']['materie']) || isset($singolo_campo['abbinamenti']['materie'][0])))
//                            {
//								$pdf->CellFitScale($larghezza_campi_liberi, 8, decode($singolo_campo["nome"]), '1', 0, 'C', $fill);
//                                $testo_excel .= chr(9) . decode($singolo_campo["nome"]);
//							}
//						}
//					}
//
//					$pdf->CellFitScale(0, 8, '', '0', 1);
//                    $testo_excel .= chr(13) . chr(10);
//
//					foreach($elenco_voti_pagelline as $studente)
//					{
//                        $altezza_cella_tmp = $altezza_cella;
//                        // calcolo l'altezza della cella maggiorata se necessario
//                        if(is_array($studente["campi_liberi"]))
//						{
//							foreach($studente["campi_liberi"] as $campi_liberi)
//							{
//								if ($campi_liberi['visibile'] == 'SI' && is_array($campi_liberi["valori_precomp"]) && (in_array($materia_professore["id_materia"], $campi_liberi['abbinamenti']['materie']) || isset($campi_liberi['abbinamenti']['materie'][0]))) {
//
//                                    foreach ($campi_liberi["valori_precomp"] as $dati_valori_precomp) {
//                                        if ($dati_valori_precomp["selezionato"] == "SI") {
//
//                                            if ($dati_valori_precomp["id_valore_precomp"] == "-1") {
//                                                if (strlen($campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]) > 50) {
//                                                    if($formato_pagina == "A4") {
//                                                        $pdf->SetFont('helvetica', '', 4);
//                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"])*2;
//                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
//                                                    } else {
//                                                        $pdf->SetFont('helvetica', '', 6);
//                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"])*3;
//                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
//                                                    }
//                                                }
//                                            } else {
//                                                if (strlen($dati_valori_precomp[$campi_liberi_stampa]) > 50) {
//                                                    if($formato_pagina == "A4") {
//                                                        $pdf->SetFont('helvetica', '', 4);
//                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa])*2;
//                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
//                                                    } else {
//                                                        $pdf->SetFont('helvetica', '', 6);
//                                                        $alt = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa])*3;
//                                                        $altezza_cella = ($altezza_cella < $alt) ? $alt : $altezza_cella;
//                                                    }
//                                                }
//                                            }
//                                        }
//                                    }
//                                }
//							}
//						}
//
//						$fill = !$fill;
//						$pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
//                        $pdf->CellFitScale($larghezza_registro, $altezza_cella, $studente["registro"], 1, 0, 'C', $fill);
//						$pdf->CellFitScale($larghezza_studente, $altezza_cella, $studente["cognome"] . " " . $studente["nome"], 1, 0, 'L', $fill);
//                        $testo_excel .= $studente["registro"] . chr(9) . $studente["cognome"] . " " . $studente["nome"] . chr(9);
//						$voto_valore = "";
//						$voto_scritto_pagella = "";
//						$voto_orale_pagella = "";
//						$voto_pratico_pagella = "";
//
//						if($tipo_visualizzazione_voti == 'personalizzato')
//						{
//							if($studente['tipo_voto_personalizzato'] == '1')
//							{
//								$check_tipo = "voto_singolo";
//							}
//
//							if($studente['tipo_voto_personalizzato'] == '3')
//							{
//								$check_tipo = "scritto_orale_pratico";
//							}
//
//							if($studente['tipo_voto_personalizzato'] == '2')
//							{
//								$check_tipo = "scritto_orale";
//							}
//						}
//						else
//						{
//							$check_tipo = $tipo_visualizzazione_voti;
//						}
//
//						if($check_tipo == "voto_singolo")
//						{
//							foreach($significati_voto as $significato)
//							{
//								if($significato["voto"] == $studente["voto_pagellina"])
//								{
//									switch($tipo_voto_stampa)
//									{
//										case "scheda":
//											$voto_valore = $studente["voto_pagellina"];
//											break;
//										case "codice":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_valore = $significati_tmp["codice_pagella"];
//											break;
//										case "descrizione":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_valore = $significati_tmp["valore_pagella"];
//											break;
//										default:
//											$voto_valore = $studente["voto_pagellina"];
//											break;
//									}
//								}
//							}
//						}
//						else
//						{
//							foreach($significati_voto as $significato)
//							{
//								if($significato["voto"] == $studente["voto_scritto_pagella"])
//								{
//									switch($tipo_voto_stampa)
//									{
//										case "scheda":
//											$voto_scritto_pagella = $studente["voto_scritto_pagella"];
//											break;
//										case "codice":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_scritto_pagella = $significati_tmp["codice_pagella"];
//											break;
//										case "descrizione":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_scritto_pagella = $significati_tmp["valore_pagella"];
//											break;
//										default:
//											$voto_scritto_pagella = $studente["voto_scritto_pagella"];
//											break;
//									}
//								}
//
//								if($significato["voto"] == $studente["voto_orale_pagella"])
//								{
//									switch($tipo_voto_stampa)
//									{
//										case "scheda":
//											$voto_orale_pagella = $studente["voto_orale_pagella"];
//											break;
//										case "codice":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_orale_pagella = $significati_tmp["codice_pagella"];
//											break;
//										case "descrizione":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_orale_pagella = $significati_tmp["valore_pagella"];
//											break;
//										default:
//											$voto_orale_pagella = $studente["voto_scritto_pagella"];
//											break;
//									}
//								}
//
//								if($significato["voto"] == $studente["voto_pratico_pagella"])
//								{
//									switch($tipo_voto_stampa)
//									{
//										case "scheda":
//											$voto_pratico_pagella = $studente["voto_pratico_pagella"];
//											break;
//										case "codice":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_pratico_pagella = $significati_tmp["codice_pagella"];
//											break;
//										case "descrizione":
//											$significati_tmp = estrai_significati_voti_specifici($significato['voto'], $dati_materia[13]);
//											$voto_pratico_pagella = $significati_tmp["valore_pagella"];
//											break;
//										default:
//											$voto_pratico_pagella = $studente["voto_pratico_pagella"];
//											break;
//									}
//								}
//							}
//
//							if(strlen($voto_scritto_pagella) > 0)
//							{
//								$voto_valore .= "Scr. " . $voto_scritto_pagella;
//							}
//
//							if(strlen($voto_orale_pagella) > 0)
//							{
//								$voto_valore .= "  -  " . "Or. " . $voto_orale_pagella;
//							}
//
//							if(strlen($voto_pratico_pagella) > 0)
//							{
//								$voto_valore .= "  -  " . "Pr. " . $voto_pratico_pagella;
//							}
//						}
//
//						$pdf->SetFont('helvetica', '', $dimensione_font);
//                        $pdf->CellFitScale($larghezza_voti, $altezza_cella, $voto_valore, '1', 0, 'C', $fill);
//						$pdf->CellFitScale($larghezza_assenze, $altezza_cella, stampa_ore_o_minuti($studente["ore_assenza"]), '1', 0, 'C', $fill);
//						$pdf->CellFitScale($larghezza_recuperi, $altezza_cella, estrai_tipo_recupero_singolo($studente["tipo_recupero"]), '1', 0, 'C', $fill);
//                        $testo_excel .= $voto_valore . chr(9) . stampa_ore_o_minuti($studente["ore_assenza"]) . chr(9) . estrai_tipo_recupero_singolo($studente["tipo_recupero"]);
//
//                        if(is_array($studente["campi_liberi"]))
//						{
//							foreach($studente["campi_liberi"] as $campi_liberi)
//							{
//								if ($campi_liberi['visibile'] == 'SI' && is_array($campi_liberi["valori_precomp"]) && (in_array($materia_professore["id_materia"], $campi_liberi['abbinamenti']['materie']) || isset($campi_liberi['abbinamenti']['materie'][0]))) {
//                                    $trovato_campo = false;
//
//                                    foreach ($campi_liberi["valori_precomp"] as $dati_valori_precomp) {
//                                        if ($dati_valori_precomp["selezionato"] == "SI") {
//                                            $trovato_campo = true;
//
//                                            if ($dati_valori_precomp["id_valore_precomp"] == "-1") {
//                                                if (strlen($campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]) > 50) {
//                                                    if($formato_pagina == "A4") {
//                                                        $pdf->SetFont('helvetica', '', 4);
//                                                    } else {
//                                                        $pdf->SetFont('helvetica', '', 6);
//                                                    }
//                                                    $tot_righe = $pdf->MultiCellNbLines($larghezza_campi_liberi, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"]);
//                                                    $pdf->MultiCell($larghezza_campi_liberi, $altezza_cella, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"], 1, 'L', false, 0);
//                                                    $testo_excel .= chr(9) . $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"];
//                                                } else {
//                                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"], '1', 0, 'C', $fill);
//                                                    $testo_excel .= chr(9) . $campi_liberi["valori_precomp"]["meno_uno"]["valore_testuale"];
//                                                }
//                                            } else {
//                                                if (strlen($dati_valori_precomp[$campi_liberi_stampa]) > 50) {
//                                                    if($formato_pagina == "A4") {
//                                                        $pdf->SetFont('helvetica', '', 4);
//                                                    } else {
//                                                        $pdf->SetFont('helvetica', '', 6);
//                                                    }
//                                                    $tot_righe = $pdf->MultiCellNbLines($larghezza_campi_liberi, $dati_valori_precomp[$campi_liberi_stampa]);
//                                                    $pdf->MultiCell($larghezza_campi_liberi, $altezza_cella, $dati_valori_precomp[$campi_liberi_stampa], 1, 'L', false, 0);
//                                                    $testo_excel .= chr(9) . $dati_valori_precomp[$campi_liberi_stampa];
//                                                }
//                                                else
//                                                {
//                                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, $dati_valori_precomp[$campi_liberi_stampa], '1', 0, 'C', $fill);
//                                                    $testo_excel .= chr(9) . $dati_valori_precomp[$campi_liberi_stampa];
//                                                }
//                                                $valore_totale_campi_liberi_studente += $dati_valori_precomp['valore'];
//                                            }
//                                        }
//                                    }
//
//                                    if(!$trovato_campo)
//                                    {
//                                        $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, "", '1', 0, 'C', $fill);
//                                        $testo_excel .= chr(13) . chr(10);
//                                    }
//                                }
//
////                                if(!$trovato_campo)
////                                {
////                                    $pdf->CellFitScale($larghezza_campi_liberi, $altezza_cella, "", '1', 0, 'C', $fill);
////                                }
//							}
//						}
//
//						$pdf->CellFitScale(0, $altezza_cella, '', '0', 1, 'C');
//                        $testo_excel .= chr(13) . chr(10);
//
//                        $altezza_cella = $altezza_cella_tmp;
//					}
//					//}}} </editor-fold>
//				}
//				//}}} </editor-fold>
//			}

			switch($tipo_file_esportato)
			{
                //{{{ <editor-fold defaultstate="collapsed" desc="Tipo file esportato">
				case "pdf":
					$pdf->Output($stato_secondario . '_' . date('Y-m-d_H-i') . ".pdf", "D");
					exit;
					break;
				case "xls":
					//cancello tutte i file temporanei fatti da più di un'ora
					$dir = "tmp_xls";
					CleanFiles($dir);
					//creo i nuovi file temporanei
					$file = basename(tempnam($dir, 'tmp'));
					rename($dir . "/" . $file, $dir . "/" . $file . '.xls');
					$file.='.xls';
					//Salva il file xls come file
					$nuovo_nome = $dir . '/' . $file;
					$handle = fopen($nuovo_nome, "w");
					fwrite($handle, $testo_excel);
					fclose($handle);
					//Reindirizzamento JavaScript
					echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
					break;
                //}}} </editor-fold>
			}
		}
		//}}} </editor-fold>
        break;
	case "gestione_tabellone_pagelline_display":
		//{{{ <editor-fold defaultstate="collapsed">
		$dati_classe = estrai_classe((int) $id_classe);
		$template->assign("dati_classe", $dati_classe);
		$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
		$template->assign("coordinatore", $coordinatore);
		$periodo = $periodo_pagella_in_uso;
		$template->assign("periodo", $periodo);
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);
		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);

		$param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
		if ($dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI') {
			$stato_successivo = 'modifica_tabellone_pagelline_competenze_display';
		} else {
			$stato_successivo = 'modifica_tabellone_pagelline_display';
		}
		$template->assign("stato_successivo", $stato_successivo);
		//}}} </editor-fold>
		break;
	case "parametri_tabellone_pagelline_display":
		//{{{ <editor-fold defaultstate="collapsed">
		$dati_classe = estrai_classe((int) $id_classe);

		if($prove_strutturate == 'SI' && $dati_classe['classe'] == '3' && $dati_classe['tipo_indirizzo'] == '2')
		{
			$periodo = '10';
		}
		else
		{
			$periodo = $periodo_pagella_in_uso;
		}

		$template->assign("periodo", $periodo);

		$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
		$template->assign("dati_checkbox", $elenco_studenti_classe);

		//calcolo, e lo passo al template, lo step per definire le colonne della tabella delle checkbox
		$tot_record = count($elenco_studenti_classe);
		$step = intval($tot_record / 4) + 1;
		$template->assign("step", $step);

		//creo, e lo passo al template, l'array contenente la partenza di ogni riga della tabella html
		$start = [];

		for($cont = 0; $cont < $step; $cont++)
		{
			$start[$cont] = $cont;
		}

		$template->assign("start", $start);
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);
		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);
		//}}} </editor-fold>
		break;
	case 'salva_storico_documento':
		//{{{ <editor-fold defaultstate="collapsed">
		if($form_id_storico_documento > 0)
		{
			modifica_storico_documento((int) $form_id_storico_documento, $form_descrizione, $documento_finale, $form_tipo, (int) $current_user);
		}
		else
		{
			$form_id_storico_documento = inserisci_storico_documento($form_descrizione, $documento_finale, $form_tipo, (int) $current_user);
		}

		$dati_classe = estrai_classe((int) $id_classe);
		$param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
		if ($dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI') {
			$stato_secondario = 'modifica_tabellone_pagelline_competenze_display';
		} else {
			$stato_espansione = 1;
			$stato_secondario = 'modifica_tabellone_pagelline_display';
		}
		//}}} </editor-fold>
		//break omesso volutamente
	case "modifica_tabellone_pagelline_display":
	case "modifica_tabellone_pagelline_competenze_display":
		$giudizio_sospeso_6_in_condotta = estrai_parametri_singoli("ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA");
		$template->assign("giudizio_sospeso_6_in_condotta", $giudizio_sospeso_6_in_condotta);
		if ($stato_secondario == 'modifica_tabellone_pagelline_competenze_display') {
			include "adm/include_tabellone_pagelle_competenze.php"; //stesso file dell'ammin
		} else {
			include('adm/include_tabellone_pagelle.php');
		}
		break;
	case "riepilogo_assenze_monteore_display":
		if($id_classe != '')
		{
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
			$template->assign("id_studente", $id_studente);
		}
		break;
	case "riepilogo_assenze_monteore_update":
		//{{{ <editor-fold defaultstate="collapsed">
		//prendo i giorni impostati dall'utente dal template e aggiungo un giorno per comprendere anche l'ultimo giorno
		$inizio_Month_int = intval($inizio_Month);
		$fine_Month_int = intval($fine_Month);
		$inizio = mktime(0, 0, 0, $inizio_Month_int, $inizio_Day, $inizio_Year);
		$fine = mktime(23, 59, 59, $fine_Month_int, $fine_Day, $fine_Year);
		$pdf = new NEXUS_PDF;
        if($id_classe > 0)
		{
			$studenti = estrai_studenti_classe((int) $id_classe);
			foreach($studenti as $studente)
			{
				$id_studente = $studente['id_studente'];
				include 'adm/stampe/stampa_riepilogo_assenze_monteore_singolo_studente.php';
			}
			$pdf->Output('riepilogo_assenze_' . date('Y-m-d_H-i') . ".pdf", "D");
			exit;
		}
		//}}} </editor-fold>
		break;
	case "inserisci_esiti_recuperi_display":
		//{{{ <editor-fold defaultstate="collapsed">
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);

		//estraggo i dati della classe
		$dati_classe = estrai_classe((int) $id_classe);
		$template->assign("dati_classe", $dati_classe);

		$periodo_esiti_recupero = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO_PER_RECUPERI", (int) $id_classe, 'classe');
		$template->assign("periodo_esiti_recupero",$periodo_esiti_recupero);
		$elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo_esiti_recupero, (int) $current_user, 'classe', 'tipi_recupero_valorizzati');

		switch($periodo_esiti_recupero)
		{
			case 1:
				$periodo_testuale = '1° pagellina infraquadrimestrale';
				break;
			case 2:
				$periodo_testuale = '2° pagellina infraquadrimestrale';
				break;
			case 3:
				$periodo_testuale = '3° pagellina infraquadrimestrale';
				break;
			case 4:
				$periodo_testuale = '4° pagellina infraquadrimestrale';
				break;
			case 5:
				$periodo_testuale = '5° pagellina infraquadrimestrale';
				break;
			case 6:
				$periodo_testuale = '6° pagellina infraquadrimestrale';
				break;
			case 7:
				$periodo_testuale = 'pagella di fine 1° quadrimestre/trimestre';
				break;
			case 8:
				$periodo_testuale = 'pagella di fine 2° trimestre';
				break;
			case 9:
				$periodo_testuale = 'pagella di fine anno';
				break;
		}

		$template->assign("periodo_testuale", $periodo_testuale);

		$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);

		if($coordinatore == 'NO') {
			$elenco_materie = estrai_materie_multi_classe_del_professore((int) $id_classe, (int) $current_user);

			foreach($elenco_voti_pagelline as $id_studente => $studente)
			{
				$studente_prof = [];

				foreach($elenco_materie as $materia_prof)
				{
					if(count($studente[$materia_prof['id_materia']]) > 0)
					{
						$studente_prof[$materia_prof['id_materia']] = $studente[$materia_prof['id_materia']];
					}
				}

				if(count($studente_prof) > 0)
				{
					$elenco_voti_pagelline_finale[$id_studente] = $studente_prof;
				}
			}
		}
		else
		{
			$elenco_voti_pagelline_finale = $elenco_voti_pagelline;
		}

		$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline_finale);

		$elenco_studenti_base = estrai_studenti_classe((int) $id_classe);

		$elenco_studenti = [];

		foreach($elenco_studenti_base as $studente)
		{
			$elenco_studenti[$studente['id_studente']]['cognome'] = $studente['cognome'];
			$elenco_studenti[$studente['id_studente']]['nome'] = $studente['nome'];
			$elenco_studenti[$studente['id_studente']]['numero_righe'] = count($elenco_voti_pagelline_finale[$studente['id_studente']]) + 1;
		}

		$template->assign("elenco_studenti", $elenco_studenti);
		//}}} </editor-fold>
		break;
	case "inserisci_esiti_recuperi_update":
		//{{{ <editor-fold defaultstate="collapsed">
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);

		$periodo_esiti_recupero = estrai_parametri_singoli("PERIODO_PAGELLA_IN_USO_PER_RECUPERI", (int) $id_classe, 'classe');
		$template->assign("periodo_esiti_recupero",$periodo_esiti_recupero);

		switch($periodo_esiti_recupero)
		{
			case 1:
				$periodo_testuale = '1° pagellina infraquadrimestrale';
				break;
			case 2:
				$periodo_testuale = '2° pagellina infraquadrimestrale';
				break;
			case 3:
				$periodo_testuale = '3° pagellina infraquadrimestrale';
				break;
			case 4:
				$periodo_testuale = '4° pagellina infraquadrimestrale';
				break;
			case 5:
				$periodo_testuale = '5° pagellina infraquadrimestrale';
				break;
			case 6:
				$periodo_testuale = '6° pagellina infraquadrimestrale';
				break;
			case 7:
				$periodo_testuale = 'pagella di fine 1° quadrimestre/trimestre';
				break;
			case 8:
				$periodo_testuale = 'pagella di fine 2° trimestre';
				break;
			case 9:
				$periodo_testuale = 'pagella di fine anno';
				break;
		}

        $template->assign("periodo_testuale", $periodo_testuale);

		//estraggo i dati della classe
		$dati_classe = estrai_classe((int) $id_classe);
		$template->assign("dati_classe", $dati_classe);

		$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);

		$elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo_esiti_recupero, (int) $current_user, 'classe', 'tipi_recupero_valorizzati');

		if($coordinatore != 'SI')
		{
			$elenco_materie = estrai_materie_multi_classe_del_professore((int) $id_classe, (int) $current_user);

			foreach($elenco_voti_pagelline as $id_studente => $studente)
			{
				$studente_prof = [];

				foreach($elenco_materie as $materia_prof)
				{
					if(count($studente[$materia_prof['id_materia']]) > 0)
					{
						$studente_prof[$materia_prof['id_materia']] = $studente[$materia_prof['id_materia']];
					}
				}

				if(count($studente_prof) > 0)
				{
					$elenco_voti_pagelline_finale[$id_studente] = $studente_prof;
				}
			}
		}
		else
		{
			$elenco_voti_pagelline_finale = $elenco_voti_pagelline;
		}

		foreach($elenco_voti_pagelline_finale as $id_studente => $studente)
		{
			foreach($studente as $id_materia => $materia)
			{
				$id_voto_pagellina = modifica_esito_tipo_recupero((int) $materia['id_voto_pagellina'], ${'esito_' . $materia['id_voto_pagellina']}, (int) $current_user);
			}
		}

		//estraggo di nuovo i dati dopo che sono stati modificati
		$elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_classe, $periodo_esiti_recupero, (int) $current_user, 'classe', 'tipi_recupero_valorizzati');

		if($coordinatore != 'SI')
		{
			$elenco_materie = estrai_materie_multi_classe_del_professore((int) $id_classe, (int) $current_user);

			foreach($elenco_voti_pagelline as $id_studente => $studente)
			{
				$studente_prof = [];

				foreach($elenco_materie as $materia_prof)
				{
					if(count($studente[$materia_prof['id_materia']]) > 0)
					{
						$studente_prof[$materia_prof['id_materia']] = $studente[$materia_prof['id_materia']];
					}
				}

				if(count($studente_prof) > 0)
				{
					$elenco_voti_pagelline_finale[$id_studente] = $studente_prof;
				}
			}
		}
		else
		{
			$elenco_voti_pagelline_finale = $elenco_voti_pagelline;
		}

		$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline_finale);

		$elenco_studenti_base = estrai_studenti_classe((int) $id_classe);

		$elenco_studenti = [];

		foreach($elenco_studenti_base as $studente)
		{
			$elenco_studenti[$studente['id_studente']]['cognome'] = $studente['cognome'];
			$elenco_studenti[$studente['id_studente']]['nome'] = $studente['nome'];
			$elenco_studenti[$studente['id_studente']]['numero_righe'] = count($elenco_voti_pagelline_finale[$studente['id_studente']]) + 1;
		}

		$template->assign("elenco_studenti", $elenco_studenti);

		$messaggio = 'Esiti inseriti correttamente!';
		$template->assign("messaggio", $messaggio);

		$stato_secondario = "inserisci_esiti_recuperi_display";
		//}}} </editor-fold>
		break;
	case "inserisci_proposte_pagelline_display":
		//{{{ <editor-fold defaultstate="collapsed">
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);

		$arrotondamento_ins_aut = estrai_parametri_singoli("ARROTONDAMENTO_VOTI",(int) $id_classe, 'classe');
		$template->assign("arrotondamento_ins_aut", $arrotondamento_ins_aut);

		$inizio_assenze = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_ASSENZE",(int) $id_classe, 'classe');
		$fine_assenze = estrai_parametri_singoli("DATA_FINE_CALCOLO_ASSENZE",(int) $id_classe, 'classe');
		$inizio_voti = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_VOTI",(int) $id_classe, 'classe');
		$fine_voti = estrai_parametri_singoli("DATA_FINE_CALCOLO_VOTI",(int) $id_classe, 'classe');
		$blocca_date = estrai_parametri_singoli("BLOCCA_DATE_CALCOLO_AUTOMATICO",(int) $id_classe, 'classe');
		$template->assign("blocca_date", $blocca_date);

		$blocca_arrotondamento = estrai_parametri_singoli("BLOCCA_ARROTONDAMENTO_CALCOLO_AUTOMATICO",(int) $id_classe, 'classe');
		$template->assign("blocca_arrotondamento", $blocca_arrotondamento);
		if($blocca_date == "BLOCCA DATE")
		{
			$inizio_assenze_tmp = $inizio_assenze;
			$inizio_assenze = [];
			$inizio_assenze["Day"] = date("d", $inizio_assenze_tmp);
			$inizio_assenze["Month"] = date("m", $inizio_assenze_tmp);
			$inizio_assenze["Year"] = date("Y", $inizio_assenze_tmp);

			$fine_assenze_tmp = $fine_assenze;
			$fine_assenze = [];
			$fine_assenze["Day"] = date("d", $fine_assenze_tmp);
			$fine_assenze["Month"] = date("m", $fine_assenze_tmp);
			$fine_assenze["Year"] = date("Y", $fine_assenze_tmp);

			$inizio_voti_tmp = $inizio_voti;
			$inizio_voti = [];
			$inizio_voti["Day"] = date("d", $inizio_voti_tmp);
			$inizio_voti["Month"] = date("m", $inizio_voti_tmp);
			$inizio_voti["Year"] = date("Y", $inizio_voti_tmp);

			$fine_voti_tmp = $fine_voti;
			$fine_voti = [];
			$fine_voti["Day"] = date("d", $fine_voti_tmp);
			$fine_voti["Month"] = date("m", $fine_voti_tmp);
			$fine_voti["Year"] = date("Y", $fine_voti_tmp);
		}
		$template->assign("inizio_assenze", $inizio_assenze);
		$template->assign("fine_assenze", $fine_assenze);
		$template->assign("inizio_voti", $inizio_voti);
		$template->assign("fine_voti", $fine_voti);
		$template->assign("periodo", $periodo_pagella_in_uso);

		$chiusura = estrai_chiusura_scrutini((int) $id_classe);
		$template->assign("chiusura", $chiusura);

		$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);
		$template->assign("coordinatore", $coordinatore);

		$param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
		$abilita_sezione_voti = 'SI';
		if ($dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI') {
			$abilita_sezione_voti = 'NO';
		}
		$template->assign("abilita_sezione_voti", $abilita_sezione_voti);
		//}}} </editor-fold>
		break;
	case "inserimento_automatico_update":
		//{{{ <editor-fold defaultstate="collapsed">
		$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);

		$dati_classe = estrai_classe((int) $id_classe);
		$valore_classe = intval($dati_classe[2]);
		$consiglio_classe_attivo = $dati_classe['consiglio_classe_attivo'];
		$data_inizio_voti = mktime(0, 0, 0, intval($voti_inizio_Month), intval($voti_inizio_Day), intval($voti_inizio_Year));
		$data_fine_voti = mktime(23, 59, 59, intval($voti_fine_Month), intval($voti_fine_Day), intval($voti_fine_Year));

		$data_inizio_assenze = mktime(0, 0, 0, intval($assenze_inizio_Month), intval($assenze_inizio_Day), intval($assenze_inizio_Year));
		$data_fine_assenze = mktime(23, 59, 59, intval($assenze_fine_Month), intval($assenze_fine_Day), intval($assenze_fine_Year));

		inserisci_proposte_voti_assenze_pagellina_classe((int) $id_classe, $periodo, $data_inizio_voti, $data_fine_voti, $inserisci_voto, $sovrascrivi_voto, $data_inizio_assenze, $data_fine_assenze, $inserisci_assenze, $sovrascrivi_assenze, $arrotondamento_ins_aut, "professore", (int) $id_professore, $funzione_abilita_proposte_totali, (int) $current_user, $consiglio_classe_attivo, $forza_inserimento_singola_materia, $periodo_ass, $sovrascrivi_monteore, $corsi_abbinati, "NO", "0", '0', $inserisci_assenze_DAD);

		$cosa = "INSERITA DAL PROFESSORE CON ID:" . $id_professore . " PROPOSTA AUTOMATICA VOTI NEL PERIODO: " .
                $periodo . " DELLA CLASSE CON ID: " . $id_classe . " CON INSERIMENTO VOTI= " . $inserisci_voto .
                " CON SOVRASCRITTURA DEI VOTI= " . $sovrascrivi_voto . " CON ARROTONDAMENTO A " . $arrotondamento_ins_aut .
                " TRA LE DATE: " . $data_inizio_voti . " " . $data_fine_voti . " CON INSERIMENTO ASSENZE= " . $inserisci_assenze .
                " SOVRASCRITTURA DELLE ASSENZE= " . $sovrascrivi_assenze . " CON SOVRASCRITTURA MONTEORE=" .  $sovrascrivi_monteore
                . " TRA LE DATE: " . $data_inizio_assenze . " " . $data_fine_assenze . " - SOLO MATERIE DOCENTE=" . $forza_inserimento_singola_materia;
		inserisci_log_storico((int) $current_user, "GESTIONE_PAGELLE", $cosa);
		$param_nuovo_tabellone = estrai_parametri_singoli('ATTIVA_NUOVO_TABELLONE_COMPETENZE');
		if ($dati_classe['tipo_indirizzo'] == '6' && $param_nuovo_tabellone == 'SI') {
			$stato_successivo = 'modifica_tabellone_pagelline_competenze_display';
		} else {
			$stato_successivo = 'modifica_tabellone_pagelline_display';
		}
		$template->assign("stato_successivo", $stato_successivo);
        $template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);
		//}}} </editor-fold>
		break;
    case "inserisci_proposte_aree_disciplinari_display";
		//{{{ <editor-fold defaultstate="collapsed">
		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);

		$template->assign("periodo", $periodo_pagella_in_uso);

		$chiusura = estrai_chiusura_scrutini((int) $id_classe);
		$template->assign("chiusura", $chiusura);

        $arrotondamento_ins_aut = estrai_parametri_singoli("ARROTONDAMENTO_VOTI",(int) $id_classe, 'classe');
		$template->assign("arrotondamento_ins_aut", $arrotondamento_ins_aut);

        $coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);
		$template->assign("coordinatore", $coordinatore);
		//}}} </editor-fold>

        break;
    case "inserimento_automatico_aree_disciplinari";

        $coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
        if ($coordinatore == 'SI') {
            $tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo,(int) $id_classe);

            $elenco_aree_disciplinari = estrai_aree_disciplinari($id_classe);

            $dati_classe = estrai_classe((int) $id_classe);

            $elenco_voti_studenti = inserisci_proposte_voti_assenze_aree_disciplinari($periodo, $id_classe, $current_user, $arrotondamento_ins_aut, $sovrascrivi_voto, $sovrascrivi_monteore);

            if ($elenco_voti_studenti['errore'] != '') {
                $report_inserimento = $elenco_voti_studenti['errore'];
            } else {
                foreach ($elenco_voti_studenti['risultato'] as $k => $stud) {

                    $dati_studente = estrai_dati_studente($k);
                    $nome_studente = $dati_studente['cognome'] . " " . $dati_studente['nome'];

                    if (count($stud) > 0) {
                        $report_inserimento .= "<b>" . $nome_studente . "</b> (". $k . ")<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
                        $report_inserimento .= implode('<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;', $stud);
                        $report_inserimento .= "<br><br>";
                    }
                }
            }

            $template->assign("report_inserimento",$report_inserimento);
            $template->assign("classi_ind",$elenco_classi_ind);
            $template->assign("indirizzo",$indirizzo);
            $template->assign("id_indirizzo",$id_indirizzo);
            $template->assign("classe",$classe);
            $template->assign("id_classe",$id_classe);
        }
        $template->assign("coordinatore", $coordinatore);

        break;
    case "stampa_pagelline_classe_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);

			$dati_classe = estrai_classe((int) $id_classe);
			$template->assign("dati_classe", $dati_classe);

			switch($dati_classe["classe"])
			{
				case "1":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_PRIME_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_PRIME";
					}
					break;
				case "2":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_SECONDE_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_SECONDE";
					}
					break;
				case "3":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_TERZE_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_TERZE";
					}
					break;
				case "4":
					$tipo_stampa = "PAGELLE_QUARTE";
					break;
				case "5":
					$tipo_stampa = "PAGELLE_QUINTE";
					break;
				default:
					$tipo_stampa = "PAGELLE_GENERICO";
					break;
			}

			$arr_temp_materie = [];
			if(is_array($elenco_materie_classe))
			{
				foreach($elenco_materie_classe as $materia)
				{
					$arr_temp_materie[$materia["tipo_valutazione"]]++;
				}

				$temp_value = 0;
				$tipo_voto = 1;
				foreach($arr_temp_materie as $key => $value)
				{
					if($value > $temp_value)
					{
						$temp_value = $value;
						$tipo_voto = $key;
					}
				}
			}
			else
			{
				$tipo_voto = 1;
			}

			$array_tipi_voto = estrai_significati_voti($tipo_voto, "solo_abilitati");

			$arr_temp = [];
			$cont = 0;
			if(is_array($array_tipi_voto))
			{
				foreach($array_tipi_voto as $voto)
				{
					if(is_numeric($voto["voto"]))
					{
						$arr_temp[$cont]["valore"] = $voto["voto"];
						$arr_temp[$cont]["nome"] = $voto["valore"];
						$cont++;
					}
				}
			}
			$template->assign("array_tipi_voto", $arr_temp);

			$stringa_stampa = estrai_parametri_stampa($tipo_stampa);

			$parametri_stampa = explode("@", $stringa_stampa);
			$cont = 0;
			$array_parametri_finale = [];
			if(is_array($parametri_stampa))
			{
				foreach($parametri_stampa as $singolo_parametro)
				{
					$temp_array = explode("#", $singolo_parametro);

					$array_parametri_finale[$cont]["nome"] = $temp_array[0];
					$array_parametri_finale[$cont]["valore"] = $temp_array[1];

					$template->assign("param_pagellina_" . $array_parametri_finale[$cont]["nome"], $array_parametri_finale[$cont]["valore"]);

					$cont++;
				}
			}
		}
		//}}} </editor-fold>
		break;
	case "stampa_pagelline_classe_update":
		//{{{ <editor-fold defaultstate="collapsed">
		//estraggo i dati da passare alla stampa
		if($id_classe >= 0)
		{
			$periodo = $periodo_pagella_in_uso;

			$dati_classe = estrai_classe((int) $id_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);

			if(intval($dati_classe["id_sede"]) > 0)
			{
				$dati_sede = estrai_sede_singola($dati_classe["id_sede"]);
				$intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];
				$indirizzo_scuola = $dati_sede["indirizzo"];
				$citta = $dati_sede["descrizione_comune"];
				$telefono = $dati_sede["telefono"];
				$tipo_istituto_abbreviato = $dati_sede["tipo_istituto_abbreviato"];
				$tipo_istituto_esteso = $dati_sede["descrizione_tipo_scuola"];
				$dirigente_scolastico = $dati_sede["nome_dirigente"];
			}

			switch($dati_classe["classe"])
			{
				case "1":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_PRIME_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_PRIME";
					}
					break;
				case "2":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_SECONDE_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_SECONDE";
					}
					break;
				case "3":
					if($dati_classe['tipo_indirizzo'] == '4')
					{
						$tipo_stampa = "PAGELLE_TERZE_MEDIE";
					}
					else
					{
						$tipo_stampa = "PAGELLE_TERZE";
					}
					break;
				case "4":
					$tipo_stampa = "PAGELLE_QUARTE";
					break;
				case "5":
					$tipo_stampa = "PAGELLE_QUINTE";
					break;
				default:
					$tipo_stampa = "PAGELLE_GENERICO";
					break;
			}

			//estrazione dal template dei parametri da utilizzare per la stampa
			$stringa_parametri = "";

			foreach($_POST as $nome => $valore)
			{
				$pos = strpos($nome, "param_pagellina_");

				if($pos !== false)
				{
					$nome_variabile = substr($nome, 16);
					${$nome_variabile} = $valore;
					$stringa_parametri .= $nome_variabile . "#" . $valore . "@";
				}
			}

			$stringa_parametri = substr($stringa_parametri, 0, -1);

			$pdf = new NEXUS_PDF;

            for($cont_stud = 0; $cont_stud < count($elenco_studenti_classe); $cont_stud++)
			{
				if($elenco_studenti_classe[$cont_stud]['ritirato'] != '0')
				{
					if($stampa_ritirati == 'NO')
					{
						$procedi_con_stampa = 'NO';
					}
					elseif($stampa_ritirati == 'SI')
					{
						$procedi_con_stampa = 'SI';
					}
					elseif($stampa_ritirati == 'SI_CON_DATA')
					{
						if($elenco_studenti_classe[$cont_stud]['data_ritiro'] > mktime(23, 59, 59, 3, 15, $anno_fine))
						{
							$procedi_con_stampa = 'SI';
						}
						else
						{
							$procedi_con_stampa = 'NO';
						}
					}
					else
					{
						$procedi_con_stampa = 'SI';
					}
				}
				else
				{
					$procedi_con_stampa = 'SI';
				}

				if($procedi_con_stampa == 'SI')
				{
					$fill = !$fill;
					$id_studente = $elenco_studenti_classe[$cont_stud][0];
					//estraggo i dati da passare alla stampa
					if($id_studente >= 0)
					{
						for($cont_kill = 0; $cont_kill < $numero_stampe; $cont_kill++)
						{
							if($dati_classe['tipo_indirizzo'] == '4')
							{
								include "adm/stampe/stampa_pagellina_singola_studente_scuole_medie.php";
							}
							elseif($dati_classe['tipo_indirizzo'] == '6')
							{
								include "adm/stampe/stampa_pagellina_singola_studente_scuole_elementari.php";
							}
							else
							{
								include "adm/stampe/stampa_pagellina_singola_studente.php";
							}
						}
					}
				}
			}

			$pdf->Output('pagella_' . date('Y-m-d_H-i') . ".pdf", "D");
			exit;
		}
		//}}} </editor-fold>
		break;
	case "stampa_ore_assenza_studente":
		include "adm/stampe/stampa_ore_assenza_studente.php";
		break;
	case "stampa_tabellone_medie_voti_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo_del_professore((int) $id_indirizzo, (int) $id_professore);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe_del_professore((int) $id_classe, (int) $id_professore);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "stampa_tabellone_medie_voti_update":
		//{{{ <editor-fold defaultstate="collapsed">
		$coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $id_professore);
		if($coordinatore == 'SI')
		{
			include('adm/stampe/stampa_tabellone_medie_coordinatore.php');
		}
		else
		{
			include('adm/stampe/stampa_tabellone_medie_prof.php');
		}
		//}}} </editor-fold>
		break;
    case "stampa_alternanza_display":
        //{{{ <editor-fold defaultstate="collapsed">
        $template->assign("id_classe", $id_classe);
        $template->assign("classe", $classe);
        //}}} </editor-fold>
        break;
    case "stampa_alternanza_update":
        include "adm/stampe/stampa_riepilogo_alternanza_classe.php";
        break;
	case "foglio_notizie_studente_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe((int) $id_classe);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);
			$template->assign("stato_visualizza_pagelle", $tipo_visualizzazione_voti);

			$elenco_voti_pagelline = estrai_voti_pagellina_studente((int) $id_classe, $periodo, (int) $id_studente, $tipo_visualizzazione_voti);
			$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline);

			$dati_studente = estrai_dati_studente((int) $id_studente);
			$template->assign("dati_studente", $dati_studente);

			$dati_classe = estrai_classe((int) $id_classe);

			if(($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "1" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_terza"];
			}
			elseif(($dati_classe["classe"] == "4" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "2" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_quarta"];
			}
			elseif(($dati_classe["classe"] == "5" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_quinta"];
			}
			$template->assign("motivazione_crediti", $motivazione_crediti);

			$foglio_notizie_studente = estrai_foglio_notizie_studente((int) $id_studente);
			$template->assign("foglio_notizie_studente", $foglio_notizie_studente);

			//creo l'array per la combo(select) delle ore di assenza
			$ore_assenza = [];
			for($cont = 0; $cont < 300; $cont++)
			{
				$ore_assenza[$cont] = $cont;
			}
			$template->assign("ore_assenza", $ore_assenza);
			$template->assign("id_classe", $id_classe);
			$template->assign("id_studente", $id_studente);
			$cognome_stud = str_replace("\\", "", $cognome_stud);
			$nome_stud = str_replace("\\", "", $nome_stud);
			$template->assign("nome_stud", $dati_studente[3]);
			$template->assign("cognome_stud", $dati_studente[4]);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("periodo", $periodo);
		}
		//}}} </editor-fold>
		break;
	case "foglio_notizie_studente_update":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);

			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe((int) $id_classe);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);

			$tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, (int) $id_classe);
			$template->assign("stato_visualizza_pagelle", $tipo_visualizzazione_voti);

			$elenco_voti_pagelline = estrai_voti_pagellina_studente((int) $id_classe, $periodo, (int) $id_studente, $tipo_visualizzazione_voti);
			$template->assign("elenco_voti_pagelline", $elenco_voti_pagelline);

			$dati_classe = estrai_classe((int) $id_classe);

			$dati_studente = estrai_dati_studente((int) $id_studente);

			$foglio_notizie_studente = estrai_foglio_notizie_studente((int) $id_studente);

			$multi_valore_finale = "";
			foreach($foglio_notizie_studente["multi_valore"] as $dati)
			{
				if(${"checkbox_multi_valore_valore1_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::1||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::1||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore2_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::2||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::2||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore3_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::3||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::3||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore4_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::4||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::4||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore5_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::5||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::5||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore6_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::6||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::6||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore7_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::7||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::7||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore8_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::8||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::8||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore9_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::9||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::9||" . $dati["codice"] . "||" . "0";
				}

				if(${"checkbox_multi_valore_valore10_" . $dati["codice"]} == 1)
				{
					$multi_valore_finale .= "::10||" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$multi_valore_finale .= "::10||" . $dati["codice"] . "||" . "0";
				}
			}
			$multi_valore_finale = substr($multi_valore_finale, 2);
			$dati_selezionati["multi_valore"] = $multi_valore_finale;

			$valori_singoli_finale = "";
			foreach($foglio_notizie_studente["valori_singoli"] as $dati)
			{
				if(${"checkbox_valore_singolo_valore_" . $dati["codice"]} == 1)
				{
					$valori_singoli_finale .= "::" . $dati["codice"] . "||" . "1";
				}
				else
				{
					$valori_singoli_finale .= "::" . $dati["codice"] . "||" . "0";
				}
			}
			$valori_singoli_finale = substr($valori_singoli_finale, 2);
			$dati_selezionati["valori_singoli"] = $valori_singoli_finale;

			$note_ulteriori = $note;
			$dati_selezionati["note_ulteriori"] = $note_ulteriori;

			modifica_foglio_notizie_studente((int) $id_studente, $dati_selezionati, (int) $current_user);

			if(($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "1" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$anno = 3;
			}
			elseif(($dati_classe["classe"] == "4" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "2" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$anno = 4;
			}
			elseif(($dati_classe["classe"] == "5" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$anno = 5;
			}
			else
			{
				$anno = 0;
			}
			aggiorna_singole_motivazioni_crediti_studente((int) $id_studente, $motivazione_crediti, $anno, (int) $current_user);

			$foglio_notizie_studente = estrai_foglio_notizie_studente((int) $id_studente);
			$template->assign("foglio_notizie_studente", $foglio_notizie_studente);

			$dati_studente = estrai_dati_studente((int) $id_studente);
			$template->assign("dati_studente", $dati_studente);

			if(($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "1" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_terza"];
			}
			elseif(($dati_classe["classe"] == "4" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "2" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_quarta"];
			}
			elseif(($dati_classe["classe"] == "5" && $dati_classe["tipo_indirizzo"] != "1") || ($dati_classe["classe"] == "3" && $dati_classe["tipo_indirizzo"] == "1"))
			{
				$motivazione_crediti = $dati_studente["motivi_crediti_quinta"];
			}
			else
			{
				$motivazione_crediti = "";
			}
			$template->assign("motivazione_crediti", $motivazione_crediti);

			//creo l'array per la combo(select) delle ore di assenza
			$ore_assenza = [];
			for($cont = 0; $cont < 300; $cont++)
			{
				$ore_assenza[$cont] = $cont;
			}
			$template->assign("ore_assenza", $ore_assenza);
			$stato_secondario = "foglio_notizie_studente_display";
			$template->assign("id_classe", $id_classe);
			$template->assign("id_studente", $id_studente);
			$cognome_stud = str_replace("\\", "", $cognome_stud);
			$nome_stud = str_replace("\\", "", $nome_stud);
			$template->assign("nome_stud", $dati_studente[3]);
			$template->assign("cognome_stud", $dati_studente[4]);
			$template->assign("periodo", $periodo);
			$template->assign("classe", $classe);
			$template->assign("periodo", $periodo);
			$template->assign("stato_secondario", $stato_secondario);
		}
		//}}} </editor-fold>
		break;
	case "consiglio_classe_update":
		//{{{ <editor-fold defaultstate="collapsed">
		cambia_stato_consiglio_di_classe((int) $id_classe, $azione, (int) $current_user);
		$cosa = "CAMBIATO STATO DI ATTIVAZIONE DEL CONSIGLIO DI CLASSE IN " . $azione . " PER LA CLASSE CON ID:" . $id_classe;
		inserisci_log_storico((int) $current_user, "GESTIONE_PAGELLE", $cosa);
		$stato_secondario = 'chiusura_scrutini';
		//}}} </editor-fold>
		//break omesso volutamente
	case "chiusura_scrutini":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			$dati_classe = estrai_classe((int) $id_classe);
			$template->assign("dati_classe", $dati_classe);

			if($stato_chiusura == "update")
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="definizione stringa">
				$stringa_chiusura = "";
				if($periodo_1 == 1)
				{
					$stringa_chiusura = "1#SI";
				}
				else
				{
					$stringa_chiusura = "1#NO";
				}

				if($periodo_2 == 1)
				{
					$stringa_chiusura .= "@2#SI";
				}
				else
				{
					$stringa_chiusura .= "@2#NO";
				}

				if($periodo_3 == 1)
				{
					$stringa_chiusura .= "@3#SI";
				}
				else
				{
					$stringa_chiusura .= "@3#NO";
				}

				if($periodo_4 == 1)
				{
					$stringa_chiusura .= "@4#SI";
				}
				else
				{
					$stringa_chiusura .= "@4#NO";
				}

				if($periodo_5 == 1)
				{
					$stringa_chiusura .= "@5#SI";
				}
				else
				{
					$stringa_chiusura .= "@5#NO";
				}

				if($periodo_6 == 1)
				{
					$stringa_chiusura .= "@6#SI";
				}
				else
				{
					$stringa_chiusura .= "@6#NO";
				}

				if($periodo_7 == 1)
				{
					$stringa_chiusura .= "@7#SI";
				}
				else
				{
					$stringa_chiusura .= "@7#NO";
				}

				if($periodo_8 == 1)
				{
					$stringa_chiusura .= "@8#SI";
				}
				else
				{
					$stringa_chiusura .= "@8#NO";
				}

				if($periodo_9 == 1)
				{
					$stringa_chiusura .= "@9#SI";
				}
				else
				{
					$stringa_chiusura .= "@9#NO";
				}

				if($periodo_21 == 1)
				{
					$stringa_chiusura .= "@21#SI";
				}
				else
				{
					$stringa_chiusura .= "@21#NO";
				}

				if($periodo_22 == 1)
				{
					$stringa_chiusura .= "@22#SI";
				}
				else
				{
					$stringa_chiusura .= "@22#NO";
				}

				if($periodo_23 == 1)
				{
					$stringa_chiusura .= "@23#SI";
				}
				else
				{
					$stringa_chiusura .= "@23#NO";
				}

				if($periodo_24 == 1)
				{
					$stringa_chiusura .= "@24#SI";
				}
				else
				{
					$stringa_chiusura .= "@24#NO";
				}

				if($periodo_25 == 1)
				{
					$stringa_chiusura .= "@25#SI";
				}
				else
				{
					$stringa_chiusura .= "@25#NO";
				}

				if($periodo_26 == 1)
				{
					$stringa_chiusura .= "@26#SI";
				}
				else
				{
					$stringa_chiusura .= "@26#NO";
				}

				if($periodo_27 == 1)
				{
					$stringa_chiusura .= "@27#SI";
				}
				else
				{
					$stringa_chiusura .= "@27#NO";
				}

				if($periodo_28 == 1)
				{
					$stringa_chiusura .= "@28#SI";
				}
				else
				{
					$stringa_chiusura .= "@28#NO";
				}

				if($periodo_29 == 1)
				{
					$stringa_chiusura .= "@29#SI";
				}
				else
				{
					$stringa_chiusura .= "@29#NO";
				}
				//}}} </editor-fold>
				$result = aggiorna_chiusura_scrutini((int) $id_classe, $stringa_chiusura, (int) $current_user);
				$messaggio = "Dati aggiornati con successo!";
				$template->assign("messaggio", $messaggio);
			}
			$chiusura = estrai_chiusura_scrutini((int) $id_classe);
			$template->assign("chiusura", $chiusura);

			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "stampa_riepilogo_debiti_display":
		//{{{ <editor-fold defaultstate="collapsed">
		if($id_classe != "")
		{
			//ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
			$elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
			$template->assign("classi_ind", $elenco_classi_ind);
			$template->assign("indirizzo", $indirizzo);
			$template->assign("id_indirizzo", $id_indirizzo);
			//estraggo le materie della classe
			$elenco_materie_classe = estrai_materie_classe((int) $id_classe);
			$template->assign("elenco_materie", $elenco_materie_classe);

			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			$template->assign("elenco_studenti", $elenco_studenti_classe);
			$template->assign("classe", $classe);
			$template->assign("id_classe", $id_classe);
		}
		//}}} </editor-fold>
		break;
	case "stampa_riepilogo_debiti_update":
		//{{{ <editor-fold defaultstate="collapsed">
		//estraggo i dati da passare alla stampa
		if($id_classe >= 0)
		{
			$elenco_debiti_studenti_classe = estrai_debiti_classe((int) $id_classe, $anno_scolastico_selezionato);
			$dati_classe = estrai_classe($id_classe);
			$elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
			if(intval($dati_classe["id_sede"]) > 0)
			{
				$dati_sede = estrai_sede_singola($dati_classe["id_sede"]);
				$intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];
				$indirizzo_scuola = $dati_sede["indirizzo"];
				$citta = $dati_sede["descrizione_comune"];
				$telefono = $dati_sede["telefono"];
				$tipo_istituto_abbreviato = $dati_sede["tipo_istituto_abbreviato"];
				$tipo_istituto_esteso = $dati_sede["descrizione_tipo_scuola"];
			}

			if($formato_pagina == "A4")
			{
				if($orientamento_pagina == "P")
				{
					$larghezza_pagina = 210;
					$altezza_pagina = 290;
				}
				else
				{
					$larghezza_pagina = 290;
					$altezza_pagina = 203;
				}
			}
			else
			{
				if($orientamento_pagina == "P")
				{
					$larghezza_pagina = 290;
					$altezza_pagina = 405;
				}
				else
				{
					$larghezza_pagina = 420;
					$altezza_pagina = 290;
				}
			}

            $pdf = new NEXUS_PDF($orientamento_pagina,'mm',$formato_pagina);
			//Data loading
			$pdf->AddPage();

			$pdf->SetFont('helvetica', 'B', 14);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione intestazione
			$pdf->SetXY(10, 10);
			$pdf->CellFitScale(0, 6, $intestazione_stampa, 0, 1, 'C');

			$pdf->SetFont('helvetica', 'B', 10);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione sotto intestazione
			$pdf->SetXY(10, 16);
			$pdf->CellFitScale(0, 6, $indirizzo . ' -- ' . $citta . ' -- ' . $telefono, 0, 1, 'C');

			$pdf->SetFont('helvetica', '', 12);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione intestazione tabella
			$pdf->SetXY(10, 30);
			$pdf->CellFitScale(0, 10, 'Riepilogo debiti studenti', 0, 1, 'L');

			$pdf->SetFont('helvetica', '', 12);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione sotto intestazione tabella
			$pdf->SetXY(10, 40);
			$pdf->CellFitScale(0, 10, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 1, 'L');

			$pdf->SetFont('helvetica', '', 12);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione a.s.
			$pdf->SetXY(10, 40);
			$pdf->CellFitScale(0, 10, 'A.S. ' . $anno_inizio . "/" . $anno_fine, 0, 1, 'R');

			$pdf->SetFont('helvetica', 'B', 6);
            $pdf->SetTextColor(0, 0, 0);
			//setto posizione colonna1
			$pdf->SetXY(10, 50);
			$pdf->CellFitScale(5, 4, 'N°', 'LTR', 0, 'C');
			$testo_excel = 'N°' . chr(9);

			$pdf->SetXY(15, 50);
			$pdf->CellFitScale(30, 4, 'NOME', 'LTR', 0, 'C');
			$testo_excel .= 'COGNOME' . chr(9);
			$testo_excel .= 'NOME' . chr(9);

			$pdf->SetXY(45, 50);
			$pdf->CellFitScale(80, 4, 'DEBITI', 'LTR', 0, 'C');
			$testo_excel .= 'DEBITO 1' . chr(9);
			$testo_excel .= 'DEBITO 2' . chr(9);
			$testo_excel .= 'DEBITO 3' . chr(9);
			$testo_excel .= 'DEBITO 4' . chr(9);
			$testo_excel .= 'DEBITO 5' . chr(9);
			$testo_excel .= 'DEBITO 6' . chr(9);
			$testo_excel .= 'DEBITO 7' . chr(9);
			$testo_excel .= 'DEBITO 8' . chr(9);
			$testo_excel .= 'DEBITO 9' . chr(9);
			$testo_excel .= 'DEBITO 10' . chr(9);
			$testo_excel = substr($testo_excel, 0, -1);
			$testo_excel .= chr(13) . chr(10);

			$pdf->SetXY(125, 50);
			$pdf->CellFitScale(0, 4, 'DEB. RECUPERO AUTON.', 'LTR', 0, 'C');

			$tot_larghezza_usata = 35;
			$altezza_ridotta = 100;
			$altezza_cella = 4; //($altezza_pagina - $altezza_ridotta) / intval(count($elenco_studenti_classe));
			$altezza_riga = 54;

			$cont_tot_comma4a = 0;
			$cont_tot_comma4b = 0;

			$pdf->SetFillColor(224, 235, 255);
			for($cont_stud = 0; $cont_stud < count($elenco_debiti_studenti_classe); $cont_stud++)
			{
				$temp_y = $pdf->GetY();
				$temp_y += (count($elenco_debiti_studenti_classe[$cont_stud]["debiti"]) * 4);
				if($temp_y >= ($altezza_pagina - 20))
				{
					$pdf->AddPage();
					//{{{ <editor-fold defaultstate="collapsed" desc="intestazione">
					$pdf->SetFont('helvetica', 'B', 14);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione intestazione
					$pdf->SetXY(10, 10);
					$pdf->CellFitScale(0, 6, $intestazione_stampa, 0, 1, 'C');

					$pdf->SetFont('helvetica', 'B', 10);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione sotto intestazione
					$pdf->SetXY(10, 16);
					$pdf->CellFitScale(0, 6, $indirizzo . ' -- ' . $citta . ' -- ' . $telefono, 0, 1, 'C');

					$pdf->SetFont('helvetica', '', 12);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione intestazione tabella
					$pdf->SetXY(10, 30);
					$pdf->CellFitScale(0, 10, 'Riepilogo debiti studenti', 0, 1, 'L');

					$pdf->SetFont('helvetica', '', 12);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione sotto intestazione tabella
					$pdf->SetXY(10, 40);
					$pdf->CellFitScale(0, 10, 'CLASSE ' . $dati_classe[2] . $dati_classe[3] . " " . $dati_classe[5], 0, 1, 'L');

					$pdf->SetFont('helvetica', '', 12);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione a.s.
					$pdf->SetXY(10, 40);
					$pdf->CellFitScale(0, 10, 'A.S. ' . $anno_inizio . "/" . $anno_fine, 0, 1, 'R');

					$pdf->SetFont('helvetica', 'B', 6);
                    $pdf->SetTextColor(0, 0, 0);
					//setto posizione colonna1
					$pdf->SetXY(10, 50);
					$pdf->CellFitScale(5, 4, 'N°', 'LTR', 0, 'C');
					$testo_excel .= 'N°' . chr(9);

					$pdf->SetXY(15, 50);
					$pdf->CellFitScale(30, 4, 'NOME', 'LTR', 0, 'C');
					$testo_excel .= 'COGNOME' . chr(9);
					$testo_excel .= 'NOME' . chr(9);

					$pdf->SetXY(45, 50);
					$pdf->CellFitScale(80, 4, 'DEBITI', 'LTR', 0, 'C');
					$testo_excel .= 'DEBITO 1' . chr(9);
					$testo_excel .= 'DEBITO 2' . chr(9);
					$testo_excel .= 'DEBITO 3' . chr(9);
					$testo_excel .= 'DEBITO 4' . chr(9);
					$testo_excel .= 'DEBITO 5' . chr(9);
					$testo_excel .= 'DEBITO 6' . chr(9);
					$testo_excel .= 'DEBITO 7' . chr(9);
					$testo_excel .= 'DEBITO 8' . chr(9);
					$testo_excel .= 'DEBITO 9' . chr(9);
					$testo_excel .= 'DEBITO 10' . chr(9);
					$testo_excel = substr($testo_excel, 0, -1);
					$testo_excel .= chr(13) . chr(10);

					$pdf->SetXY(125, 50);
					$pdf->CellFitScale(0, 4, 'DEB. RECUPERO AUTON.', 'LTR', 0, 'C');

					$tot_larghezza_usata = 35;
					$altezza_ridotta = 100;
					$altezza_cella = 4; //($altezza_pagina - $altezza_ridotta) / intval(count($elenco_studenti_classe));
					$altezza_riga = 54;

					$cont_tot_comma4a = 0;
					$cont_tot_comma4b = 0;

					$pdf->SetFillColor(224, 235, 255);
					//}}} </editor-fold>
				}
				$pdf->SetFont('helvetica', 'B', $dimensione_font_nomi);
                $pdf->SetTextColor(0, 0, 0);
				$fill = !$fill;
				$id_studente = $elenco_studenti_classe[$cont_stud][0];
				if($id_studente >= 0)
				{
					$cont_debiti_comma4a = 0;
					$cont_debiti_comma4b = 0;
					$tot_debiti_comma4a = 0;
					$tot_debiti_comma4b = 0;
					for($cont = 0; $cont < count($elenco_debiti_studenti_classe[$cont_stud]["debiti"]); $cont++)
					{
						if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_debito"] == "CON VERIFICA")
						{
							$tot_debiti_comma4a++;
						}
						if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_debito"] == "CON RECUPERO AUTONOMO")
						{
							$tot_debiti_comma4b++;
						}
					}

					if($tot_debiti_comma4a > $tot_debiti_comma4b)
					{
						$tot_deb = $tot_debiti_comma4a;
					}
					else
					{
						$tot_deb = $tot_debiti_comma4b;
					}

					$altezza_cella_debiti = $tot_deb * 4;
					if($altezza_cella_debiti > 4)
					{
						$altezza_cella = $altezza_cella_debiti;
					}
					else
					{
						$altezza_cella = 4;
					}

					$pdf->SetXY(10, $altezza_riga);
					$pdf->CellFitScale(5, $altezza_cella, $elenco_debiti_studenti_classe[$cont_stud]["registro"], 1, 0, 'R', $fill);
					$testo_excel .= $elenco_debiti_studenti_classe[$cont_stud]["registro"] . chr(9);

					$pdf->SetXY(15, $altezza_riga);
					$pdf->CellFitScale(30, $altezza_cella, $elenco_debiti_studenti_classe[$cont_stud]["cognome"] .
							' ' .
							$elenco_debiti_studenti_classe[$cont_stud]["nome"], 1, 0, 'L', $fill);
					$testo_excel .= $elenco_debiti_studenti_classe[$cont_stud]["cognome"] . chr(9);
					$testo_excel .= $elenco_debiti_studenti_classe[$cont_stud]["nome"] . chr(9);


					if($tot_debiti_comma4a == 0)
					{
						$altezza_4a = $altezza_cella;
						$pdf->SetXY(45, $altezza_riga);
						$pdf->CellFitScale(80, $altezza_4a, '', 1, 1, 'L', $fill);
					}

					if($tot_debiti_comma4b == 0)
					{
						$altezza_4b = $altezza_cella;
						$pdf->SetXY(125, $altezza_riga);
						$pdf->CellFitScale(0, $altezza_4b, '', 1, 1, 'L', $fill);
					}

					if($tot_debiti_comma4a > 0)
					{
						$altezza_4a = intval($altezza_cella / $tot_debiti_comma4a);
					}


					if($tot_debiti_comma4b > 0)
					{
						$altezza_4b = intval($altezza_cella / $tot_debiti_comma4b);
					}

					$pdf->SetTextColor(0, 0, 0);
					$pdf->SetFont('helvetica', '', 7);

                    for($cont = 0; $cont < count($elenco_debiti_studenti_classe[$cont_stud]["debiti"]); $cont++)
					{
						if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_debito"] == "CON VERIFICA")
						{
							$pdf->SetFont('helvetica', 'B', $dimensione_font_4a);
                            $altezza_agg = $altezza_4a * $cont_debiti_comma4a;
							$descrizione_totale = $elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["descrizione_materia"];
							if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["debito_recuperato"] == "SI")
							{
								$pdf->SetTextColor(0, 150, 0);
								$descrizione_totale .= " recuperato il " .
										date("d/m/Y", $elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["data_recupero_debito"]) .
										" nel " .
										$elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_appello"] .
										" appello";
							}
							else
							{
								$pdf->SetTextColor(150, 0, 0);
								$descrizione_totale .= 	" NON recuperato";

                                if ($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["data_recupero_debito"] > 0)
                                {
                                    $descrizione_totale .= 	" il " .
                                        date ("d/m/Y", $elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["data_recupero_debito"]) .
                                        " nel " . $elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_appello"] . " appello";
                                }
							}
							$pdf->SetXY(45, $altezza_riga + $altezza_agg);
							$pdf->CellFitScale(80, $altezza_4a, $descrizione_totale, 1, 0, 'L', $fill);
							$testo_excel .= $descrizione_totale . chr(9);
							$cont_debiti_comma4a++;
						}

						if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["tipo_debito"] == "CON RECUPERO AUTONOMO")
						{
							$pdf->SetFont('helvetica', 'B', $dimensione_font_4b);
                            $altezza_agg = $altezza_4b * $cont_debiti_comma4b;
							$descrizione_totale = $elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["descrizione_materia"];
							if($elenco_debiti_studenti_classe[$cont_stud]["debiti"][$cont]["debito_recuperato"] == "SI")
							{
								$pdf->SetTextColor(0, 150, 0);
								$descrizione_totale .= " recuperato";
							}
							else
							{
								$pdf->SetTextColor(150, 0, 0);
								$descrizione_totale .= " NON recuperato";
							}
							$pdf->SetXY(125, $altezza_riga + $altezza_agg);
							$pdf->CellFitScale(0, $altezza_4b, $descrizione_totale, 1, 0, 'L', $fill);
							$testo_excel .= $descrizione_totale . chr(9);
							$cont_debiti_comma4b++;
						}
					}
					$altezza_riga = $altezza_riga + $altezza_cella;
					$testo_excel = substr($testo_excel, 0, -1);
					$testo_excel .= chr(13) . chr(10);
				}
			}
			switch($tipo_file_esportato)
			{
				//{{{ <editor-fold defaultstate="collapsed" desc="esporta e pubblica pdf o xls">
				case "pdf":
					$pdf->Output('stampa_debiti_'. date('Y-m-d_H-i') .".pdf","D");
					exit;
					break;
				case "xls":
					//cancello tutte i file temporanei fatti da più di un'ora
					$dir = "tmp_xls";
					CleanFiles($dir);
					//creo i nuovi file temporanei
                    $file="stampa_debiti_". date('Y-m-d_H-i');
                    rename($dir . "/" . $file, $dir . "/" . $file . '.xls');
					$file.='.xls';
					//Salva il file xls come file
					$nuovo_nome = $dir . '/' . $file;
					$handle = fopen($nuovo_nome, "w");
					fwrite($handle, $testo_excel);
					fclose($handle);
					//Reindirizzamento JavaScript
					echo "<HTML><SCRIPT>document.location='$nuovo_nome';</SCRIPT></HTML>";
					break;
				//}}} </editor-fold>
			}
		}
		//}}} </editor-fold>
		break;
	case "gestione_debiti":
		//{{{ <editor-fold defaultstate="collapsed">
		$elenco_debiti_studenti_classe = estrai_debiti_classe($id_classe);

        switch($stato_debiti) {
            case "modifica_debiti":
                for($cont_studenti = 0; $cont_studenti < count($elenco_debiti_studenti_classe); $cont_studenti++)
                {
                    for($cont_debiti = 0; $cont_debiti < count($elenco_debiti_studenti_classe[$cont_studenti]["debiti"]); $cont_debiti++)
                    {
                        $id_debito_selezionato = $elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"];
                        $recupero_debito = ${"recupero_debito_" . $elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"]};
                        $tipo_appello = ${"tipo_appello_" . $elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"]};
                        $giorno_select = ${$elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"] . "Day"};
                        $mese_select = ${$elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"] . "Month"};
                        $anno_select = ${$elenco_debiti_studenti_classe[$cont_studenti]["debiti"][$cont_debiti]["id_debito"] . "Year"};
                        $data_selezionata = mktime(1, 1, 1, intval($mese_select), intval($giorno_select), intval($anno_select));

                        if(strlen($recupero_debito) > 0)
                        {
                            if ($tipo_appello  == 'NESSUNO') {
                                $data_selezionata = 0;
                            }

                            $modifica_debito = modifica_debito((int) $id_debito_selezionato, $recupero_debito, $tipo_appello, $data_selezionata, $current_user);
                        }
                    }
                }
                $stato_debiti = "";
                break;

            case "inserisci_debiti":
                if($id_materia != "-1")
                {
                    $dati_materia = estrai_dati_materia((int) $id_materia);
                    $nome_materia = encode($dati_materia[2]);
                }
                else
                {
                    $nome_materia = encode($nome_materia_scritta);
                }
                $recupero_debito = $tipo_debito == "CON RECUPERO AUTONOMO" ? "SI" : "NO";
                $inserisci_debito = inserisci_debito((int) $id_materia, $nome_materia, (int) $id_studente, $tipo_debito, $recupero_debito, $anno_scolastico_selezionato, $current_user, $tipo_carenza);
                $stato_debiti = "";
                break;

            case "modifica_debiti":
    			$inserisci_debito = elimina_debito((int) $id_debito, $current_user);
                $stato_debiti = "";
                break;

        }

		$elenco_materie = estrai_materie();
		$template->assign("elenco_materie", $elenco_materie);

		$elenco_debiti_studenti_classe = estrai_debiti_classe((int) $id_classe);
		$template->assign("elenco_debiti_studenti_classe", $elenco_debiti_studenti_classe);

		$template->assign("classi_ind", $elenco_classi_ind);
		$template->assign("indirizzo", $indirizzo);
		$template->assign("id_indirizzo", $id_indirizzo);

		$template->assign("classe", $classe);
		$template->assign("id_classe", $id_classe);
		$template->assign("stato_debiti", $stato_debiti);
		$template->assign("id_studente", $id_studente);

		$anno_partenza = $anno_inizio - 5;
		$anno_ultimo = $anno_fine;
		$template->assign("anno_partenza", $anno_partenza);
		$template->assign("anno_ultimo", $anno_ultimo);
		//}}} </editor-fold>
		break;
    case "multi_documenti":
        //{{{ <editor-fold defaultstate="collapsed">
        $elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
        $elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
        $periodo = ( $periodo != '' ? $periodo : 'finale' );

        if($upload_avvenuto == "SI")
        {
            foreach($elenco_studenti_classe as $studente)
            {
                $id_studente = $studente['id_studente'];
                $upload_id = 'file_' . $id_studente;
                $upload_file = $_FILES[$upload_id];
                // Se è presente un file
                if ($upload_file['error'] === 0)
                {
                    // Preparo i dati da mandare su Messenger
                    $file_name = ${'documento_' . $id_studente} != '' ? ${'documento_' . $id_studente} . '.' . pathinfo($upload_file['name'], PATHINFO_EXTENSION) : $upload_file['name'];

                    $contenuto_file = file_get_contents($upload_file['tmp_name']);
                    $recipients = messengerGetStudentsRecipients($id_studente);

                    $hidden = false;
                    switch (${'tipo_' . $id_studente}) {
                        case 5:
                            $tags = 'PAGELLE';
                            $periodo = $periodo;
                            break;
                    }
                    $year = date('m') > 8 ? date('Y') : date('Y') - 1;

                    messengerSaveFile([
                        'content'    => $contenuto_file,
                        'hidden'     => $hidden,
                        'mime'       => $upload_file['type'],
                        'name'       => $file_name,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_studente,
                            'year'   => $year . "/" . ($year + 1),
                            'period' => $periodo,
                        ],
                        'recipients' => $recipients,
                        'tags'       => [$tags]
                        ]
                    );
                }
			}

			if (isset($documenti_da_eliminare)) {
				foreach ($documenti_da_eliminare as $id) {
					messengerDeleteDoc($id);
				}
			}
        }

        $elenco_studenti_documenti = [];
        foreach ($elenco_studenti_classe as $studente)
        {
            $id_studente = $studente['id_studente'];

            // Estraggo i documenti dello studente
            $elenco_documenti = messengerFindDocs(null, null, (int) $id_studente, ['PAGELLE']);
            foreach ($elenco_documenti as $key => $value) {
                switch($value['tags']) {
                    case '{PAGELLE}':
                        $elenco_documenti[$key]['usage_type'] = 5;
                        break;
                }
            }

            $elenco_studenti_documenti[$id_studente] = [
                    'id_studente'   => $id_studente,
                    'cognome'       => $studente['cognome'],
                    'nome'          => $studente['nome'],
                    'registro'      => $studente['registro'],
                    'documenti'     => $elenco_documenti
                ];
        }

        //ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
        $template->assign("classi_ind",$elenco_classi_ind);
        $template->assign("indirizzo",$indirizzo);
        $template->assign("id_indirizzo",$id_indirizzo);
        //e di far chiudere l'elenco degli studenti

        $template->assign("elenco_studenti_documenti",$elenco_studenti_documenti);
		$template->assign("num_stud",count($elenco_studenti_classe));
		$template->assign("max_num_stud",ini_get('max_file_uploads'));
        $template->assign("classe",$classe);
        $template->assign("id_classe",$id_classe);
        $studente_stato = "multi_documenti";
        $template->assign("studente_stato",$studente_stato);

        //}}} </editor-fold>
		break;
    case "multi_consiglio_orientativo":
        $elenco_classi_ind = estrai_classi_indirizzo((int) $id_indirizzo);
        $elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
        $elenco_caratteristiche = estrai_consiglio_orientativo_caratteristiche();
        $solo_aree_di_interesse = array_filter($elenco_caratteristiche, function($caratteristica) {
            return ($caratteristica['categoria'] == 'AREA_INTERESSI');
        });

        if($salva_consiglio == 'SI'){
            foreach($elenco_studenti_classe as $studente){
                // consigli e data
                $dati_studente = [];
                $dati_studente['id_consiglio_orientativo'] = ${'consiglio_' . $studente['id_studente']};
                $dati_studente['id_studente'] = $studente['id_studente'];

                $consiglio1 = (${'consiglio_' . $studente['id_studente']} != 'X') ? ${'consiglio_' . $studente['id_studente']} : 0;
                $consiglio2 = (${'consiglio2_' . $studente['id_studente']} != 'X') ? ${'consiglio2_' . $studente['id_studente']} : 0;
                $consiglio3 = (${'consiglio3_' . $studente['id_studente']} != 'X') ? ${'consiglio3_' . $studente['id_studente']} : 0;

                $query = "UPDATE studenti SET id_consiglio_orientativo = " . $consiglio1 . " WHERE id_studente = " . $dati_studente['id_studente'];
                pgsql_query($query) or die("Invalid $query");

                $query = "UPDATE studenti SET id_consiglio_orientativo2 = " . $consiglio2 . " WHERE id_studente = " . $dati_studente['id_studente'];
                pgsql_query($query) or die("Invalid $query");

                $query = "UPDATE studenti SET id_consiglio_orientativo3 = " . $consiglio3 . " WHERE id_studente = " . $dati_studente['id_studente'];
                pgsql_query($query) or die("Invalid $query");

                $data_consiglio = (${'data_consiglio_' . $studente['id_studente']} != '') ? strtotime(${'data_consiglio_' . $studente['id_studente']}) : 0;
                $query = "UPDATE studenti SET data_consiglio_orientativo = " . $data_consiglio . " WHERE id_studente = " . $dati_studente['id_studente'];
                pgsql_query($query) or die("Invalid $query");

                // caratteristiche
                // prima le cancello tutte poi le riattivo se selezionate
                cancella_abbinamenti_consiglio_orientativo_studente($studente['id_studente'], $current_user, $current_key);

                foreach ($caratteristica[$studente['id_studente']] as $id_caratteristica_abbinamento){
                    inserisci_abbinamento_consiglio_orientativo_studente($studente['id_studente'], $id_caratteristica_abbinamento, $caratteristica_testo[$studente['id_studente']][$id_caratteristica_abbinamento], $current_user, $current_key);
                }
            }
            $elenco_studenti_classe = estrai_studenti_classe((int) $id_classe);
        }

        $elenco_studenti_consiglio_orientativo = [];
        $abbinamenti_caratteristiche_studenti = [];
        foreach ($elenco_studenti_classe as $studente)
        {
            $elenco_studenti_consiglio_orientativo[$studente['id_studente']] = [
                    'id_studente'           => $studente['id_studente'],
                    'cognome'               => $studente['cognome'],
                    'nome'                  => $studente['nome'],
                    'registro'              => $studente['registro'],
                    'id_consiglio_orientativo' => $studente['id_consiglio_orientativo'],
                    'id_consiglio_orientativo2' => $studente['id_consiglio_orientativo2'],
                    'id_consiglio_orientativo3' => $studente['id_consiglio_orientativo3'],
                    'data_consiglio_orientativo' => ($studente['data_consiglio_orientativo'] > 0) ? date('Y-m-d', $studente['data_consiglio_orientativo']) : '',
                    'requisiti_soddisfatti'	=> false
                ];

            $abbinamenti_caratteristiche = estrai_abbinamenti_consiglio_orientativo_studente($studente['id_studente'], $current_user, $current_key);

            foreach ($abbinamenti_caratteristiche as $abbinamento){
                $abbinamenti_caratteristiche_studenti[$studente['id_studente']][$abbinamento['id_caratteristica']]['id_caratteristica'] = $abbinamento['id_caratteristica'];
                $abbinamenti_caratteristiche_studenti[$studente['id_studente']][$abbinamento['id_caratteristica']]['testo'] = $abbinamento['testo'];
                $elenco_studenti_consiglio_orientativo[$studente['id_studente']]['caratteristiche_abbinate'][] = $abbinamento['id_caratteristica'];
            }

            // verifico se lo studente ha i requisiti minimi soddisfatti per l'invio
            $requisito1 = false;
            $requisito2 = false;

            // 1 - almeno un consiglio orientativo selezionato
            if ($studente['id_consiglio_orientativo'] > 0 || $studente['id_consiglio_orientativo2'] > 0 || $studente['id_consiglio_orientativo3'] > 0){
                $requisito1 = true;
            }

            // 2 - almeno un'area di interesse abbinata
            foreach ($elenco_studenti_consiglio_orientativo[$studente['id_studente']]['caratteristiche_abbinate'] as $id_car_abbinata){
                if (in_array($id_car_abbinata, array_column($solo_aree_di_interesse, 'id_caratteristica'))){
                    $requisito2 = true;
                    break;
                }
            }

            if ($requisito1 && $requisito2){
                $elenco_studenti_consiglio_orientativo[$studente['id_studente']]['requisiti_soddisfatti'] = true;
            }
        }

        //ripeto questa parte per evitare di far chiudere l'elenco aperto delle classi
        $template->assign("classi_ind",$elenco_classi_ind);
        $template->assign("indirizzo",$indirizzo);
        $template->assign("id_indirizzo",$id_indirizzo);
        //e di far chiudere l'elenco degli studenti

        $param_consigli = [
                'anno_scolastico' => str_replace('/', '_', estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE'))
            ];
        $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);

        $template->assign("elenco_studenti_consiglio_orientativo",$elenco_studenti_consiglio_orientativo);
        $template->assign("array_consigli",$elenco_consigli);
        $template->assign("elenco_caratteristiche", $elenco_caratteristiche);
        $template->assign("abbinamenti_caratteristiche_studenti", $abbinamenti_caratteristiche_studenti);
        $template->assign("num_stud",count($elenco_studenti_classe));
        $template->assign("classe",$classe);
        $template->assign("id_classe",$id_classe);
        $studente_stato = "multi_consiglio_orientativo";
        $template->assign("studente_stato",$studente_stato);
            break;
	default:
		$stato_espansione = 1;
		break;
}
$template->assign("stato_espansione", $stato_espansione);
$template->assign("stato_secondario", $stato_secondario);

$db_key = $db_official;
