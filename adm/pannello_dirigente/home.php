<?php
$tempo_inizio_totale = microtime(true);
ppre("=== INIZIO ANALISI TEMPI CARICAMENTO HOME DIRIGENTE ===");

$tempo_inizio_sezione = microtime(true);
if (count($limiti_indirizzi_utente) > 0) {
    $limiti_indirizzi = ['indirizzi' => array_column($limiti_indirizzi_utente, 'id_indirizzo')];
} else {
    $limiti_indirizzi = 'nessuna';
}

$classi_limiti = estrai_classi('base', $limiti_indirizzi);
$id_classi_limiti = array_column($classi_limiti, 'id_classe');

$compleanni_registro = estrai_parametri_singoli('COMPLEANNI_REGISTRO');
$template->assign('compleanni_registro', $compleanni_registro);
$santi_registro = estrai_parametri_singoli('SANTI_REGISTRO');
$template->assign('santi_registro', $santi_registro);

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 1 - Impostazione limiti e parametri base: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");

//{{{ <editor-fold defaultstate="collapsed" desc="Impostazione dati base, date e ore">
$tempo_inizio_sezione = microtime(true);
if (isset($giorno_scelto) && $giorno_scelto > 0) {
    $timestamp_controllo = $giorno_scelto;
} elseif ($data_da_calendario != '') {
    $array_giorno = explode("/", $data_da_calendario);
    $timestamp_controllo = mktime(0, 0, 1, $array_giorno[1], $array_giorno[0], $array_giorno[2]);
} else {
    $timestamp_controllo = time();
}

//imposto gli orari in base al giorno; se precedente al vero attuale, successivo o è lo stesso
if (date("Y-m-d", $timestamp_controllo) != date("Y-m-d", time())) {
    // le 23:59, ossia prendere tutta la giornata, è necessario per far apparire il grafico delle ore nelle giornate nelle quali ce ne sono
    // se imposto le 00:00 non trova ore nelle estrazioni successive e dice che non ce ne sono quel giorno, nonostante possano essercene
    $timestamp_controllo = strtotime("23:59:00", $timestamp_controllo);

    if (date("Y-m-d", $timestamp_controllo) > date("Y-m-d", time())) {
        $giorno_futuro = true;
        $giorno_passato = false;
    } else {
        $giorno_futuro = false;
        $giorno_passato = true;
    }
} else {
    $timestamp_controllo = time();
    $oggi = "SI"; //flag per identificare che la data che si sta visualizzando è quella della giornata reale
    $template->assign('oggi', $oggi);
}

$ora_controllo = date("H:i", $timestamp_controllo);
$giorno_controllo = date('d', $timestamp_controllo);
$mese_controllo = date('m', $timestamp_controllo);
$anno_controllo = date('Y', $timestamp_controllo);
$inizio_giorno = mktime(0, 0, 0, $mese_controllo, $giorno_controllo, $anno_controllo);
$fine_giorno = mktime(23, 59, 59, $mese_controllo, $giorno_controllo, $anno_controllo);

$anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
$anno_scolastico = explode("/", $anno_scolastico_attuale);
$inizio_anno_scolastico = mktime(1, 1, 1, 9, 1, $anno_scolastico[0]);

// giorno precedente e successivo
$giorno_precedente = strtotime("-1 day", $timestamp_controllo);
$giorno_successivo = strtotime("+1 day", $timestamp_controllo);

$template->assign('giorno_precedente', $giorno_precedente);
$template->assign('giorno_successivo', $giorno_successivo);

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 2 - Impostazione dati base, date e ore: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>


//{{{ <editor-fold defaultstate="collapsed" desc="Data, Santi e Compleanni del giorno">
$tempo_inizio_sezione = microtime(true);
$elenco_studenti_istituto = estrai_studenti_istituto('rapida', '', '', $limiti_indirizzi);
$id_studenti_istituto = array_column($elenco_studenti_istituto, 'id_studente');

$data['giorno'] = $giorno_controllo;
$data['giorno2'] = date("j", $timestamp_controllo);
$data['mese'] = $mese_controllo;
$data['mese_tradotto'] = traduci_mese_in_lettere($mese_controllo, 'esteso');
$data['anno'] = $anno_controllo;
$data['giorno_tradotto'] = traduci_giorno_in_lettere(date("w", $timestamp_controllo), true);
$data['data'] = date("d/m/Y", $timestamp_controllo);

$data['santi'] = estrai_santi($giorno_controllo, $mese_controllo);

foreach ($elenco_studenti_istituto as $studente) {
    $giorno = date('d', $studente['data_nascita']);
    $mese = date('m', $studente['data_nascita']);
    if ($giorno == $giorno_controllo && $mese == $mese_controllo) {
        $data['compleanni'][$studente['id_studente']] = $studente;
        $data['compleanni'][$studente['id_studente']]['anni_compiuti'] = $anno_controllo - date('Y', $studente['data_nascita']);
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 3 - Data, Santi e Compleanni del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Assenze del giorno">
$tempo_inizio_sezione = microtime(true);
// 1 - giornaliera
// 2 - entrata ritardo
// 3 - uscita anticipo
// 6 - assenza mattino
// 7 - assenza pomeriggio
// 8 - entrata in ritardo al pomeriggio
// 9 - uscita in anticipo al pomeriggio
// 12 - entrata ritardo minore mattino
// 18 - entrata ritardo minore pomeriggio
$assenti_del_giorno = estrai_assenze_studenti_istituto($inizio_giorno, $fine_giorno, [], false, 'NO');
$assenze_giornaliere = [];

foreach ($assenti_del_giorno as $id_studente => $assenze) {
    if (in_array($id_studente, $id_studenti_istituto)){
        foreach ($assenze as $key => $assenza) {
            if ($assenza['data_prenotazione'] == 0) {
                switch ($assenza['tipo_assenza']) {
                    case "1":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Assenza giornaliera';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "2":
                    case "8":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Entrata in ritardo';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "3":
                    case "9":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Uscita in anticipo';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "6":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Assenza solo al mattino';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "7":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Assenza solo al pomeriggio';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "12":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Entrata in ritardo minore al mattino';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                    case "18":
                        $assenze_giornaliere[$assenza['tipo_assenza']]['tipo_assenza_tradotta'] = 'Entrata in ritardo minore al pomeriggio';
                        $assenze_giornaliere[$assenza['tipo_assenza']]['studenti'][] = $assenza;
                        break;
                }
            } else {
                $assenze_giornaliere['entrate_uscite_prenotate']['tipo_assenza_tradotta'] = print_label('Entrate/uscite prenotate');
                switch ($assenza['tipo_assenza']) {
                    case "2":
                    case "8":
                    case "12":
                    case "18":
                        $assenza['orario_tipologia'] = print_label('entrata') . ' ' . $assenza['orario'];
                        break;
                    case "3":
                    case "9":
                        $assenza['orario_tipologia'] = print_label('uscita') . ' ' . $assenza['orario'];
                        break;
                }
                $assenze_giornaliere['entrate_uscite_prenotate']['studenti'][] = $assenza;
            }
        }
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 4 - Assenze del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Firme fatte fino ad ora">
$tempo_inizio_sezione = microtime(true);
$ore_della_giornata = estrai_orario_periodo($inizio_giorno, $fine_giorno);
$cont_ore_firme['ore'] = 0;
$cont_ore_firme['firme'] = 0;
$max_ora_fine = 0;

if (!empty($ore_della_giornata)) {
    foreach ($ore_della_giornata as $ora) {
        if(in_array($ora['id_classe'], $id_classi_limiti)){
            if ($ora['data_inizio'] < $timestamp_controllo) {
                $ore_fino_ad_adesso[] = $ora;
                if ($ora['data_fine'] > $max_ora_fine) {
                    $max_ora_fine = $ora['data_fine'];
                }
            }
        }
    }
}

$firme_fino_ad_adesso = estrai_firme_periodo($inizio_giorno, $max_ora_fine);

if (!empty($firme_fino_ad_adesso)) {
    foreach ($ore_fino_ad_adesso as $ora) {
        if (in_array($ora['id_classe'], $id_classi_limiti)) {
            foreach ($firme_fino_ad_adesso as $key => $firma) {
                if ($firma['data'] >= $ora['data_inizio'] && $firma['data'] <= $ora['data_fine']) {
                    $cont_ore_firme['firme']++;
                    unset($firme_fino_ad_adesso[$key]);
                    break;
                }
            }
        }
    }
}

$cont_ore_firme['ore'] = count($ore_fino_ad_adesso);

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 5 - Firme fatte fino ad ora: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Brutti voti del giorno">
$tempo_inizio_sezione = microtime(true);
$scala_voti = estrai_parametri_singoli('SCALA_VOTI');
$dati_scala_voti = estrai_scala_voti($scala_voti);
$voto_minimo_suff = $dati_scala_voti['voto_minimo_suff'];

$elenco_voti = estrai_voti_periodo($inizio_giorno, $fine_giorno, 'voto');
$insufficienti = [];

if (!empty($elenco_voti)) {
    foreach ($elenco_voti as $voto) {
        if (in_array($voto['id_classe'], $id_classi_limiti)) {
            if (
                $voto['voto'] != 'A'
                &&
                ($voto['voto'] < $voto_minimo_suff)
            ) {
                $insufficienti[$voto['id_voto']] = $voto;

                switch ($voto['tipo']) {
                    case '1':
                        $insufficienti[$voto['id_voto']]['tipo_voto_tradotto'] = 'orale';
                        break;
                    case '2':
                        $insufficienti[$voto['id_voto']]['tipo_voto_tradotto'] = 'pratico';
                        break;
                    case '0':
                    default:
                        $insufficienti[$voto['id_voto']]['tipo_voto_tradotto'] = 'scritto/grafico';
                        break;
                }

                $insufficienti[$voto['id_voto']]['data_voto_tradotta'] = date("d/n/Y", $voto['data']);
            }
        }
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 6 - Brutti voti del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Note disciplinari del giorno">
$tempo_inizio_sezione = microtime(true);
$parametri_ricerca = [
    "start_interval"    =>  $inizio_giorno,
    "end_interval"      =>  $fine_giorno,
    "tipo_data"         =>  'data_inserimento',
    "filtra_classi"     =>  $id_classi_limiti
];
$note_disciplinari_tmp = ricerca_note_disciplinari($parametri_ricerca);

if (!empty($note_disciplinari_tmp)) {
    foreach ($note_disciplinari_tmp as $key => $nota) {
        $nota['data_tradotta'] = date("d/n/Y - H:i", $nota['data']);
        $nota['elenco_classi'] = implode(", ", $nota['elenco_nomi_classi']);
        $nota['elenco_studenti'] = implode(", ", $nota['elenco_nomi_soggetti']);

        $considera = false;
        if (empty($limiti_indirizzi_utente)) {
            $considera = true;
        } else {
            $id_classi_nota = array_keys($nota['elenco_nomi_classi']);

            foreach ($id_classi_nota as $id_classe_nota) {
                if (in_array($id_classe_nota, $id_classi_limiti)) {
                    $considera = true;
                    break;
                }
            }
        }

        if ($considera) {
            $note_disciplinari[] = $nota;
        }
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 7 - Note disciplinari del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Note disciplinari ancora non valutate">
$tempo_inizio_sezione = microtime(true);
$parametri_ricerca = [
    "start_interval"    =>  $inizio_anno_scolastico,
    "end_interval"      =>  $timestamp_controllo,
    "filtra_classi"     =>  $id_classi_limiti
];
$note_tutte = ricerca_note_disciplinari($parametri_ricerca, 'NO');
$statistiche_note['valutate'] = 0;
$statistiche_note['pubblicate'] = 0;
$statistiche_note['non_pubblicate'] = 0;
$statistiche_note['non_valutate'] = 0;

if (!empty($note_tutte)) {
    foreach ($note_tutte as $key => $nota) {
        if ($nota['stato'] == '') {
            $statistiche_note['non_valutate']++;
        } elseif ($nota['stato'] == 'pubblica') {
            $statistiche_note['pubblicate']++;
            $statistiche_note['valutate']++;
        } elseif ($nota['stato'] == 'no_pubblica') {
            $statistiche_note['non_pubblicate']++;
            $statistiche_note['valutate']++;
        }
    }

    $tot = $statistiche_note['valutate'] + $statistiche_note['non_valutate'];
    $statistiche_note['percentuale_valutate'] = 100 * $statistiche_note['valutate'] / $tot;
    $statistiche_note['percentuale_non_valutate'] = 100 - $statistiche_note['percentuale_valutate'];
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 8 - Note disciplinari ancora non valutate: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Eventi del giorno">
$tempo_inizio_sezione = microtime(true);
$elenco_eventi = estrai_elenco_eventi_agenda(0, $inizio_giorno, $fine_giorno);

if (!empty($elenco_eventi)) {
    foreach ($elenco_eventi as $numero_giorno_settimana => $giorno) {
        foreach ($giorno as $id_evento => $evento) {
            $tieni_evento = false;
            $classi = [];

            $elenco_eventi[$numero_giorno_settimana][$id_evento]['data_inizio_tradotta'] = date("d/n/Y H:i", $evento['data_inizio']);
            $elenco_eventi[$numero_giorno_settimana][$id_evento]['data_fine_tradotta'] = date("d/n/Y H:i", $evento['data_fine']);

            foreach ($evento['classi'] as $id_classe_evento => $classe) {
                if ($classe['ordinamento'] != 'CORSO'){
                    $classi[] = $classe['classe'];
                }

                if (in_array($id_classe_evento, $id_classi_limiti)) {
                    $tieni_evento = true;
                }
            }

            if ($tieni_evento) {
                $elenco_eventi[$numero_giorno_settimana][$id_evento]['elenco_classi'] = implode(", ", $classi);
            }
        }
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 9 - Eventi del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Corsi del giorno">
$tempo_inizio_sezione = microtime(true);
if ($corsi_pannello_dirigente == 'SI'){
    $elenco_corsi = estrai_corsi_periodo(date("Y-m-d", $timestamp_controllo), date("Y-m-d", $timestamp_controllo));
    $template->assign('corsi_pannello_dirigente', $corsi_pannello_dirigente);
    $template->assign('elenco_corsi', $elenco_corsi);
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 10 - Corsi del giorno: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

//{{{ <editor-fold defaultstate="collapsed" desc="Messaggi del giorno A e DA parenti">
$tempo_inizio_sezione = microtime(true);
$messaggi_a_parenti = $messaggi_da_parenti = [];

if ($versione_messenger == 'VERSIONE_2') {
    $payload = [
        "db_richiesto" => $db_key,
        "tipo_messaggio" => 'NORMALE',
        "data_inizio_ricerca" => $inizio_giorno,
        "data_fine_ricerca" => $fine_giorno,
        'elementi_x_pagina' => 10,
        'campi' => ["corpo" => "SI"],
        'estrai_per_dirigente' => 'SI'
    ];

    $res = nextapi_call('messaggistica/estrai_lista_messaggi', 'GET', $payload, $current_key);

    if (!empty($res)) {
        $messaggi_a_parenti = $res['messaggi_a_parenti'];
        $messaggi_da_parenti = $res['messaggi_da_parenti'];
        // foreach ($res as $mex) {
        //     $mex['data'] = date('d/m/Y', $mex['data_invio']);
        //     if ($mex['data'] == date('d/m/Y', time())) {
        //         $mex['data'] = print_label('oggi');
        //     } elseif ($mex['data'] == date('d/m/Y', strtotime('-1 day'))) {
        //         $mex['data'] = print_label('ieri');
        //     }
        //     $mex['ora'] = date('H:i', $mex['data_invios']);
        //     $mex['name'] = $mex['oggetto'];
        //     $mex['content'] = $mex['corpo'];
        //     $mex['owner_surname'] = $mex['label_mittente'];

        //     $messaggi_non_letti[] = $mex;
        // }
    }
} else {
    // -- CHIAMATA API
    $server_id = messengerGetParameter('SERVER_ID')[0]['value'];
    $salt = rand(1, 1000);

    $parametri = [
        'date_start'    => date("Y-m-d", $timestamp_controllo),
        'date_end'      => date("Y-m-d", $timestamp_controllo),
        '_authId'       => $salt,
        '_token'        => md5($server_id . $salt . date('Y-m-d') . time()),
    ];

    $url = "localhost/messenger/1.0/messages/parents";
    $response = chiamata_api($url, $parametri, 'GET');
    $messaggi = json_decode($response, true);
    // --

    if (is_array($messaggi) && $messaggi['success'] == 1) {
        if (!empty($messaggi['results']['recipients'])) {
            $cont = 0;
            foreach ($messaggi['results']['recipients'] as $mex) {
                $messaggi_a_parenti[$cont]['name'] = $mex['name'];
                $messaggi_a_parenti[$cont]['content'] = $mex['content'];
                $messaggi_a_parenti[$cont]['received'] = $mex['received'];
                $messaggi_a_parenti[$cont]['files'] = $mex['files'];
                $messaggi_a_parenti[$cont]['from'] = $mex['properties']['from'];

                $arr_parenti = $arr_docenti = [];
                foreach ($mex['properties']['to'] as $destinatario) {
                    if ($destinatario['group'] == 'Parenti') {
                        $arr_parenti[] = $destinatario;
                    } else {
                        $arr_docenti[] = $destinatario;
                    }
                }
                $messaggi_a_parenti[$cont]['to']['parenti'] = $arr_parenti;
                $messaggi_a_parenti[$cont]['to']['docenti'] = $arr_docenti;
                $cont++;
            }
        }

        if (!empty($messaggi['results']['owners'])) {
            $cont = 0;
            foreach ($messaggi['results']['owners'] as $mex) {
                $messaggi_da_parenti[$cont]['name'] = $mex['name'];
                $messaggi_da_parenti[$cont]['content'] = $mex['content'];
                $messaggi_da_parenti[$cont]['received'] = $mex['received'];
                $messaggi_da_parenti[$cont]['files'] = $mex['files'];
                $messaggi_da_parenti[$cont]['from'] = $mex['properties']['from'];
                $messaggi_da_parenti[$cont]['to'] = $mex['properties']['to'];
                $cont++;
            }
        }
    }
}

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 11 - Messaggi del giorno A e DA parenti: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");
//}}} </editor-fold>

$tempo_inizio_sezione = microtime(true);
$template->assign('ora_controllo', $ora_controllo);
$template->assign('data', $data);
$template->assign('assenze_giornaliere', $assenze_giornaliere);
$template->assign('elenco_eventi', $elenco_eventi);
$template->assign('insufficienti', $insufficienti);
$template->assign('note_disciplinari', $note_disciplinari);
$template->assign('statistiche_note', $statistiche_note);
$template->assign('cont_ore_firme', $cont_ore_firme);
$template->assign('messaggi_a_parenti', $messaggi_a_parenti);
$template->assign('messaggi_da_parenti', $messaggi_da_parenti);

$tempo_fine_sezione = microtime(true);
ppre("SEZIONE 12 - Assegnazione template finale: " . round(($tempo_fine_sezione - $tempo_inizio_sezione) * 1000, 2) . " ms");

$tempo_fine_totale = microtime(true);
ppre("=== TEMPO TOTALE CARICAMENTO HOME DIRIGENTE: " . round(($tempo_fine_totale - $tempo_inizio_totale) * 1000, 2) . " ms ===");