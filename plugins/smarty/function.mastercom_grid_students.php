<?php

function smarty_function_mastercom_grid_students($params, $content) {
    $calendar = null;
    $type = null;
    extract($params);
    $html = '';
    $sede_default = estrai_sedi_con_comune_e_provincia();
    $giudizio_sospeso_6_in_condotta = estrai_parametri_singoli("ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA");

    $trentino_abilitato = 'NO';
    foreach ($sede_default as $singola_sede) {
        if ($singola_sede['provincia'] === 'BZ' || $singola_sede['provincia'] === 'TN') {
            $trentino_abilitato = 'SI';
        }
    }
    if ($trentino_abilitato == 'SI') {
        $html .= "<input type = 'hidden' id = 'trentino_abilitato' value = '$trentino_abilitato'";
    } else {
        $html .= "<input type = 'hidden' id = 'trentino_abilitato' value = 'NO'";
    }

    if ($giudizio_sospeso_6_in_condotta == 'SI') {
        $html .= "<input type = 'hidden' id = 'giudizio_sospeso_6_in_condotta' value = '$giudizio_sospeso_6_in_condotta'";
    } else {
        $html .= "<input type = 'hidden' id = 'giudizio_sospeso_6_in_condotta' value = 'NO'";
    }

    if (is_array($mat_studenti) && is_array($mat_materie)) {
        if (stripos($_SERVER['HTTP_USER_AGENT'], 'msie') !== false) {
            $pref_classe = 'ie_';
            $browser = 'ie';
        } else {
            $pref_classe = '';
            $browser = 'moz';
        }

        if ($alto_contrasto == 'SI') {
            $pref_classe .= 'hc_';
        }

        if (!($schermo > 0)) {
            $schermo = 1024;
        }

        $width_nomi = 250;

        foreach ($mat_materie as $materia) {
            $cont_col += $materia['colonne'];
        }

        $col_width = floor(($schermo - $width_nomi) / $cont_col);

        if ($browser == 'ie') {
            $width_nomi = $width_nomi - ($cont_col + 5);
        }

        if ($schermo <= 800) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 15;
            } else {
                $font_size = 12;
                $row_height = 18;
            }
        } elseif ($schermo > 800 && $schermo < 1024) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 15;
            } else {
                $font_size = 12;
                $row_height = 18;
            }
        } elseif ($schermo >= 1024 && $schermo <= 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 12;
                $row_height = 18;
            } else {
                $font_size = 14;
                $row_height = 21;
            }
        } elseif ($schermo > 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 12;
                $row_height = 18;
            } else {
                $font_size = 14;
                $row_height = 21;
            }
        }

        switch ($periodo) {
            case '1':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '2':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '3':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '4':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '5':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '6':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '7':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '8':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '9':
                $nome_pagella = 'Pagella fine anno';
                break;
            case '10':
                $nome_pagella = 'Prove strutturate';
                break;
            case '11':
                $nome_pagella = 'Esami di licenza maestro d\'arte';
                break;
            case '21':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '22':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '23':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '24':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '25':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '26':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '27':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '28':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '29':
                $nome_pagella = 'Pagella fine anno';
                break;
        }

        //{{{ <editor-fold defaultstate="collapsed" desc="Verifica anomalie pagelle">
        $anomalie_voti = [];
        $anomalie_presenti = false;
        foreach ($mat_studenti as $studente) {
            // Recupero i dati studente
            $anomalie_voti[$studente['id_studente']]['studente'] = $studente['cognome'] . ' ' . $studente['nome'];
            $recuperi_insufficienze = ['presente' => null, 'assente' => null];

            // Ciclo le materie
            foreach ($mat_materie as $indice_materia => $materia) {
                // Dati del voto per materia
                $voto_materia = $mat_voti[$studente['id_studente']][$materia['id_materia']];

                //{{{ <editor-fold defaultstate="collapsed" desc="Controllo voti interi">
                // Voto Singolo
                if ($tipo_visualizzazione == 'voto_singolo' || ($tipo_visualizzazione == 'personalizzato' && $materia['tipo_voto_personalizzato'] == '1')) {
                    if (!is_numeric((float) $voto_materia['voto_pagellina']) || strpos($voto_materia['voto_pagellina'], ".") > 0) {
                        $anomalie_voti[$studente['id_studente']]['anomalie'][$materia['descrizione']][] = "uno o più voti inseriti non sono corretti: il voto in pagella non può avere decimali";
                        $anomalie_presenti = true;
                    }
                } else {
                    if (($materia['scritto'] == 1 && (!is_numeric((float) $voto_materia['voto_scritto_pagella']) || strpos($voto_materia['voto_scritto_pagella'], ".") > 0)) ||
                            ($materia['orale'] == 1 && (!is_numeric((float) $voto_materia['voto_orale_pagella']) || strpos($voto_materia['voto_orale_pagella'], ".") > 0)) ||
                            ($materia['pratico'] == 1 && (!is_numeric((float) $voto_materia['voto_pratico_pagella']) || strpos($voto_materia['voto_pratico_pagella'], ".") > 0))
                    ) {
                        $anomalie_voti[$studente['id_studente']]['anomalie'][$materia['descrizione']][] = "uno o più voti inseriti non sono corretti: il voto in pagella non può avere decimali";
                        $anomalie_presenti = true;
                    }
                }
                //}}} </editor-fold>
                //{{{ <editor-fold defaultstate="collapsed" desc="Controllo voti sufficienti - Recuperi">
                if ($trentino_abilitato == 'SI') {
                    // Verifico l'assenza di debiti?
                } else {
                    switch ($voto_materia['esito_recupero']) {
                        case 'NO':
                            $esito_recupero = "Negativo";
                            break;
                        case 'ASSENTE':
                            $esito_recupero = "Assente";
                            break;
                        case 'NI':
                            $esito_recupero = "Parziale";
                            break;
                        default:
                            $esito_recupero = "Mancante";
                            break;
                    }

                    if ($tipo_visualizzazione == 'voto_singolo' || ($tipo_visualizzazione == 'personalizzato' && $materia['tipo_voto_personalizzato'] == '1')) {
                        // Voto singolo: controllo se è sufficiente e se c'è un recupero non recuperato
                        if ($voto_materia['voto_pagellina'] >= $parametri_voto['voto_minimo_suff'] && $voto_materia['tipo_recupero'] != '' && $voto_materia['esito_recupero'] != 'SI') {
                            $anomalie_voti[$studente['id_studente']]['anomalie'][$materia['descrizione']][] = "il voto inserito è sufficiente ma è presente un recupero di tipo <b>'" . estrai_tipo_recupero_singolo($voto_materia['tipo_recupero']) . "'</b>  con esito <b>" . $esito_recupero . "</b>";
                            $anomalie_presenti = true;
                        }

                        // Voti insufficienti
                        if ($voto_materia['voto_pagellina'] < $parametri_voto['voto_minimo_suff'] && $voto_materia['voto_pagellina'] != '' && in_array($materia['in_media_pagelle'], ['SI', 'NO']) && !in_array($materia['tipo_materia'], ['RELIGIONE', 'ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO'])
                        ) {

                            if ($voto_materia['tipo_recupero'] != '' && $voto_materia['esito_recupero'] != 'SI') {
                                // Recupero presente ma non recuperato
                                if ($periodo != '9' and $periodo != '29') {
                                    $recuperi_insufficienze['presente'][] = $materia['descrizione'];
                                }
                            }
                        }
                    } else {
                        // Se non è voto singolo controllo:
                        // - Se c'è un recupero e non è recuperato
                        if ($voto_materia['tipo_recupero'] != '' && $voto_materia['esito_recupero'] != 'SI') {
                            $insuff = false;
                            // - Se almeno un voto tra scritto orale a pratico è insufficiente (e quel tipo voto è attivo)
                            if (($materia['scritto'] == 1 && $voto_materia['voto_scritto_pagella'] < $parametri_voto['voto_minimo_suff']) ||
                                    ($materia['orale'] == 1 && $voto_materia['voto_orale_pagella'] < $parametri_voto['voto_minimo_suff']) ||
                                    ($materia['pratico'] == 1 && $voto_materia['voto_pratico_pagella'] < $parametri_voto['voto_minimo_suff'])
                            ) {
                                $insuff = true;
                            }

                            // - Se non ci sono insufficienze controllo che ci siano sufficienze
                            if (!$insuff &&
                                    (
                                    ($materia['scritto'] == 1 && $voto_materia['voto_scritto_pagella'] >= $parametri_voto['voto_minimo_suff']) ||
                                    ($materia['orale'] == 1 && $voto_materia['voto_orale_pagella'] >= $parametri_voto['voto_minimo_suff']) ||
                                    ($materia['pratico'] == 1 && $voto_materia['voto_pratico_pagella'] >= $parametri_voto['voto_minimo_suff'])
                                    )
                            ) {
                                $anomalie_voti[$studente['id_studente']]['anomalie'][$materia['descrizione']] = "il voto inserito è sufficiente ma è presente un recupero di tipo <b>'" . estrai_tipo_recupero_singolo($voto_materia['tipo_recupero']) . "'</b>  con esito <b>" . $esito_recupero . "</b>";
                                $anomalie_presenti = true;
                            }
                        }
                    }

                    if ($materia['tipo_materia'] == 'CONDOTTA' && $giudizio_sospeso_6_in_condotta == 'NO'){
                        // verifico se ha voto 6
                        if ($voto_materia['voto_pagellina'] == 6) {
                            $esito_finale_studente = '';
                            switch ($dati_classe['anno_reale_classe']) {
                                case '1':
                                    $esito_finale_studente = $studente['esito_prima_superiore'];
                                    break;
                                case '2':
                                    $esito_finale_studente = $studente['esito_seconda_superiore'];
                                    break;
                                case '3':
                                    $esito_finale_studente = $studente['esito_terza_superiore'];
                                    break;
                                case '4':
                                    $esito_finale_studente = $studente['esito_quarta_superiore'];
                                    break;
                                case '5':
                                    $esito_finale_studente = $studente['esito_quinta_superiore'];
                                    break;
                            }

                            // voto 6 senza recupero e non giudizio sospeso
                            if ($voto_materia['esito_recupero'] == '' && $esito_finale_studente != '4') {
                                $anomalie_voti[$studente['id_studente']]['anomalie'][$materia['descrizione']][] = "il voto in condotta è pari a 6 e non ha un esito di recupero, ma non ha un giudizio sospeso";
                                $anomalie_presenti = true;
                            }
                        }
                    }
                }
                //}}} </editor-fold>
            }

            // Incongruenze tra insufficienze con e senza recupero
            if (count($recuperi_insufficienze['presente']) > 0) {
                $anomalie_voti[$studente['id_studente']]['anomalie']['RECUPERI'][] = "le materie " . implode(', ', $recuperi_insufficienze['presente']) . " risultano con recupero non ancora recuperato.";
                $anomalie_presenti = true;
            }
        }
        //}}} </editor-fold>

        $nome_pagella .= " della classe {$dati_classe['classe']} {$dati_classe['sezione']} {$dati_classe['codice']}";

        $mat_chiusura_scrutini = explode('@', $dati_classe['blocco_scrutini']);

        foreach ($mat_chiusura_scrutini as $singolo_periodo_chiusura) {
            $mat_singolo_periodo = explode('#', $singolo_periodo_chiusura);
            if ($mat_singolo_periodo[0] == $periodo) {
                $stato_chiusura_scrutini = $mat_singolo_periodo[1];
            }
        }

        $html .= "<div style='background-color:#8888ff'>";
        $html .= "<div id='div_main' class='div_" . $pref_classe . "tabellone_main'>\n";
        $html .= '<div class="div_' . $pref_classe . 'tabellone_header"
                        style="top: 0px;
                                left: 0px;
                                width:' . ($schermo - 150) . 'px;
                                height:' . ($row_height * 3) . 'px;
                                text-align: center;
                                font-size: ' . ($font_size + 2) . ';
                                font-weight: bold;"
                                "
							>' . $nome_pagella . '</div>' . "\n";

        /* {{{ Div presenti consiglio ed anomalie nei voti */
        $html .= '<div 	class="div_' . $pref_classe . 'tabellone_header"
                        style="top: 0px; left: 0px; width:350px; height:' . ($row_height * 3) . 'px;
                                text-align: center;
                                font-size: ' . ($font_size + 2) . ';
                                font-weight: bold;">' . "\n";
        if ($stato_chiusura_scrutini != 'SI' and $dati_classe['consiglio_classe_attivo'] == 'SI') {
            $html .= "<input type='button' value='Presenze consiglio' onclick='popup_presenze_consiglio(\"apri\");'>" . "\n";
        }
        if (in_array($dati_classe['consiglio_classe_attivo'], ['SI', 'POST'])) {
            if ($dati_classe['consiglio_classe_attivo'] == 'SI') {
                $script_mostra_anomalie = "document.forms[\"form_cambia_stato\"].nuovo_stato.value=\"APRI_CONSIGLIO\"; "
                        . "document.forms[\"form_cambia_stato\"].mostra_anomalie.value=\"SI\"; "
                        . "document.forms[\"form_cambia_stato\"].submit();";
            } else {
                $script_mostra_anomalie = "$(\"#overlay_anomalie\").show();";
            }
            if ($periodo == 9) {
                $html .= "<button onclick='" . $script_mostra_anomalie . "'>Visualizza incongruenze</button>" . "\n";
            }
        }

        $html .= '</div>' . "\n";
        /* }}}  */

        //$html_to_return .= '<div class="div_'.$pref_classe.'tabellone_header" style="top: 0px; right: 0px; width:150px;height:' . ($row_height * 2) . 'px;text-align: center;"><input type="button" value="Chiudi" onclick="document.getElementById(\'div_main\').style.visibility=\'hidden\';document.getElementById(\'popup_voti_pagelle\').style.visibility=\'hidden\';document.getElementById(\'popup_crediti\').style.visibility=\'hidden\';"></div>'."\n";
        $html .= '<div 	class="div_' . $pref_classe . 'tabellone_header"
                        style="top: 0px; right: 0px; width:250px; height:' . ($row_height * 3) . 'px;
                                text-align: center;
                                font-size: ' . ($font_size + 2) . ';
                                font-weight: bold;">' . "\n";

        if ($periodo == 9 and ( $coordinatore == 'SI' or $form_stato == 'amministratore') and time() > $data_inizio_calcolo_giudizi_sospesi) {
            $html .= '<input type="button" value="Filtra recuperi" onclick="display_righe_recupero();">' . "\n";
        }

        $html .= '<input type="button" value="Chiudi" onclick=" document.forms[\'form_esci\'].submit();"></div>' . "\n";

        if (($form_stato == 'amministratore' or $coordinatore == 'SI') and $funzione_cambia_stato_tabellone == 0) {
            /* {{{ Sezione cambio stato tabellone */

            if ($stato_chiusura_scrutini == 'SI') {
                $html_stato_scrutinio = 'Scrutinio chiuso e dati pubblicati solo sul sito famiglie --&gt;
													<input type="button"
														value="Riapri scrutinio"
														onclick="if(confirm(\'Procedere alla riapertura dello scrutino?\')){
																	document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'APRI_SCRUTINIO\';
																	document.forms[\'form_cambia_stato\'].submit();
																	}
																">';
            } else {
                switch ($dati_classe['consiglio_classe_attivo']) {
                    case 'NO':
                        $html_stato_scrutinio = 'Inserimento dati professori attivo --&gt;
													<input type="button"
														value="Apri consiglio di classe"
														onclick="if(confirm(\'Procedere alla apertura del consiglio di classe?\'))
																{
																	if(confirm(\'ATTENZIONE!!! Procedendo i professori non avranno piu la possibilita di inserire le proprie proposte di voto!\'))
																	{
																		document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'APRI_CONSIGLIO\';
																		document.forms[\'form_cambia_stato\'].submit();
																	}
																}
																"
														>';
                        break;
                    case 'SI':
                        $html_stato_scrutinio = '<input type="button"
														value="Riapri inserimento dati professori"
														onclick="if(confirm(\'Ritornare alla fase di inserimento proposte di voto?\')){
																	document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'APRI_SCRUTINIO\';
																	document.forms[\'form_cambia_stato\'].submit();
																	}
																">
													&lt;-- Consiglio di classe aperto --&gt;
													<input type="button"
														value="Chiudi consiglio di classe"';
                        $html_stato_scrutinio .= 'onclick="if(confirm(\'Procedere alla chiusura del consiglio di classe?\')){
																	document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'CHIUDI_CONSIGLIO\';
																	document.forms[\'form_cambia_stato\'].submit();
																	}
																">';
                        break;
                    case 'POST':
                        $html_stato_scrutinio = '<input type="button"
														value="Riapri consiglio di classe"
														onclick=" 	if(confirm(\'Procedere alla riapertura del consiglio di classe?\')){
																	document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'APRI_CONSIGLIO\';
																	document.forms[\'form_cambia_stato\'].submit();
																	}
																"
														>
													&lt;-- Consiglio di classe chiuso --&gt;
													<input type="button"
														value="Chiudi scrutinio e pubblica dati solo sul sito famiglie"
														onclick="if(confirm(\'Procedere alla chiusura dello scrutino e alla pubblicazione solo sul sito famiglie?\')){
																	if(confirm(\'ATTENZIONE!!!! Procedendo i dati delle pagelle verranno pubblicati solo sul sito famiglie.\'))
																	{
																		document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'CHIUDI_SCRUTINIO\';
																		document.forms[\'form_cambia_stato\'].submit();
																	}
																}"> ';
                        break;
                }
            }
            /* }}} */
        } else {
            if ($stato_chiusura_scrutini == 'SI') {
                $html_stato_scrutinio = 'Scrutinio chiuso e dati pubblicati solo sul sito famiglie';
            } else {
                switch ($dati_classe['consiglio_classe_attivo']) {
                    case 'NO':
                        $html_stato_scrutinio = 'Inserimento dati attivo';
                        break;
                    case 'SI':
                        $html_stato_scrutinio = 'Consiglio di classe aperto';
                        break;
                    case 'POST':
                        $html_stato_scrutinio = 'Consiglio di classe chiuso';
                        break;
                }
            }
        }

        $html .= '<div class="div_' . $pref_classe . 'tabellone_header"
                        style="	top: ' . ($row_height * 1) . 'px;
                                left: 0px;
                                width:' . ($schermo - 150) . 'px;
                                height:' . ($row_height * 2) . 'px;
                                text-align: center;
                                font-size: ' . ($font_size + 2) . ';
                                font-weight: bold;
                                margin-top: 9px;
                                margin-bottom: 30px;"
                                "
							>' . $html_stato_scrutinio . '</div>' . "\n";

        $row_start = $row_height * 3;

        if ($browser == 'ie') {
            $html .= '<div class="div_' . $pref_classe . 'tabellone_top"
                            style="	top:' . $row_start . 'px;
                                    left:0px;
                                    width:' . ($width_nomi) . 'px;
                                    height:' . ($row_height) . 'px"
								>&nbsp</div>' . "\n";
        } else {
            $html .= '<div class="div_' . $pref_classe . 'tabellone_top"
                                style="	top:' . $row_start . 'px;
                                        left:0px;
                                        width:' . ($width_nomi - 3) . 'px;
                                        height:' . ($row_height - 3) . 'px"
								>&nbsp</div>' . "\n";
        }

        $cont_col = 1;

        //{{{ <editor-fold defaultstate="collapsed" desc="blocco recuperi da metà a gosto a metà settembre"
        $data = time();
        $data_limite_agosto = mktime(0, 0, 0, 8, 15, date('Y', $data));
        $data_limite_settembre = mktime(23, 59, 59, 9, 15, date('Y', $data));
        if (($data >= $data_limite_agosto) && ($data <= $data_limite_settembre) && in_array($periodo, ['9', '29'])) {
            //$blocco_recuperi = 'attivo';
            $blocco_recuperi = 'disattivo';
        } else {
            $blocco_recuperi = 'disattivo';
        }
        //}}} </editor-fold>

        foreach ($mat_materie as $indice_materia => $materia) {
            //{{{ <editor-fold defaultstate="collapsed"> Intestazione di colonna
            $col_width_temp = $col_width * $materia['colonne'] - 1;
            if ($browser == 'ie') {
                $html .= '<div
										id="div_0_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_int_colonna"
										style="	cursor:pointer;
												top:' . ($row_start) . 'px;
												left: ' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
												width: ' . ($col_width_temp + 1) . 'px;
												height:' . ($row_height) . 'px;
												font-size: ' . $font_size . '"
										onMouseOver="hover_on_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onclick="	document.forms[\'form_edit\'].id_materia.value=\'' . $materia['id_materia'] . '\';
													document.forms[\'form_edit\'].torna_a.value=\'materia\';
													document.forms[\'form_edit\'].submit();"
									>' . "\n";
            } else {
                if ($form_stato == 'professore' && !in_array($current_user, $materia['professori']) && $coordinatore == 'NO' && $form_stato != 'amministratore') {
                    $html .= '<div
										id="div_0_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_int_colonna"
										style="	cursor:pointer;
												top:' . ($row_start) . 'px;
												left: ' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
												width: ' . $col_width_temp . 'px;
												height:' . ($row_height - 2) . 'px;
												font-size: ' . $font_size . '"
										onMouseOver="hover_on_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";
                } else {
                    $html .= '<div
										id="div_0_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_int_colonna"
										style="	cursor:pointer;
												top:' . ($row_start) . 'px;
												left: ' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
												width: ' . $col_width_temp . 'px;
												height:' . ($row_height - 2) . 'px;
												font-size: ' . $font_size . '"
										onMouseOver="hover_on_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(0,' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onclick="	document.forms[\'form_edit\'].id_materia.value=\'' . $materia['id_materia'] . '\';
													document.forms[\'form_edit\'].torna_a.value=\'materia\';
													document.forms[\'form_edit\'].submit();"
									>' . "\n";
                }
            }

            $larghezza_descrizione_materia_approx = strlen($materia['nome_materia_breve']) * ($font_size / 1.5);
            if ($larghezza_descrizione_materia_approx < $col_width_temp) {
                if ($materia['nome_materia_breve'] == '') {
                    $testo_descrizione = $materia['descrizione'];
                } else {
                    $testo_descrizione = $materia['nome_materia_breve'];
                }
            } else {
                if ($materia['nome_materia_breve'] == '') {
                    $testo_descrizione = substr($materia['descrizione'], 0, floor((1 * $col_width_temp / $font_size))) . '...';
                } else {
                    $testo_descrizione = substr($materia['nome_materia_breve'], 0, floor((1 * $col_width_temp / $font_size))) . '...';
                }
            }

            if (count($materia['mat_classi_orig']) == 0) {
                if ($materia['nome_materia_breve'] == '') {
                    $html .= '<a title="' . $materia['descrizione'] . '">' . $testo_descrizione . '</a>' . "\n";
                } else {
                    $html .= '<a title="' . $materia['nome_materia_breve'] . '">' . $testo_descrizione . '</a>' . "\n";
                }
            } else {
                //Materia proveniente da calcolo multiclasse
                if ($materia['nome_materia_breve'] == '') {
                    $html .= '<a title="' . $materia['descrizione'] . '">' . $testo_descrizione . '*</a>' . "\n";
                } else {
                    $html .= '<a title="' . $materia['nome_materia_breve'] . '">' . $testo_descrizione . '*</a>' . "\n";
                }
            }
            $html .= "<input type='hidden' id='tipo_voto_personalizzato_" . $cont_col . "' 	value='" . $materia['tipo_voto_personalizzato'] . "'>\n";
            $html .= "<input type='hidden' id='id_materia_" . $cont_col . "'                  value='" . $materia['id_materia'] . "'>\n";
            $html .= "<input type='hidden' id='codice_materia_" . $cont_col . "'              value='" . $materia['codice'] . "'>\n";
            if ($materia['nome_materia_breve'] == '') {
                $html .= "<input type='hidden' id='descrizione_materia_" . $cont_col . "'         value='" . $materia['descrizione'] . "'>\n";
            } else {
                $html .= "<input type='hidden' id='descrizione_materia_" . $cont_col . "'         value='" . $materia['nome_materia_breve'] . "'>\n";
            }

//            $col_scritto = in_array($materia['cpm_scritto'], ['0','1'] ) ? $materia['cpm_scritto'] : $materia['scritto'];
//            $col_orale = in_array($materia['cpm_orale'], ['0','1'] ) ? $materia['cpm_orale'] : $materia['orale'];
//            $col_pratico = in_array($materia['cpm_pratico'], ['0','1'] ) ? $materia['cpm_pratico'] : $materia['pratico'];
            // scritto
            if (in_array($materia['tipo_voto_personalizzato'], [1]) && $tipo_visualizzazione == 'personalizzato') {
                $col_scritto = 0;
            } else {
                $col_scritto = in_array($materia['cpm_scritto'], ['0', '1']) ? $materia['cpm_scritto'] : $materia['scritto'];
            }
            // orale
            if (in_array($materia['tipo_voto_personalizzato'], [1]) && $tipo_visualizzazione == 'personalizzato') {
                $col_orale = 0;
            } else {
                $col_orale = in_array($materia['cpm_orale'], ['0', '1']) ? $materia['cpm_orale'] : $materia['orale'];
            }

            //pratico
            if (
                    (in_array($materia['tipo_voto_personalizzato'], [1, 2]) && $tipo_visualizzazione == 'personalizzato') ||
                    (in_array($tipo_visualizzazione, ['voto_singolo', 'scritto_orale']))
            ) {
                $col_pratico = 0;
            } else {
                $col_pratico = in_array($materia['cpm_pratico'], ['0', '1']) ? $materia['cpm_pratico'] : $materia['pratico'];
            }

            $html .= "<input type='hidden' id='scritto_materia_" . $cont_col . "'             value='" . $col_scritto . "'>\n";
            $html .= "<input type='hidden' id='orale_materia_" . $cont_col . "'               value='" . $col_orale . "'>\n";
            $html .= "<input type='hidden' id='pratico_materia_" . $cont_col . "'             value='" . $col_pratico . "'>\n";
            $html .= "<input type='hidden' id='in_media_materia_" . $cont_col . "'            value='" . $materia['in_media_pagelle'] . "'>\n";

            if (is_array($materia['schema_voti'])) {
                $stringa_significati = '@@@Nessun voto';
                if($materia['tipo_materia'] == 'RELIGIONE'){
                    foreach ($materia['schema_voti'] as $significato_voto) {
                        $stringa_significati .= '###' . $significato_voto['voto'] . '@@@' . $significato_voto['valore_pagella'] . ' (' . $significato_voto['codice'] . ')' . '@@@' . $significato_voto['codice'];
                    }
                }else{
                    foreach ($materia['schema_voti'] as $significato_voto) {
                        $stringa_significati .= '###' . $significato_voto['voto'] . '@@@' . $significato_voto['valore_pagella'] . ' (' . $significato_voto['voto'] . ')' . '@@@' . $significato_voto['codice'];
                    }
                }
                $html .= "<input type='hidden' id='schema_voti_materia_" . $cont_col . "' 	value='" . $stringa_significati . "'>\n";
            }
            $html .= '</div>' . "\n";
            $cont_col++;
            //}}} </editor-fold>
        }

        // Determino se devo sottolineare i voti insufficienti senza recupero
        if ($dati_classe["tipo_indirizzo"] == "4" or $dati_classe["tipo_indirizzo"] == "6" or $dati_classe["tipo_indirizzo"] == "7" or $dati_classe["tipo_indirizzo"] == "8" or $trentino_abilitato == 'SI') {
            $visualizza_sottolineature_voti_senza_recupero = false;
        } else {
            $visualizza_sottolineature_voti_senza_recupero = true;
        }



        //$draw_head = false;
        $cont_row = 1;

        foreach ($mat_studenti as $studente) {
            //{{{ <editor-fold defaultstate="collapsed"> Intestazione di riga
            $cont_col = 1;
            $row_start += $row_height;

            $html_temp = '';
            //Intestazione di riga
            if (($coordinatore == 'SI' or $form_stato == 'amministratore') and $dati_classe['consiglio_classe_attivo'] == 'POST') {
                //{{{ <editor-fold defaultstate="collapsed">
                if ($browser == 'ie') {
                    $html_temp .= '<div
												class="div_' . $pref_classe . 'tabellone_int_riga_clear"
												style="	cursor:pointer;
														top:' . $row_start . 'px;
														left:0px;
														width:25px;
														height:' . ($row_height) . 'px;
														font-size: ' . $font_size . '"
												onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
										>' . "\n";
                    $html_temp .= '<input type=\'checkbox\' name=\'mat_stud[]\' value=\'' . $studente['id_studente'] . '\'> ';
                    $html_temp .= '</div>';
                    $html_temp .= '<div 	id="div_' . $cont_row . '_0"
												class="div_' . $pref_classe . 'tabellone_int_riga_clear"
												style="	cursor:pointer;
														top:' . $row_start . 'px;
														left:25px;
														width:' . ($width_nomi - 25) . 'px;
														height:' . ($row_height) . 'px;
														font-size: ' . $font_size . '"
												onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onclick="	document.forms[\'form_edit\'].id_studente.value=\'' . $studente['id_studente'] . '\';
															document.forms[\'form_edit\'].torna_a.value=\'studente\';
															document.forms[\'form_edit\'].submit();"
										>' . "\n";
                } else {
                    $html_temp .= '<div
												class="div_' . $pref_classe . 'tabellone_int_riga_clear"
												style="	cursor:pointer;
														top:' . $row_start . 'px;
														left:0px;
														width:22px;
														height:' . ($row_height - 1) . 'px;
														font-size: ' . $font_size . '"
												onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
										>' . "\n";
                    $html_temp .= '<input type=\'checkbox\' name=\'mat_stud[]\' value=\'' . $studente['id_studente'] . '\'> ';
                    $html_temp .= '</div>';
                    $html_temp .= '<div 	id="div_' . $cont_row . '_0"
												class="div_' . $pref_classe . 'tabellone_int_riga_clear"
												style="	cursor:pointer;
														top:' . $row_start . 'px;
														left:25px;
														width:' . ($width_nomi - 28) . 'px;
														height:' . ($row_height - 1) . 'px;
														font-size: ' . $font_size . '"
												onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
												onclick="	document.forms[\'form_edit\'].id_studente.value=\'' . $studente['id_studente'] . '\';
															document.forms[\'form_edit\'].torna_a.value=\'studente\';
															document.forms[\'form_edit\'].submit();"
										>' . "\n";
                }
                //}}} </editor-fold>
            } else {
                //{{{ <editor-fold defaultstate="collapsed">
                if ($browser == 'ie') {
                    $html_temp .= '<div id="div_' . $cont_row . '_0"
                                        class="div_' . $pref_classe . 'tabellone_int_riga_clear"
                                        style="	cursor:pointer;
                                                top:' . $row_start . 'px;
                                                left:0px;
                                                width:' . ($width_nomi) . 'px;
                                                height:' . ($row_height) . 'px;
                                                font-size: ' . $font_size . '"
                                        onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
                                        onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
                                        onclick="	document.forms[\'form_edit\'].id_studente.value=\'' . $studente['id_studente'] . '\';
                                                    document.forms[\'form_edit\'].torna_a.value=\'studente\';
                                                    document.forms[\'form_edit\'].submit();">' . "\n";
                } else {
                    $html_temp .= '<div id="div_' . $cont_row . '_0"
                                        class="div_' . $pref_classe . 'tabellone_int_riga_clear"
                                        style="	cursor:pointer;
                                                top:' . $row_start . 'px;
                                                left:0px;
                                                width:' . ($width_nomi - 3) . 'px;
                                                height:' . ($row_height - 1) . 'px;
                                                font-size: ' . $font_size . '"
                                        onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
                                        onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',0,\'' . $alto_contrasto . '\');"
                                        onclick="	document.forms[\'form_edit\'].id_studente.value=\'' . $studente['id_studente'] . '\';
                                                    document.forms[\'form_edit\'].torna_a.value=\'studente\';
                                                    document.forms[\'form_edit\'].submit();"
										>' . "\n";
                }
                //}}} </editor-fold>
            }

            if ($studente['registro'] < 10) {
                $html_temp .= "&nbsp;&nbsp;" . $studente['registro'] . ' ';
            } else {
                $html_temp .= $studente['registro'] . ' ';
            }

            $html_temp .= $studente['cognome'] . ' ' . $studente['nome'];

            if (stripos($studente['esito'], 'iscritto') === false and stripos($studente['esito'], 'in corso') === false and $studente['esito'] != '') {
                $html_temp .= " (" . $studente['esito'] . ")";
            }
            $html_temp .= "\n";

            $html_temp .= "<input type='hidden' id='id_studente_" . $cont_row . "' value='" . $studente['id_studente'] . "'>\n";
            $html_temp .= "<input type='hidden' id='cognome_studente_" . $cont_row . "'	value=\"" . $studente['cognome'] . "\">\n";
            $html_temp .= "<input type='hidden' id='nome_studente_" . $cont_row . "' value=\"" . $studente['nome'] . "\">\n";
            $html_temp .= "<input type='hidden' id='esonero_religione_studente_" . $cont_row . "' value='" . $studente['esonero_religione'] . "'>\n";
            $html_temp .= "<input type='hidden' id='pei_studente_" . $cont_row . "' value='" . $studente['pei'] . "'>\n";
            $html_temp .= "<input type='hidden' id='bes_studente_" . $cont_row . "' value='" . $studente['bes'] . "'>\n";
            $html_temp .= "<input type='hidden' id='oggetto_studente_" . $cont_row . "' value='" . $studente . "'>\n";

            if ($studente['tipo_studente'] == 'Privatista') {
                $html_temp .= "<input type='hidden' id='stato_privatista_" . $cont_row . "' value='SI'>\n";
            } else {
                $html_temp .= "<input type='hidden' id='stato_privatista_" . $cont_row . "' value='NO'>\n";
            }

            $html_temp .= '</div>' . "\n";
            //$col_start = $col_width * 4;
            $studente_recupero = 'NO';

            foreach ($mat_materie as $indice_materia => $materia) {
                //{{{ <editor-fold defaultstate="collapsed">
                $col_width_temp = $col_width * $materia['colonne'] - 1;

                if (is_numeric($materia['id_materia'])) {
                    //{{{ <editor-fold defaultstate="collapsed">
                    //Singola cella
                    if ($form_stato == 'amministratore'
                            or (
                            $coordinatore == 'SI'
                            and
                            $dati_classe['consiglio_classe_attivo'] == 'SI'
                            )
                            or (
                            in_array($current_user, $materia['professori'])
                            and
                            $dati_classe['consiglio_classe_attivo'] == 'NO'
                            )
                    ) {
                        $utente_modifica_cella = 'SI';
                    } else {
                        $utente_modifica_cella = 'NO';
                    }

                    if (count($materia['mat_classi_orig']) == 0) {
                        $studente_ha_classe = 'si';
                    } else {
                        $studente_ha_classe = 'no';
                        foreach ($studente['mat_classi'] as $classe_studente) {
                            foreach ($materia['mat_classi_orig'] as $id_classe_orig) {
                                if ($classe_studente['id_classe'] == $id_classe_orig) {
                                    $studente_ha_classe = 'si';
                                }
                            }
                            //definire se si vuole inserire un parametro per gestire questa cosa
                            $parametro_multi_classe = 'SI';
                            if ($parametro_multi_classe == 'SI') {
                                foreach ($materia['mat_classi_abb'] as $id_classe_orig) {
                                    if ($classe_studente['id_classe'] == $id_classe_orig) {
                                        $studente_ha_classe = 'si';
                                    }
                                }
                            }
                        }
                    }
//                    $parametro_multi_classe = 'SI';
//                    if ($parametro_multi_classe == 'SI'){// && $classe_studente['id_classe'] == $materia['id_classe']) {
//                        $studente_ha_classe = 'no';
//                         foreach ($studente['mat_classi'] as $classe_studente) {
//                            if ($classe_studente['id_classe'] == $materia['id_classe']) {
//                                $studente_ha_classe = 'si';
//                            }
//                        }
//                    }

                    $html_temp .= '<div
								id="div_' . $cont_row . '_' . $cont_col . '"
								class="div_' . $pref_classe . 'tabellone_cella_clear"';

                    if ($browser == 'ie') {
                        $html_temp .= '
								style="top:' . $row_start . 'px;
											left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
											width:' . ($col_width_temp + 1) . 'px;
											height:' . ($row_height) . 'px"';
                    } else {
                        $html_temp .= '
								style="top:' . $row_start . 'px;
											left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
											width:' . $col_width_temp . 'px;
											height:' . ($row_height - 1) . 'px"';
                    }

                    if (
                            (
                            $mat_materie[$indice_materia]['tipo_materia'] != 'RELIGIONE'
                            or (
                            $mat_materie[$indice_materia]['tipo_materia'] == 'RELIGIONE'
                            and
                            $studente['esonero_religione'] == 0
                            )
                            )
                            and
                            $studente_ha_classe == 'si'
                    ) {
                        $html_temp .= '	onclick="popup_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'apri\');"
						';
                    }
                    $html_temp .= '	onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
							>' . "\n";

                    if (
                            (
                            $mat_materie[$indice_materia]['tipo_materia'] == 'RELIGIONE'
                            and
                            $studente['esonero_religione'] == 1
                            )
                            or
                            $studente_ha_classe == 'no'
                    ) {
                        $html_temp .= '--';
                    } else {
                        if (($tipo_visualizzazione == 'voto_singolo') or ( ($tipo_visualizzazione == 'personalizzato') and ( $materia['tipo_voto_personalizzato'] == '1'))) {
                            if (
                                    $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] >= $parametri_voto['voto_minimo_suff']
                                    or
                                    $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == ''
                            ) {
                                $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_" . $cont_row . "_" . $cont_col . "' >";
                            } else {
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '' and $visualizza_sottolineature_voti_senza_recupero) {
                                    $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_" . $cont_row . "_" . $cont_col . "' style='text-decoration: underline;'>";
                                } else {
                                    $studente_recupero = 'SI';
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == 'SI') {
                                        $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_" . $cont_row . "_" . $cont_col . "' >";
                                    } elseif ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == '') {
                                        $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_" . $cont_row . "_" . $cont_col . "' >";
                                    } else {
                                        $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_" . $cont_row . "_" . $cont_col . "' >*";
                                    }
                                }
                            }
                            if ($mostra_OSDB == 'SI') {
                                if (strlen($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina']) > 1) {
                                    $output = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")";
                                } else {
                                    $output = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . " (" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")";
                                }
                            } else {
                                $output = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'];
                                if (
                                        $mat_materie[$indice_materia]['tipo_materia'] == 'RELIGIONE'
                                        and
                                        $studente['esonero_religione'] == 0
                                ) {
                                    foreach ($mat_voti[$studente['id_studente']][$materia['id_materia']]['significati_voto'] as $singolo_significato) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $singolo_significato['voto']) {
                                            $output = $singolo_significato['codice'];
                                        }
                                    }
                                }
                            }
                        } else {
                            $span_size = 0;

                            $stampa_scritto = false;
                            if ($tipo_visualizzazione == 'personalizzato') {
                                if (in_array($materia['tipo_voto_personalizzato'], [2, 3])) {
                                    $stampa_scritto = true;
                                }
                            } else {
                                if ($materia['cpm_scritto'] == '1' || (!isset($materia['cpm_scritto']) && $materia['scritto'] == '1')) {
                                    $stampa_scritto = true;
                                }
                            }

                            $stampa_orale = false;
                            if ($tipo_visualizzazione == 'personalizzato') {
                                if (in_array($materia['tipo_voto_personalizzato'], [2, 3])) {
                                    $stampa_orale = true;
                                }
                            } else {
                                if (
                                        (in_array($tipo_visualizzazione, ['scritto_orale', 'scritto_orale_pratico'])) &&
                                        ($materia['cpm_orale'] == '1' || (!isset($materia['cpm_orale']) && $materia['orale'] == '1'))) {
                                    $stampa_orale = true;
                                }
                            }

                            $stampa_pratico = false;
                            if ($tipo_visualizzazione == 'personalizzato') {
                                if ($materia['tipo_voto_personalizzato'] == 3) {
                                    $stampa_pratico = true;
                                }
                            } else {
                                if (
                                        (in_array($tipo_visualizzazione, ['scritto_orale_pratico'])) &&
                                        ($materia['cpm_pratico'] == '1' || (!isset($materia['cpm_pratico']) && $materia['pratico'] == '1'))) {
                                    $stampa_pratico = true;
                                }
                            }

                            $OSDB_posizione = "scritto";
                            if ($stampa_scritto) {
                                $OSDB_posizione = "scritto";
                            }
                            if ($stampa_orale) {
                                $OSDB_posizione = "orale";
                            }
                            if ($stampa_pratico) {
                                $OSDB_posizione = "pratico";
                            }

                            if ($stampa_scritto) {
                                //if (($materia['cpm_scritto'] == 1 || (!isset($materia['cpm_scritto']) && $materia['scritto'] == 1) && $tipo_visualizzazione != 'personalizzato') || ($tipo_visualizzazione == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'],[2,3]))) {
                                $html_temp .= '<span style="	position:absolute;
																	top:0px;
																	left:' . $span_size . 'px;
																	width:' . $col_width . 'px;
																	text-align:center;"
													>';
                                if (
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] >= $parametri_voto['voto_minimo_suff']
                                        or
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] == ''
                                ) {
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_scritto_" . $cont_row . "_" . $cont_col . "' >";
                                } else {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '' and $visualizza_sottolineature_voti_senza_recupero) {
                                        $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_scritto_" . $cont_row . "_" . $cont_col . "' style='text-decoration: underline;'>";
                                    } else {
                                        $studente_recupero = 'SI';
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == 'SI') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_scritto_" . $cont_row . "_" . $cont_col . "' >";
                                        } elseif ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == '') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_scritto_" . $cont_row . "_" . $cont_col . "' >";
                                        } else {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_scritto_" . $cont_row . "_" . $cont_col . "' >*";
                                        }
                                    }
                                }
                                if (($mostra_OSDB == 'SI') && ($OSDB_posizione == "scritto")) {
                                    if (strlen($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella']) > 1) {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    } else {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    }
                                } else {
                                    $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . "</a>\n";
                                }
                                $html_temp .= "</span>\n";
                                $span_size += $col_width;
                            }

                            if ($stampa_orale) {
                                //if (($materia['cpm_orale'] == 1 || (!isset($materia['cpm_orale']) && $materia['orale'] == 1) && $tipo_visualizzazione != 'personalizzato') || ($tipo_visualizzazione == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'],[2,3]))) {
                                $html_temp .= '<span style="	position:absolute;
																	top:0px;
																	left:' . $span_size . 'px;
																	width:' . $col_width . 'px;
																	text-align:center;"
													>';

                                if (
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] >= $parametri_voto['voto_minimo_suff']
                                        or
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] == ''
                                ) {
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_orale_" . $cont_row . "_" . $cont_col . "' >";
                                } else {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '' and $visualizza_sottolineature_voti_senza_recupero) {
                                        $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_orale_" . $cont_row . "_" . $cont_col . "' style='text-decoration: underline;'>";
                                    } else {
                                        $studente_recupero = 'SI';
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == 'SI') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_orale_" . $cont_row . "_" . $cont_col . "' >";
                                        } elseif ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == '') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_orale_" . $cont_row . "_" . $cont_col . "' >";
                                        } else {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_orale_" . $cont_row . "_" . $cont_col . "' >*";
                                        }
                                    }
                                }
                                if (($mostra_OSDB == 'SI') && ($OSDB_posizione == "orale")) {
                                    if (strlen($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella']) > 1) {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    } else {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    }
                                } else {
                                    $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . "</a>\n";
                                }

                                $html_temp .= "</span>\n";
                                $span_size += $col_width;
                            }

                            if ($stampa_pratico) {
                                //if (($materia['cpm_pratico'] == '1' || (!isset($materia['cpm_pratico']) && $materia['pratico'] == '1') && $tipo_visualizzazione != 'personalizzato') || ($tipo_visualizzazione == 'personalizzato' && in_array($materia['tipo_voto_personalizzato'],[3]))) {
                                $html_temp .= '<span style="position:absolute;
																top:0px;
																left:' . $span_size . 'px;
																width:' . $col_width . 'px;
																text-align:center;"
                                                    >';
                                if (
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] >= $parametri_voto['voto_minimo_suff']
                                        or
                                        $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] == ''
                                ) {
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_pratico_" . $cont_row . "_" . $cont_col . "' >";
                                } else {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '' and $visualizza_sottolineature_voti_senza_recupero) {
                                        $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_pratico_" . $cont_row . "_" . $cont_col . "' style='text-decoration: underline;'>";
                                    } else {
                                        $studente_recupero = 'SI';
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == 'SI') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_pratico_" . $cont_row . "_" . $cont_col . "' >";
                                        } elseif ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] == '') {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_pratico_" . $cont_row . "_" . $cont_col . "' >";
                                        } else {
                                            $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_pratico_" . $cont_row . "_" . $cont_col . "' >*";
                                        }
                                    }
                                }

                                if (($mostra_OSDB == 'SI') && ($OSDB_posizione == "pratico")) {
                                    if (strlen($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella']) > 1) {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    } else {
                                        $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . "(" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['codice'] . ")" . "</a>\n";
                                    }
                                } else {
                                    $html_temp .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . "</a>\n";
                                }

                                $html_temp .= "</span>\n";
                                $span_size += $col_width;
                            }
                        }

                        if (!(strlen(trim($output)) > 0)) {
                            $output = '&nbsp;';
                        }

                        $html_temp .= $output;
                        $html_temp .= "</a>\n";
                        $output = '&nbsp;';
                    }

                    $html_temp .= "<input type='hidden' id='assenze_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='monteore_totale_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] . "'>\n";

                    $html_temp .= "<input type='hidden' id='id_pagellina_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['id_pagellina'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='id_voto_pagellina_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['id_voto_pagellina'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='tipo_recupero_" . $cont_row . "_" . $cont_col . "' 	value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='esito_recupero_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero'] . "'>\n";

                    //sezione dedicata al campo applicazione di OSDB
                    if ($mostra_OSDB == 'SI') {
                        $html_temp .= "<input type='hidden' id='OSDB_applicazione_" . $cont_row . "_" . $cont_col . "' value='" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['id_valore_precomp'] . "'>\n";
                        $html_temp .= "<input type='hidden' id='id_valore_campo_libero_OSDB_applicazione_" . $cont_row . "_" . $cont_col . "' value='" . $mat_valori_applicazione_OSDB[$studente['id_studente']][$materia['id_materia']]['id_valore_campo_libero'] . "'>\n";
                    }

                    $dato_voto_unico_stampa = $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'];
                    if ($mat_materie[$indice_materia]['tipo_materia'] == 'RELIGIONE'
                        and
                        $studente['esonero_religione'] == 0){
                        foreach ($mat_voti[$studente['id_studente']][$materia['id_materia']]['significati_voto'] as $singolo_significato) {
                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $singolo_significato['voto']) {
                                $dato_voto_unico_stampa = $singolo_significato['codice'];
                                break;
                            }
                        }
                    }

                    $flag_voto_condotta = "";
                    if ($materia['tipo_materia'] == 'CONDOTTA') {
                        $flag_voto_condotta = " data-voto-condotta-" . $cont_row . " ";
                    }
                    $html_temp .= "<input type='hidden' id='voto_unico_" . $cont_row . "_" . $cont_col . "' value='" . $dato_voto_unico_stampa . "' " . $flag_voto_condotta . ">\n";

                    $html_temp .= "<input type='hidden' id='voto_scritto_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='voto_orale_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='voto_pratico_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . "'>\n";

                    $dato_voto_unico_proposta_stampa = $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'];
                    if ($mat_materie[$indice_materia]['tipo_materia'] == 'RELIGIONE'
                        and
                        $studente['esonero_religione'] == 0){
                        foreach ($mat_voti[$studente['id_studente']][$materia['id_materia']]['significati_voto'] as $singolo_significato) {
                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] == $singolo_significato['voto']) {
                                $dato_voto_unico_proposta_stampa = $singolo_significato['codice'];
                                break;
                            }
                        }
                    }
                    $html_temp .= "<input type='hidden' id='voto_unico_" . $cont_row . "_" . $cont_col . "' value='" . $dato_voto_unico_stampa . "'>\n";

                    $html_temp .= "<input type='hidden' id='proposta_voto_unico_" . $cont_row . "_" . $cont_col . "' value='" . $dato_voto_unico_proposta_stampa . "'>\n";
                    $html_temp .= "<input type='hidden' id='proposta_voto_scritto_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='proposta_voto_orale_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='proposta_voto_pratico_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] . "'>\n";

                    $html_temp .= "<input type='hidden' id='giudizio_analitico_" . $cont_row . "_" . $cont_col . "' value=\"" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['giudizio_analitico'] . "\">\n";

                    $html_temp .= "<input type='hidden' id='modificato_da_amministratore_" . $cont_row . "_" . $cont_col . "' 		value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['modificato_da_amministratore'] . "'>\n";
                    $html_temp .= "<input type='hidden' id='modificabile_da_utente_" . $cont_row . "_" . $cont_col . "' value='" . $utente_modifica_cella . "'>\n";

                    $html_temp .= "<input type='hidden' id='debito_" . $cont_row . "_" . $cont_col . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['debito'] . "'>\n";

                    // Se la materia coincide con la materia da cui prelevo i campi liberi per la compilazione del giudizio di ammissione di quinta, valorizzo il relativo parametro

                    if ($materia['id_materia'] == $parametro_id_materia_per_compilazione_giudizio_ammissione) {
                        $html_temp .= "<input type='hidden' id='id_voto_pagellina_giudizio_" . $cont_row . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['id_voto_pagellina'] . "'>\n";
                    }

                    //}}} </editor-fold>
                } else {
                    //{{{ <editor-fold defaultstate="collapsed">
                    switch ($materia['id_materia']) {
                        case 'crediti':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
											id="div_' . $cont_row . '_' . $cont_col . '"
											class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_crediti(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_crediti_" . $cont_row . "'>";

                            //a causa dell'adeguamento della funziona calcola_anno_reale_classe(), usata in estrai_classe(), non è più necessario cil caso particolare per le accademie d'arte
                            switch ($dati_classe['anno_reale_classe']) {
                                case '3':
                                    $html_temp .= ($studente['crediti_terza']);
                                    break;
                                case '4':
                                    $html_temp .= ($studente['crediti_quarta']);
                                    break;
                                case '5':
                                    $html_temp .= ($studente['crediti_quinta']);
                                    break;
                            }
                            $html_temp .= "</a>\n";
                            $html_temp .= "<input type='hidden' id='crediti_terza_" . $cont_row . "' 	 value='" . $studente['crediti_terza'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_terza_" . $cont_row . "'  value='" . $studente['crediti_reintegrati_terza'] . "'>\n";

                            $html_temp .= "<input type='hidden' id='crediti_quarta_" . $cont_row . "' 	 value='" . $studente['crediti_quarta'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_quarta_" . $cont_row . "' value='" . $studente['crediti_reintegrati_quarta'] . "'>\n";

                            $html_temp .= "<input type='hidden' id='crediti_quinta_" . $cont_row . "' 	 value='" . $studente['crediti_quinta'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_quinta_" . $cont_row . "' value='" . $studente['crediti_finali_agg'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_terza_" . $cont_row . "' value='" . encode($studente['motivi_crediti_terza']) . "'>\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_quarta_" . $cont_row . "' value='" . encode($studente['motivi_crediti_quarta']) . "'>\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_quinta_" . $cont_row . "' value='" . encode($studente['motivi_crediti_quinta']) . "'>\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_agg_" . $cont_row . "' value='" . encode($studente['motivi_crediti_agg']) . "'>\n";
                            //}}} </editor-fold>
                            break;
                        case 'crediti_qual':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
											id="div_' . $cont_row . '_' . $cont_col . '"
											class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_crediti_qual(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_crediti_" . $cont_row . "'>";
                            switch ($dati_classe['anno_reale_classe']) {
                                case '3':
                                    $html_temp .= ($studente['crediti_terza']);
                                    break;
                                case '4':
                                    $html_temp .= ($studente['crediti_quarta']);
                                    break;
                                case '5':
                                    $html_temp .= ($studente['crediti_quinta']);
                                    break;
                            }
                            $html_temp .= "</a>\n";
                            $html_temp .= "<input type='hidden' id='voto_crediti_qual_" . $cont_row . "' 	value='" . round(floatval($studente['voto_qualifica'] / 10), 1) . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_terza_" . $cont_row . "' 	value='" . $studente['crediti_terza'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_terza_" . $cont_row . "' 	value='" . $studente['crediti_reintegrati_terza'] . "'>\n";

                            $html_temp .= "<input type='hidden' id='crediti_quarta_" . $cont_row . "' 	value='" . $studente['crediti_quarta'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_quarta_" . $cont_row . "'	value='" . $studente['crediti_reintegrati_quarta'] . "'>\n";

                            $html_temp .= "<input type='hidden' id='crediti_quinta_" . $cont_row . "' 	value='" . $studente['crediti_quinta'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='crediti_r_quinta_" . $cont_row . "' 	value='" . $studente['crediti_finali_agg'] . "'>\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_terza_" . $cont_row . "' value=\"" . utf8_decode($studente['motivi_crediti_terza']) . "\">\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_quarta_" . $cont_row . "' value=\"" . utf8_decode($studente['motivi_crediti_quarta']) . "\">\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_quinta_" . $cont_row . "' value=\"" . utf8_decode($studente['motivi_crediti_quinta']) . "\">\n";
                            $html_temp .= "<input type='hidden' id='motivi_crediti_agg_" . $cont_row . "' value=\"" . utf8_decode($studente['motivi_crediti_agg']) . "\">\n";
                            //}}} </editor-fold>
                            break;
                        case 'ammissione':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_ammissione_quinta(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            $html_temp .= "<a class=" . $pref_classe . "normal' id='label_ammissione_quinta_" . $cont_row . "'>";
                            $html_temp .= $studente['ammesso_esame_quinta'];
                            $html_temp .= "</a>\n";


                            $html_temp .= "<input type='hidden' id='giudizio_ammissione_quinta_" . $cont_row . "' 	value=\"" . $studente['giudizio_ammissione_quinta'] . "\">\n";
                            //}}} </editor-fold>
                            break;
                        case 'ammissione_qualifica':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_ammissione_qualifica(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            //{{{ <editor-fold defaultstate="collapsed">
                            $tabella_qualifica = '<table border = "1">';
                            $testa_qualifica = '';
                            $corpo_qualifica = '';
                            foreach ($elenco_risultati_ammissione['dati_colonne_ammissione_media'] as $gruppo) {
                                $testa_qualifica .= '<td>' . $gruppo['intestazione'] . '</td>';
                                $corpo_qualifica .= '<td>' . $gruppo['valori'][$studente['id_studente']]['media'] . '</td>';
                            }
                            foreach ($elenco_risultati_ammissione['dati_colonne_ammissione_somma'] as $gruppo) {
                                $testa_qualifica .= '<td>' . $gruppo['intestazione'] . '</td>';
                                $corpo_qualifica .= '<td>' . $gruppo['valori'][$studente['id_studente']]['somma'] . '</td>';
                            }
                            $testa_qualifica .= '<td>Voto ammissione calcolato</td>';
                            $corpo_qualifica .= '<td>' . $elenco_risultati_ammissione['elenco_studenti'][$studente['id_studente']]['voto_ammissione_calcolato'] . '</td>';

                            $tabella_qualifica .= '<tr>' . $testa_qualifica . '</tr>';
                            $tabella_qualifica .= '<tr>' . $corpo_qualifica . '</tr>';
                            $tabella_qualifica .= '</table>';
                            //}}} </editor-fold>

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_ammissione_qualifica_" . $cont_row . "'>" . $studente['ammesso_esame_qualifica'] . "</a>\n";
                            $html_temp .= "<input type='hidden' id='colonne_ammissione_qualifica_" . $cont_row . "' value='" . $tabella_qualifica . "'>\n";

                            if ($studente['voto_ammissione'] > 0) {
                                $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_ammissione_qualifica_" . $cont_row . "'>" . $studente['voto_ammissione'] . "</a>)\n";
                            } else {
                                $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_ammissione_qualifica_" . $cont_row . "'>" . $elenco_risultati_ammissione['elenco_studenti'][$studente['id_studente']]['voto_ammissione_calcolato'] . "</a>)\n";
                            }

                            $html_temp .= "<input type='hidden' id='giudizio_ammissione_qualifica_" . $cont_row . "' value=\"" . $studente['giudizio_ammissione_terza'] . "\">\n";
                            //}}} </editor-fold>
                            break;
                        case 'qualifica':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            if ($studente['ammesso_esame_qualifica'] == 'SI') {
                                $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_voto_qualifica_" . $cont_row . "'>" . $studente['voto_qualifica'] . "</a>\n";
                            }
                            $html_temp .= "<input type='hidden' id='voto_qualifica_" . $cont_row . "' value=\"" . $studente['voto_qualifica'] . "\">\n";
                            //}}} </editor-fold>
                            break;
                        case 'ammissione_medie':
                            //{{{ <editor-fold defaultstate="collapsed">
                            //manca dati_classe
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
											onclick="popup_ammissione_medie(' . $cont_row . ',\'apri\');"
											onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
											onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_ammissione_medie_" . $cont_row . "'>";
                            if ($dati_classe["tipo_indirizzo"] == "4") {
                                if (intval($dati_classe["classe"]) == 3) {
                                    $html_temp .= $studente["esito_terza_media"];
                                    $html_temp .= "</a>";

                                    if ($studente['voto_ammissione_medie'] > 0) {
                                        $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_ammissione_medie_" . $cont_row . "'>" . $studente['voto_ammissione_medie'] . "</a>)\n";
                                    } else {
                                        $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_ammissione_medie_" . $cont_row . "'></a>)\n";
                                    }
                                } elseif (intval($dati_classe["classe"]) == 2) {
                                    $html_temp .= $studente["esito_seconda_media"];
                                } elseif (intval($dati_classe["classe"]) == 1) {
                                    $html_temp .= $studente["esito_prima_media"];
                                } else {
                                    $html_temp .= ' ';
                                }
                            }
                            $html_temp .= "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'voto_finale_medie':
                            //{{{ <editor-fold defaultstate="collapsed">
                            //manca dati_classe
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
											onclick="popup_voto_finale_medie(' . $cont_row . ',\'apri\');"
											onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
											onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='voto_finale_medie_" . $cont_row . "'>";
                            if ($dati_classe["tipo_indirizzo"] == "4") {
                                if (intval($dati_classe["classe"]) == 3) {
                                    $html_temp .= $studente["giudizio_sintetico_esame_terza_media"];
                                    $html_temp .= "</a>";

//                                    if ($studente['giudizio_sintetico_esame_terza_media'] > 0) {
//                                        $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_finale_medie_" . $cont_row . "'>" . $studente['giudizio_sintetico_esame_terza_media'] . "</a>)\n";
//                                    } else {
//                                        $html_temp .= " - (<a class='link_" . $pref_classe . "normal' id='voto_finale_medie_" . $cont_row . "'></a>)\n";
//                                    }
                                } else {
                                    $html_temp .= ' ';
                                }
                            }
                            $html_temp .= "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'ammissione_elementari':
                            //{{{ <editor-fold defaultstate="collapsed">
                            //manca dati_classe
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
											onclick="popup_ammissione_elementari(' . $cont_row . ',\'apri\');"
											onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
											onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_ammissione_elementari_" . $cont_row . "'>";
                            if ($dati_classe["tipo_indirizzo"] == "6") {
                                if (intval($dati_classe["classe"]) == 5) {
                                    $html_temp .= $studente["esito_quinta_elementare"];
                                } elseif (intval($dati_classe["classe"]) == 4) {
                                    $html_temp .= $studente["esito_quarta_elementare"];
                                } elseif (intval($dati_classe["classe"]) == 3) {
                                    $html_temp .= $studente["esito_terza_elementare"];
                                } elseif (intval($dati_classe["classe"]) == 2) {
                                    $html_temp .= $studente["esito_seconda_elementare"];
                                } elseif (intval($dati_classe["classe"]) == 1) {
                                    $html_temp .= $studente["esito_prima_elementare"];
                                } else {
                                    $html_temp .= ' ';
                                }
                            }
                            $html_temp .= "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'ammissione_maestro':
                            //{{{ <editor-fold defaultstate="collapsed">
                            //manca dati_classe
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
											onclick="popup_ammissione_maestro(' . $cont_row . ',\'apri\');"
											onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
											onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										>' . "\n";

                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_ammissione_maestro_" . $cont_row . "'>";
                            $html_temp .= $studente["ammesso_esame_qualifica"];
                            $html_temp .= "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'media_voti':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';

                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            $somma_valori = 0;
                            $cont_media = 0;

                            foreach ($mat_materie as $materia_per_media) {
                                if ($materia_per_media['in_media_pagelle'] == 'SI' and intval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']) > 0) {
                                    if ($trentino_abilitato == 'SI' &&
                                            verifica_presenza_debito($studente['id_studente'], $materia_per_media['id_materia']) &&
                                            floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']) < $parametri_voto['voto_minimo_suff']
                                    ) {
                                        $tmp_voto = $parametri_voto['voto_minimo_suff'];
                                    } else {
                                        $tmp_voto = floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['voto_pagellina']);
                                    }
                                    $somma_valori += $tmp_voto;
                                    $cont_media++;
                                }
                            }

                            if ($cont_media > 0) {
                                $risultato_media = round($somma_valori / $cont_media, 2);
                            } else {
                                $risultato_media = '';
                            }
                            if ($risultato_media >= $parametri_voto['voto_minimo_suff']) {
                                $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_media_voti_" . $cont_row . "'>";
                            } else {
                                $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_media_voti_" . $cont_row . "'>";
                            }

                            $html_temp .= $risultato_media . "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'perc_assenze':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';

                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";

                            $somma_valori = 0;
                            $cont_media = 0;
                            $somma_assenze = 0;
                            $somma_monteore = 0;

                            foreach ($mat_materie as $materia_per_media) {
                                if (intval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['monteore_totale']) > 0) {
                                    $somma_monteore += floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['monteore_totale']);
                                    $somma_assenze += floatval($mat_voti[$studente['id_studente']][$materia_per_media['id_materia']]['ore_assenza']);
                                    $cont_media++;
                                }
                            }

                            if ($cont_media > 0) {
                                // round 4 perche' con il *100 due decimali spariscono e in questo modo ne rimangono 2
                                $risultato_media = round($somma_assenze / $somma_monteore, 4) * 100 . ' %';
                            } else {
                                $risultato_media = '';
                            }
                            if (intval($risultato_media) < 25) {
                                $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_perc_assenze_" . $cont_row . "'>";
                            } else {
                                $html_temp .= "<a class='link_" . $pref_classe . "rosso' id='label_perc_assenze_" . $cont_row . "'>";
                            }

                            $html_temp .= $risultato_media . "</a>";
                            //}}} </editor-fold>
                            break;
                        case 'licenza':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
										id="div_' . $cont_row . '_' . $cont_col . '"
										class="div_' . $pref_classe . 'tabellone_cella_clear"';

                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_licenza_maestro(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";
                            $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_licenza_maestro_" . $cont_row . "'>";
                            $html_temp .= $studente['stato_licenza_maestro'];
                            $html_temp .= "</a>";
                            //}}} </editor-fold>
                            break;

                        case 'esito_finale':
                            //{{{ <editor-fold defaultstate="collapsed">
                            $html_temp .= '<div
											id="div_' . $cont_row . '_' . $cont_col . '"
											class="div_' . $pref_classe . 'tabellone_cella_clear"';
                            if ($browser == 'ie') {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . ($col_width_temp + 1) . 'px;
													height:' . ($row_height) . 'px
												"';
                            } else {
                                $html_temp .= '
										style=	"
													top:' . $row_start . 'px;
													left:' . ($col_width * $materia['colonna'] + $width_nomi) . 'px;
													width:' . $col_width_temp . 'px;
													height:' . ($row_height - 1) . 'px
												"';
                            }
                            $html_temp .= '
										onclick="popup_esito_finale(' . $cont_row . ',\'apri\');"
										onMouseOver="hover_on_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
										onMouseOut="hover_out_cella_tabellone(' . $cont_row . ',' . $cont_col . ',\'' . $alto_contrasto . '\');"
									>' . "\n";


                            //a causa dell'adeguamento della funziona calcola_anno_reale_classe(), usata in estrai_classe(), non è più necessario cil caso particolare per le accademie d'arte
                            switch ($dati_classe['anno_reale_classe']) {
                                case '1':
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_esito_finale_" . $cont_row . "'>";
                                    $html_temp .= ($elenco_esiti[$studente['esito_prima_superiore']]['descrizione']);
                                    $html_temp .= "</a>\n";
                                    $html_temp .= "<input type='hidden' id='value_esito_finale_" . $cont_row . "' value='" . $studente['esito_prima_superiore'] . "'>\n";
                                    break;
                                case '2':
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_esito_finale_" . $cont_row . "'>";
                                    $html_temp .= ($elenco_esiti[$studente['esito_seconda_superiore']]['descrizione']);
                                    $html_temp .= "</a>\n";
                                    $html_temp .= "<input type='hidden' id='value_esito_finale_" . $cont_row . "' value='" . $studente['esito_seconda_superiore'] . "'>\n";
                                    break;
                                case '3':
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_esito_finale_" . $cont_row . "'>";
                                    $html_temp .= ($elenco_esiti[$studente['esito_terza_superiore']]['descrizione']);
                                    $html_temp .= "</a>\n";
                                    $html_temp .= "<input type='hidden' id='value_esito_finale_" . $cont_row . "' value='" . $studente['esito_terza_superiore'] . "'>\n";
                                    break;
                                case '4':
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_esito_finale_" . $cont_row . "'>";
                                    $html_temp .= ($elenco_esiti[$studente['esito_quarta_superiore']]['descrizione']);
                                    $html_temp .= "</a>\n";
                                    $html_temp .= "<input type='hidden' id='value_esito_finale_" . $cont_row . "' value='" . $studente['esito_quarta_superiore'] . "'>\n";
                                    break;
                                case '5':
                                    $html_temp .= "<a class='link_" . $pref_classe . "normal' id='label_esito_finale_" . $cont_row . "'>";
                                    $html_temp .= ($elenco_esiti[$studente['esito_quinta_superiore']]['descrizione']);
                                    $html_temp .= "</a>\n";
                                    $html_temp .= "<input type='hidden' id='value_esito_finale_" . $cont_row . "' value='" . $studente['esito_quinta_superiore'] . "'>\n";
                                    break;
                            }

                            //}}} </editor-fold>
                            break;

                        default:

                            break;
                    }
                    //}}} </editor-fold>
                }

                //$html_temp .= '<script type="text/javascript">calcola_media_tabellone_pagella(' . $cont_row . ');</script>';
                $html_temp .= '</div>' . "\n";
                //$col_start += $col_width_temp;
                $cont_col++;
                //}}} </editor-fold>
            }
            //$draw_head = true;

            if ($periodo == 9 and ( $coordinatore == 'SI' or $form_stato == 'amministratore') and time() > $data_inizio_calcolo_giudizi_sospesi and ( $default_mostra_solo_giudizi_sospesi == 'SI' or $visualizzazione_recuperi_tabellone == 'NO') and $studente_recupero == 'NO') {
                $html .= "<div id='container_" . $cont_row . "' style='display: none;'>" . "\n" . $html_temp;
            } else {
                $html .= "<div id='container_" . $cont_row . "'>" . "\n" . $html_temp;
            }

            $html .= "<input type='hidden' id='recupero_studente_" . $cont_row . "' value='" . $studente_recupero . "'>\n";
            $html .= '</div>' . "\n";

            $cont_row++;
            //}}} </editor-fold>
        }

        if ($coordinatore == 'SI' or $form_stato == 'amministratore' or $verbalizzatore == 'SI_VISUALIZZA' or $verbalizzatore == 'SI_MODIFICA') {
            //{{{ <editor-fold defaultstate="collapsed">
            $html .= '<div
										class="div_' . $pref_classe . 'tabellone_header"
										style="	top: ' . ($row_start + $row_height + 5) . 'px;
												left: 0px;
												right:0px;
                                                padding-left: 5px;
												height:' . ($row_height * 9) . 'px;
												text-align: center;
												font-size: ' . ($font_size + 2) . ';
												font-weight: bold;"
												"
								>' . "\n";
            $html .= "<table>" . "\n";

            /* {{{ Moduli e Verbali */
            $html .= "<tr><td><b>Verbali</b></td><td><b>Moduli</b></td></tr><tr>" . "\n";
            //VERBALI
            if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                $html .= "<td><select style='width:490px;' name='' size='5'\">" . "\n";
            } else {
                $html .= "<td><select style='width:490px;' name='' size='5' disabled>" . "\n";
            }

            foreach ($mat_verbali as $verbale) {
                $html .= "<option value='" . $verbale['valore'] . "'>" . $verbale['nome'] . "</option>" . "\n";
            }
            $html .= "</select>" . "\n";

            if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                $html .= "<br>";
                $html .= "<button type='button' onclick=\"";
                $html .= "var myform = document.forms['form_print'];
                                for(z=0;z<myform.length;z++)
                                {
                                    if(myform[z].name == 'mat_studenti[]')
                                    {
                                        myform.removeChild(myform[z]);
                                        z--;
                                    }
                                }
                                document.forms['form_print'].form_id_modello_documento.value=$($(this).parent().find('select')[0]).val();
                                document.forms['form_print'].form_azione.value='genera_documento';
                                document.forms['form_print'].submit();";
                $html .= "\">Apri</button>";
            }

            $html .= "</td>" . "\n";

            // //MODULI
            if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                $html .= "<td><select style='width:490px;'  name='' size='5'\">" . "\n";
            } else {
                $html .= "<td><select style='width:490px;'  name='' size='5' disabled>" . "\n";
            }

            foreach ($mat_moduli as $modulo) {
                $html .= "<option value='" . $modulo['valore'] . "'>" . $modulo['nome'] . "</option>" . "\n";
            }
            $html .= "</select>" . "\n";

            if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                $html .= "<br>";
                $html .= "<button type='button' onclick=\"";
                $html .= "var myform = document.forms['form_print'];
										for(z=0;z<myform.length;z++)
										{
											if(myform[z].name == 'mat_studenti[]')
											{
												myform.removeChild(myform[z]);
												z--;
											}
										}
										var mat_studenti = document.getElementsByName('mat_stud[]');
										for(i=0;i<(mat_studenti.length);i++)
										{
											if (mat_studenti[i].checked)
											{
												var input = document.createElement('input');
												input.setAttribute('type', 'hidden');
												input.setAttribute('name', 'mat_studenti[]');
												input.setAttribute('value', mat_studenti[i].value);
												document.forms['form_print'].appendChild(input);
											}
										}
                                document.forms['form_print'].form_id_modello_documento.value=$($(this).parent().find('select')[0]).val();
                                document.forms['form_print'].form_azione.value='genera_documento';
                                document.forms['form_print'].submit();";
                $html .= "\">Apri</button>";
            }

            $html .= "</td>" . "\n";
            $html .= "</tr>" . "\n";

            if ($verbalizzatore == 'SI_VISUALIZZA' or $verbalizzatore == 'SI_MODIFICA' or $form_stato == 'amministratore') {
                $html .= "<tr><td><b>Verbali Storici</b></td><td><b>Moduli Storici</b></td></tr><tr>" . "\n";
                //VERBALI STORICI
                if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                    $html .= "<td><select style='width:490px;'  name='' size='5'\">" . "\n";
                } else {
                    $html .= "<td><select style='width:490px;'  name='' size='5' disabled>" . "\n";
                }

                foreach ($mat_verbali_storici as $verbale) {
                    $html .= "<option value='" . $verbale['valore'] . "'>" . $verbale['nome'] . "</option>" . "\n";
                }
                $html .= "</select>" . "\n";

                if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                    $html .= "<br>";
                    $html .= "<button type='button' onclick=\"";
                    $html .= "var myform = document.forms['form_print'];
                                    for(z=0;z<myform.length;z++)
                                    {
                                        if(myform[z].name == 'mat_studenti[]')
                                        {
                                            myform.removeChild(myform[z]);
                                            z--;
                                        }
                                    }";
                    $html .= "document.forms['form_print'].form_id_storico_documento.value=$($(this).parent().find('select')[0]).val();";

                    if ($verbalizzatore == 'SI_MODIFICA') {
                        $html .= "document.forms['form_print'].form_azione.value='carica_storico_documento';
                                  document.forms['form_print'].form_bypass_current_user.value='NO';";
                    } else {
                        $html .= "document.forms['form_print'].form_azione.value='download_storico_documento';
                                  document.forms['form_print'].form_bypass_current_user.value='SI';";
                    }

                    $html .= "document.forms['form_print'].submit();";
                    $html .= "\">Apri</button>";
                }

                $html .= "</td>" . "\n";

                //MODULI STORICI
                if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                    $html .= "<td><select style='width:490px;'  name='' size='5'\">" . "\n";
                } else {
                    $html .= "<td><select style='width:490px;'  name='' size='5' disabled>" . "\n";
                }

                foreach ($mat_moduli_storici as $modulo) {
                    $html .= "<option value='" . $modulo['valore'] . "'>" . $modulo['nome'] . "</option>" . "\n";
                }
                $html .= "</select>" . "\n";

                if ($dati_classe['consiglio_classe_attivo'] == 'POST') {
                    $html .= "<br>";
                    $html .= "<button type='button' onclick=\"";
                    $html .= "var myform = document.forms['form_print'];
                                    for(z=0;z<myform.length;z++)
                                    {
                                        if(myform[z].name == 'mat_studenti[]')
                                        {
                                            myform.removeChild(myform[z]);
                                            z--;
                                        }
                                    }
                                    var mat_studenti = document.getElementsByName('mat_stud[]');
                                    for(i=0;i<(mat_studenti.length);i++)
                                    {
                                        if (mat_studenti[i].checked)
                                        {
                                            var input = document.createElement('input');
                                            input.setAttribute('type', 'hidden');
                                            input.setAttribute('name', 'mat_studenti[]');
                                            input.setAttribute('value', mat_studenti[i].value);
                                            document.forms['form_print'].appendChild(input);
                                        }
                                    }";
                    $html .= "document.forms['form_print'].form_id_storico_documento.value=$($(this).parent().find('select')[0]).val();";

                    if ($verbalizzatore == 'SI_MODIFICA') {
                        $html .= "document.forms['form_print'].form_azione.value='carica_storico_documento';
                                  document.forms['form_print'].form_bypass_current_user.value='NO';";
                    } else {
                        $html .= "document.forms['form_print'].form_azione.value='download_storico_documento';
                                  document.forms['form_print'].form_bypass_current_user.value='SI';";
                    }

                    $html .= "document.forms['form_print'].submit();";
                    $html .= "\">Apri</button>";
                }

                $html .= "</td>" . "\n";
            }
            $html .= "</tr>" . "\n";
            // /*}}}*/
            // /*}}}*/

            $html .= "</table>" . "\n";

            //{{{ <editor-fold defaultstate="collapsed" desc="Tabella anomalie voti-recuperi">
            if (in_array($dati_classe['consiglio_classe_attivo'], ['SI', 'POST'])) {
                if ($dati_classe['consiglio_classe_attivo'] == 'POST' && $anomalie_presenti || ($dati_classe['consiglio_classe_attivo'] == 'SI' && $mostra_anomalie == 'SI')) {
                    $visualizza_overlay = "";
                    $visualizza_riapertura = $dati_classe['consiglio_classe_attivo'] == 'POST' ? "" : "display: none;";
                } else {
                    $visualizza_overlay = "display: none;";
                }

                $html .= "<br>" . "\n";



                if ($periodo == '9') {
                    $html .= "<div id='overlay_anomalie' "
                            . "style='position: fixed; top: 0px; left: 0px; bottom: 0px; right: 0px; z-index: 10000; "
                            . "background-color: rgba(0, 0, 0, 0.8); align: center; overflow:scroll; height: 100%; {$visualizza_overlay}'"
                            . "><br><br>" . "\n";

                    $html .= "<center><table class='tab_" . $pref_classe . "table allarmi_pagelle_table'>" . "\n";
                    $html .= "<tr>"
                            . "<td  width=180px  height=30px class='tab_" . $pref_classe . "header allarmi_pagelle_header'>"
                            . '<input type="button" style="' . $visualizza_riapertura . '"
														value="Riapri consiglio di classe"
														onclick=" 	if(confirm(\'Procedere alla riapertura del consiglio di classe?\')){
																	document.forms[\'form_cambia_stato\'].nuovo_stato.value=\'APRI_CONSIGLIO\';
																	document.forms[\'form_cambia_stato\'].submit();
																	}
																"
														></td>'
                            . "<td align='center' height=30px width=80% class='tab_" . $pref_classe . "header allarmi_pagelle_header'>CONTROLLO INCONGRUENZE"
                            . "<td align='right' width=180px class='allarmi_pagelle_header'><button onclick='$(\"#overlay_anomalie\").hide();'>X</button></td>"
                            . "</tr>" . "\n";
                    if ($anomalie_presenti) {

                        $html .= "<tr><td colspan=3 align=center>Di seguito sono elencati i dati che necessitano una verifica in quanto potrebbero non essere congruenti<br></td></tr>" . "\n";

                        foreach ($anomalie_voti as $anomalie_studente) {
                            $numero_anomalie = count($anomalie_studente) - 1;
                            if ($numero_anomalie > 0) {
                                $html .= "<tr class='tab_" . $pref_classe . "header'>";
                                $html .= "<td colspan=3 rowspan={$numero_anomalie} class='padding_cella_generica' style='padding-left: 15px; border-bottom: 1px solid black;'>";
                                $html .= "<b>" . $anomalie_studente['studente'] . "</td></tr>" . "\n";

                                $html .= "<tr>";
                                $html .= "<td colspan=3 class='padding_cella_generica allarmi_pagelle_riga_anomalia'><ul>\n";
                                foreach ($anomalie_studente['anomalie'] as $key => $anomalie_materia) {
                                    $html .= "<b>" . $key . "</b>\n";
                                    foreach ($anomalie_materia as $anomalia) {
                                        $html .= "<ul>- " . $anomalia . "</ul>\n";
                                    }
                                }
                                $html .= "</ul><br></td></tr>";
                            }
                        }
                    } else {
                        $html .= "<tr><td colspan=3 align=center height=60px><br>Nessuna incongruenza presente nei dati<br></td></tr>" . "\n";
                    }
                }
                $html .= "<tr><td colspan=4></td></tr>" . "\n";
                $html .= "</table>" . "\n";
                $html .= "</div>" . "\n";
                $html .= "<br>" . "\n";
            }

            //}}} </editor-fold>

            $html .= "</div>" . "\n";
            //}}} </editor-fold>
        }
        $html .= "</div>" . "\n";

        //{{{ <editor-fold defaultstate="collapsed" desc="popup voti">
        $html .= '<div class="popup_tabellone" id="popup_voti_pagelle">\n';
        $html .= "<div>\n";
        $html .= '<table id="tabella_popup_voti_pagelle" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_voti_pagelle\' class=\'td_header_popup\'>Dettaglio voto studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente"></a></td><td>Nome</td><td><a id="popup_nome_studente"></a></td></tr>';
        $html .= '<tr><td>Materia</td><td colspan="3"><a id="popup_materia"></a></td></tr>';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<tr><td>Monteore complessivo</td><td colspan="3">
											<input type="hidden" id="popup_monteore_totale">
											<input type="text" id="popup_monteore_totale_ore" size="3"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_monteore_totale\').value
													=
													(parseInt(document.getElementById(\'popup_monteore_totale_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_monteore_totale_min\').value));"
											> ore
											<input type="text" id="popup_monteore_totale_min" size="2"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_monteore_totale\').value
													=
													(parseInt(document.getElementById(\'popup_monteore_totale_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_monteore_totale_min\').value));"
												> min
										</td></tr>';
            $html .= '<tr><td>Ore assenza</td><td colspan="3">
											<input type="hidden" id="popup_ore_assenza">
											<input type="text" id="popup_ore_assenza_ore" size="3"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_ore_assenza\').value
													=
													(parseInt(document.getElementById(\'popup_ore_assenza_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_ore_assenza_min\').value));"
											> ore
											<input type="text" id="popup_ore_assenza_min" size="2"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_ore_assenza\').value
													=
													(parseInt(document.getElementById(\'popup_ore_assenza_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_ore_assenza_min\').value));"

											> min
										</td></tr>';
            $html .= '<tr><td>Tipo recupero</td><td colspan="3">';
            $html .= '<select id="popup_tipo_recupero">';
        } else {
            $html .= '<tr><td>Monteore complessivo</td><td colspan="3">
											<input type="hidden" id="popup_monteore_totale">
											<input readonly type="text" id="popup_monteore_totale_ore" size="3"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_monteore_totale\').value
													=
													(parseInt(document.getElementById(\'popup_monteore_totale_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_monteore_totale_min\').value));"
											> ore
											<input readonly type="text" id="popup_monteore_totale_min" size="2"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_monteore_totale\').value
													=
													(parseInt(document.getElementById(\'popup_monteore_totale_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_monteore_totale_min\').value));"

											> min
										</td></tr>';

            $html .= '<tr><td>Ore assenza</td><td colspan="3">
											<input type="hidden" id="popup_ore_assenza">
											<input readonly type="text" id="popup_ore_assenza_ore" size="3"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													document.getElementById(\'popup_ore_assenza\').value
													=
													(parseInt(document.getElementById(\'popup_ore_assenza_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_ore_assenza_min\').value));"
											> ore
											<input readonly type="text" id="popup_ore_assenza_min" size="2"
												onkeyup="jm_integermask(this);"
												onmouseup="jm_integermask(this);"
												onchange="
													this.document.getElementById(\'popup_ore_assenza\').value
													=
													(parseInt(document.getElementById(\'popup_ore_assenza_ore\').value) * 60)
													+
													(parseInt(document.getElementById(\'popup_ore_assenza_min\').value));"

											> min
										</td></tr>';

            $html .= '<tr><td>Tipo recupero</td><td colspan="3">';
            $html .= '<select disabled id="popup_tipo_recupero">';
        }
        $html .= '<option value="">Nessun recupero necessario</option>';
        foreach ($tipi_recupero as $tipo_recupero) {
            $html .= '<option value="' . $tipo_recupero['valore'] . '">' . $tipo_recupero['nome'] . '</option>';
        }
        $html .= '</select>';
        $html .= '</td></tr>';
        $html .= '<tr><td>Esito recupero</td><td colspan="3">';
        $html .= '<select id="popup_esito_recupero">';
        $html .= '<option value="">Nessun esito definito/da definire</option>';
        $html .= '<option value="SI">Positivo</option>';
        $html .= '<option value="NO">Negativo</option>';
        $html .= '<option value="ASSENTE">Assente</option>';
        $html .= '<option value="NI">Parziale</option>';
        $html .= '</select>';
        $html .= '</td></tr>';
        if ($dati_classe['anno_reale_classe'] == 3 and $dati_classe['tipo_indirizzo'] == '2') {
            $html .= '<tr><td>Giudizio analitico</td><td colspan="3">';
            $html .= '<textarea id="popup_giudizio_analitico" rows=8 cols=50 ></textarea>';
            $html .= '</td></tr>';
        }

        $html .= '<tr>';
        $html .= '<td colspan="1">';

        //if($stato_chiusura_scrutini != 'SI')
        //{
        $html .= '<input id="salva_popup_voti_pagelle" type="button" onclick="" value=\'Salva\'>';
        //}
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_voti_pagelle\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';
        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup crediti">
        $html .= "<div class='popup_tabellone' id='popup_crediti'>\n"
                . "<div>\n"
                . '<table id="tabella_popup_crediti" class=\'td_normal_popup\'>'
                . '<tr><td colspan="4" id=\'drag_div_crediti\' class=\'td_header_popup\'>Dettaglio crediti studente</td></tr>'
                . '<tr><td>Cognome</td><td><a id="popup_cognome_studente_crediti"></a></td><td>Nome</td><td><a id="popup_nome_studente_crediti"></a></td></tr>'
                . '<tr><td>Media</td><td><a id="popup_media_studente_crediti"></a></td><td>Fascia</td><td><a id="popup_fascia_crediti"></a></td></tr>'
                . '<tr><td>Crediti assegnati</td><td colspan="3">'
                . '<select id="popup_crediti_assegnati">'
                . '</select>'
                . '<tr><td>Motivazione crediti</td><td colspan="3">'
                . '<textarea id="popup_motivazione_crediti_assegnati" rows=3 cols=50 ></textarea>'
                . '</td></tr>';
        if (is_array($motivazioni_crediti)) {
            $html .= '<tr><td colspan="2"><select id="popup_select_motivazione_crediti_assegnati">'
                    . '<option value="0" selected></option>';
            foreach ($motivazioni_crediti as $motivazione) {
                $html .= '<option value="' . $motivazione['id_motivazione'] . '">' . $motivazione['descrizione'] . '</option>';
            }
            $html .= '</select></td>
						<td><input type="button" value="+" onclick="
							document.getElementById(\'popup_motivazione_crediti_assegnati\').value = document.getElementById(\'popup_motivazione_crediti_assegnati\').value + document.getElementById(\'popup_select_motivazione_crediti_assegnati\').options[document.getElementById(\'popup_select_motivazione_crediti_assegnati\').selectedIndex].text + \'\n\' ;
							"></td>
						<td><input type="button" value="R" onclick="
							document.getElementById(\'popup_motivazione_crediti_assegnati\').value = document.getElementById(\'popup_select_motivazione_crediti_assegnati\').options[document.getElementById(\'popup_select_motivazione_crediti_assegnati\').selectedIndex].text;
							"></td>
					</tr>';
        }
        $str_crediti_conv_om_2022 = '';
        if ($dati_classe['anno_reale_classe'] == 5 and $dati_classe['tipo_indirizzo'] != '6') {
            //$str_crediti_conv_om_2022 ='<tr><td>Totale crediti convertiti</td><td colspan="3"><a id="popup_totale_crediti_om_2022"></a></td></tr>';
        }

        $html .= '<tr><td>Totale crediti</td><td colspan="3"><a id="popup_totale_crediti"></a></td></tr>'
                //.$str_crediti_conv_om_2022
                . '<tr><td colspan=\'4\'>Motivazioni crediti anni precedenti</td></tr><tr><td colspan="4"><a id="popup_storia_crediti"></a></td></tr>'
                . '<tr>'
                . '<td colspan="1">';

//        $html .= '<tr><td>Totale crediti</td><td colspan="3"><a id="popup_totale_crediti"></a></td></tr>'
//                . '<tr><td colspan=\'4\'>Motivazioni crediti anni precedenti</td></tr><tr><td colspan="4"><a id="popup_storia_crediti"></a></td></tr>'
//                . '<tr>'
//                . '<td colspan="1">';

        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_crediti" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>'
                . '<td colspan="2"></td>'
                . '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_crediti\').style.visibility=\'hidden\'" value=\'Annulla\'></td>'
                . '</tr>'
                . '</table>'
                . "</div>\n"
                . "<div class='popup_tabellone_shadow'>&nbsp;\n"
                . "</div>\n"
                . "</div>\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup crediti in caso di terza professionale">
        $html .= "<div class='popup_tabellone' id='popup_crediti_qual'>\n";
        $html .= "<div>\n";
        $html .= '<table id="tabella_popup_crediti_qual" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_crediti_qual\' class=\'td_header_popup\'>Dettaglio crediti studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_crediti_qual"></a></td><td>Nome</td><td><a id="popup_nome_studente_crediti_qual"></a></td></tr>';
        $html .= '<tr><td>Voto qualifica/10</td><td><a id="popup_media_studente_crediti_qual"></a></td><td>Fascia</td><td><a id="popup_fascia_crediti_qual"></a></td></tr>';
        $html .= '<tr><td>Crediti assegnati</td><td colspan="3">';
        $html .= '<select id="popup_crediti_assegnati_qual">';
        $html .= '</select>';
        $html .= '<tr><td>Motivazione crediti</td><td colspan="3">';
        $html .= '<textarea id="popup_motivazione_crediti_assegnati_qual" rows=3 cols=50 ></textarea>';
        $html .= '</td></tr>';

        $html .= '<tr><td>Totale crediti</td><td colspan="3"><a id="popup_totale_crediti_qual"></a></td></tr>';
        $html .= '<tr><td>Storia crediti</td><td colspan="3"><a id="popup_storia_crediti_qual"></a></td></tr>';
        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_crediti_qual" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_crediti_qual\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup ammissione quinta">
        $html .= "<div class='popup_tabellone' id='popup_ammissione_quinta'>\n";
        $html .= "<div>\n";
        $html .= '<table id="tabella_popup_ammissione_quinta" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_ammissione_quinta\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_ammissione_quinta"></a></td><td>Nome</td><td><a id="popup_nome_studente_ammissione_quinta"></a></td></tr>';
        $html .= '<tr><td>Ammesso</td><td colspan="3">';
        $html .= '<select id="popup_select_ammissione_quinta">';
        $html .= '<option value="SI">Ammesso</option>';
        $html .= '<option value="NO">Non ammesso</option>';
        $html .= '</select>';
        $html .= '<tr><td>Giudizio ammissione /<br>non ammissione';
        if (intval($parametro_id_materia_per_compilazione_giudizio_ammissione) > 0) {
            $html .= '<br><input id="compila_popup_ammissione_quinta" type="button" onclick="" value=\'Compila\'>';
        }
        $html .= '</td><td colspan="3"><textarea id="popup_giudizio_ammissione_quinta" rows="8" cols="50"></textarea></td></tr>';
        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_ammissione_quinta" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_ammissione_quinta\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup ammissione medie">
        $html .= "<div class='popup_tabellone' id='popup_ammissione_medie'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_ammissione_medie" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_ammissione_medie\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_ammissione_medie"></a></td><td>Nome</td><td><a id="popup_nome_studente_ammissione_medie"></a></td></tr>';
        if ($dati_classe["tipo_indirizzo"] == "4") {
            if (intval($dati_classe["classe"]) == 3) {
                $html .= '<tr><td>Voto Ammissione</td><td colspan="3">';
                $html .= '<select id="popup_voto_ammissione_medie">';
                for ($cont = 10; $cont >= 0; $cont--) {
                    $html .= '<option value="' . $cont . '">' . str_pad($cont, 2, '0', STR_PAD_LEFT) . '</option>';
                }
                $html .= '<option value="">---</option>';
                $html .= '</select>';
                $html .= '</td></tr>';
            }
        }

        $html .= '<tr><td>Ammesso</td><td colspan="3">';
        $html .= '<select id="popup_select_ammissione_medie">';
        $html .= '<option value="SI">Ammesso</option>';
        $html .= '<option value="NO">Non ammesso</option>';
        $html .= '</select>';
        $html .= '</td></tr>';

        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_ammissione_medie" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_ammissione_medie\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        ////{{{ <editor-fold defaultstate="collapsed" desc="popup voto finale medie">
        $html .= "<div class='popup_tabellone' id='popup_voto_finale_medie'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_voto_finale_medie" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_voto_finale_medie\' class=\'td_header_popup\'>Voto esame</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_voto_finale_medie"></a></td><td>Nome</td><td><a id="popup_nome_studente_voto_finale_medie"></a></td></tr>';
        if ($dati_classe["tipo_indirizzo"] == "4") {
            if (intval($dati_classe["classe"]) == 3) {
                $html .= '<tr><td>Voto finale</td><td colspan="3">';
                $html .= '<select id="popup_voto_finale_medie_esame">';
                $html .= '<option value="10elode">10 e lode</option>';
                for ($cont = 10; $cont >= 0; $cont--) {
                    $html .= '<option value="' . $cont . '">' . $cont . '</option>';
                }
                $html .= '<option value="">---</option>';
                $html .= '</select>';
                $html .= '</td></tr>';
            }
        }


        $html .= '<tr>';
        $html .= '<td colspan="1">';
        $html .= '<input id="salva_popup_voto_finale_medie" type="button" onclick="" value=\'Salva\'>';
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_voto_finale_medie\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup ammissione elementari">
        $html .= "<div class='popup_tabellone' id='popup_ammissione_elementari'>\n";
        $html .= "<div>\n";
        $html .= '<table id="tabella_popup_ammissione_elementari" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_ammissione_elementari\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_ammissione_elementari"></a></td><td>Nome</td><td><a id="popup_nome_studente_ammissione_elementari"></a></td></tr>';
        if ($dati_classe["tipo_indirizzo"] == "4") {
            if (intval($dati_classe["classe"]) == 3) {
                $html .= '<tr><td>Voto Ammissione</td><td colspan="3">';
                $html .= '<select id="popup_voto_ammissione_elementari">';
                for ($cont = 10; $cont >= 0; $cont--) {
                    $html .= '<option value="' . $cont . '">' . str_pad($cont, 2, '0', STR_PAD_LEFT) . '</option>';
                }
                $html .= '<option value="">---</option>';
                $html .= '</select>';
                $html .= '</td></tr>';
            }
        }

        $html .= '<tr><td>Ammesso</td><td colspan="3">';
        $html .= '<select id="popup_select_ammissione_elementari">';
        $html .= '<option value="SI">Ammesso</option>';
        $html .= '<option value="NO">Non ammesso</option>';
        $html .= '</select>';
        $html .= '</td></tr>';

        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_ammissione_elementari" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_ammissione_elementari\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup ammissione qualifica">
        $html .= "<div class='popup_tabellone' id='popup_ammissione_qualifica'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_ammissione_qualifica" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_ammissione_qualifica\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_ammissione_qualifica"></a></td><td>Nome</td><td><a id="popup_nome_studente_ammissione_qualifica"></a></td></tr>';
        $html .= '<tr><td colspan="4" id="popup_colonne_ammissione_qualifica">';
        $html .= '</td></tr>';

        $html .= '<tr><td>Voto Ammissione</td><td colspan="3">';
        $html .= '<select id="popup_voto_ammissione_qualifica">';
        for ($cont = 100; $cont >= 0; $cont--) {
            $html .= '<option value="' . $cont . '">' . str_pad($cont, 3, '0', STR_PAD_LEFT) . '</option>';
        }
        $html .= '<option value="">---</option>';
        $html .= '</select>';
        $html .= '</td></tr>';

        $html .= '<tr><td>Ammesso</td><td colspan="3">';
        $html .= '<select id="popup_select_ammissione_qualifica">';
        $html .= '<option value="SI">Ammesso</option>';
        $html .= '<option value="NO">Non ammesso</option>';
        $html .= '</select>';
        $html .= '</td></tr>';
        $html .= '<tr><td>Giudizio ammissione</td><td colspan="3">';
        $html .= '<textarea id="popup_giudizio_ammissione_qualifica" rows=3 cols=50 ></textarea>';
        $html .= '</td></tr>';
        $html .= '<tr>';

        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_ammissione_qualifica" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_ammissione_qualifica\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup ammissione maestro">
        $html .= "<div class='popup_tabellone' id='popup_ammissione_maestro'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_ammissione_maestro" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_ammissione_maestro\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_ammissione_maestro"></a></td><td>Nome</td><td><a id="popup_nome_studente_ammissione_maestro"></a></td></tr>';
        $html .= '<tr><td>Ammesso</td><td colspan="3">';
        $html .= '<select id="popup_select_ammissione_maestro">';
        $html .= '<option value="SI">Ammesso</option>';
        $html .= '<option value="NO">Non ammesso</option>';
        $html .= '</select>';
        $html .= '</td></tr>';
        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_ammissione_maestro" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_ammissione_maestro\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup licenza maestro">
        $html .= "<div class='popup_tabellone' id='popup_licenza_maestro'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_licenza_maestro" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_licenza_maestro\' class=\'td_header_popup\'>Licenza studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_licenza_maestro"></a></td><td>Nome</td><td><a id="popup_nome_studente_licenza_maestro"></a></td></tr>';
        $html .= '<tr><td>Licenziato</td><td colspan="3">';
        $html .= '<select id="popup_select_licenza_maestro">';
        $html .= '<option value="SI">Licenziato</option>';
        $html .= '<option value="NO">Non licenziato</option>';
        $html .= '</select>';
        $html .= '</td></tr>';
        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_licenza_maestro" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_licenza_maestro\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup esito finale">
        $html .= "<div class='popup_tabellone' id='popup_esito_finale'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_esito_finale" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_esito_finale\' class=\'td_header_popup\'>Ammissione studente</td></tr>';
        $html .= '<tr><td>Cognome</td><td><a id="popup_cognome_studente_esito_finale"></a></td><td>Nome</td><td><a id="popup_nome_studente_esito_finale"></a></td></tr>';
        $html .= '<tr><td>Esito</td><td colspan="3">';
        $html .= '<select id="popup_select_esito_finale">';
        $html .= '<option value="0"></option>';

        foreach ($elenco_esiti as $id_esito => $esito) {
            $html .= '<option value="' . $id_esito . '">' . $esito['descrizione'] . '</option>';
        }
        $html .= '</select>';
        $html .= '</td></tr>';
        $html .= '<tr>';
        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI') {
            $html .= '<input id="salva_popup_esito_finale" type="button" onclick="" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_esito_finale\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>
        //{{{ <editor-fold defaultstate="collapsed" desc="popup presenze professori al consiglio">
        $html .= "<div class='popup_tabellone' id='popup_presenze_consiglio'>\n";
        $html .= "<div>\n";
        $html .= '<table  id="tabella_popup_presenze_consiglio" class=\'td_normal_popup\'>';
        $html .= '<tr><td colspan="4" id=\'drag_div_presenze_consiglio\' class=\'td_header_popup\'>Presenze Consiglio</td></tr>';

        foreach ($mat_professori as $id_professore => $professore) {
            $html .= '<tr>';
            $html .= '<td colspan="2">' . $professore['nome'] . '</td>';
            $html .= '<td colspan="2"><select class="select_presenza_consiglio" name="presenza_consiglio_prof_' . $id_professore . '">';

            if ($professore['presente'] == 0) {
                $html .= '<option selected value="presente">Presente</option>';
                $html .= '<option value="assente">Assente</option>';
            } else if ($professore['presente'] == -1) {
                $html .= '<option value="presente">Presente</option>';
                $html .= '<option selected value="assente">Assente</option>';
            } else {
                $html .= '<option value="presente">Presente</option>';
                $html .= '<option value="assente">Assente</option>';
            }

            foreach ($elenco_professori as $professore_elenco) {
                if ($professore['presente'] == $professore_elenco['valore']) {
                    $html .= '<option selected value="' . $professore_elenco['valore'] . '">Sost. da ' . $professore_elenco['nome'] . '</option>';
                } else {
                    $html .= '<option value="' . $professore_elenco['valore'] . '">Sost. da ' . $professore_elenco['nome'] . '</option>';
                }
            }
            $html .= '</select></td>';
            $html .= '</tr>';
        }

        $html .= '<td colspan="1">';
        if ($stato_chiusura_scrutini != 'SI' and $dati_classe['consiglio_classe_attivo'] == 'SI') {
            $html .= '<input id="salva_popup_presenze_consiglio" type="button" onclick="popup_presenze_consiglio(\'salva\');" value=\'Salva\'>';
        }
        $html .= '</td>';
        $html .= '<td colspan="2"></td>';
        $html .= '<td colspan="1"><input type="button" onclick="document.getElementById(\'popup_presenze_consiglio\').style.visibility=\'hidden\'" value=\'Annulla\'></td>';
        $html .= '</tr>';

        $html .= '</table>';
        $html .= "</div>" . "\n";
        $html .= "<div class='popup_tabellone_shadow'>&nbsp;\n";
        $html .= "</div>" . "\n";
        $html .= "</div>" . "\n";
        //}}} </editor-fold>

        $html .= "</div>";

        $html .= '<script src="javascript/dom-drag.js" type="text/javascript"></script>';
        $html .= "<script type=\"text/javascript\" language=\"javascript\">
								var handle = document.getElementById('drag_div_voti_pagelle');
								var div_window = document.getElementById('popup_voti_pagelle');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_voti_pagelle');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_voti_pagelle');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
								var handle = document.getElementById('drag_div_crediti');
								var div_window = document.getElementById('popup_crediti');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_crediti');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_crediti');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
								var handle = document.getElementById('drag_div_crediti_qual');
								var div_window = document.getElementById('popup_crediti_qual');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_crediti_qual');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_crediti_qual');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
								var handle = document.getElementById('drag_div_ammissione_quinta');
								var div_window = document.getElementById('popup_ammissione_quinta');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_ammissione_quinta');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_ammissione_quinta');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
								var handle = document.getElementById('drag_div_ammissione_medie');
								var div_window = document.getElementById('popup_ammissione_medie');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_ammissione_medie');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_ammissione_medie');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
                                var handle = document.getElementById('drag_div_voto_finale_medie');
								var div_window = document.getElementById('popup_voto_finale_medie');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_voto_finale_medie');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_voto_finale_medie');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
								var handle = document.getElementById('drag_div_ammissione_elementari');
								var div_window = document.getElementById('popup_ammissione_elementari');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_ammissione_elementari');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_ammissione_elementari');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}

								var handle = document.getElementById('drag_div_ammissione_qualifica');
								var div_window = document.getElementById('popup_ammissione_qualifica');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_ammissione_qualifica');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_ammissione_qualifica');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}


								var handle = document.getElementById('drag_div_ammissione_maestro');
								var div_window = document.getElementById('popup_ammissione_maestro');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_ammissione_maestro');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_ammissione_maestro');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}

								var handle = document.getElementById('drag_div_licenza_maestro');
								var div_window = document.getElementById('popup_licenza_maestro');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_licenza_maestro');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_licenza_maestro');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}

								var handle = document.getElementById('drag_div_presenze_consiglio');
								var div_window = document.getElementById('popup_presenze_consiglio');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_presenze_consiglio');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_presenze_consiglio');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}

								var handle = document.getElementById('drag_div_esito_finale');
								var div_window = document.getElementById('popup_esito_finale');
								Drag.init(handle, div_window);
								div_window.onDragStart = function()
													{
														var div_main = document.getElementById('popup_esito_finale');
														div_main.style.filter = 'alpha(opacity=80)';
														div_main.style.opacity = '0.8';
													}
								div_window.onDragEnd = function()
													{
														var div_main = document.getElementById('popup_esito_finale');
														div_main.style.filter = 'alpha(opacity=100)';
														div_main.style.opacity = '1';
													}
							</script>";

        $html .= "<input type='hidden' id='tipo_visualizzazione' value='" . $tipo_visualizzazione . "'>\n";
        $html .= "<input type='hidden' id='voto_minimo_suff' value='" . $parametri_voto['voto_minimo_suff'] . "'>\n";
        $html .= "<input type='hidden' id='aggiustamento_media' value='" . $parametri_voto['aggiustamento_media'] . "'>\n";
        $html .= "<input type='hidden' id='consiglio_classe_attivo'	value='" . $dati_classe['consiglio_classe_attivo'] . "'>\n";
        $html .= "<input type='hidden' id='stato_chiusura_scrutini'	value='" . $stato_chiusura_scrutini . "'>\n";
        $html .= '<input type="hidden" id="current_user" value="' . $current_user . '">';
        $html .= '<input type="hidden" id="current_key" value="' . $current_key . '">';
        $html .= '<input type="hidden" id="popup_stato" value="' . $form_stato . '">';
        $html .= '<input type="hidden" id="periodo" value="' . $periodo . '">';
        $html .= '<input type="hidden" id="id_classe" value="' . $dati_classe['id_classe'] . '">';
        $html .= '<input type="hidden" id="anno_reale_classe" value="' . $dati_classe['anno_reale_classe'] . '">';
        $html .= '<input type="hidden" id="tipo_indirizzo" value="' . $dati_classe['tipo_indirizzo'] . '">';
        $html .= '<input type="hidden" id="coordinatore" value="' . $coordinatore . '">';
        $html .= '<input type="hidden" id="db_key" value="' . $db_key . '">';
        $html .= '<input type="hidden" id="mostra_OSDB" value="' . $mostra_OSDB . '">';
        $html .= '<input type="hidden" id="anno_scolastico_attuale" value="' . $db_key . '">';
        $html .= "<input type='hidden' id='trentino_abilitato" . $cont_row . "' value='" . $trentino_abilitato . "'>\n";
        $html .= "<input type='hidden' id='visualizza_sottolineature_voti_senza_recupero' value='" . $visualizza_sottolineature_voti_senza_recupero . "'>\n";
        $html .= '<input type="hidden" id="popup_id_voto_pagellina">';
        $html .= '<input type="hidden" id="popup_id_pagellina">';
        $html .= '<input type="hidden" id="popup_id_materia">';
        $html .= '<input type="hidden" id="drag_active">';
        $html .= '<input type="hidden" id="blocco_recuperi" value="' . $blocco_recuperi . '">';
    }

    return $html;
}
