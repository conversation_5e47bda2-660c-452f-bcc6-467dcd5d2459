<?php

/*
 * Smarty plugin
 * -------------------------------------------------------------
 *
 * -------------------------------------------------------------
 */

function smarty_function_mastercom_grid_detail_subject_report($params, $content) {
    global $trentino_abilitato;
    $calendar = null;
    $type = null;

    extract($params);

    $html = '';

    if (is_array($mat_studenti) and is_array($mat_materie)) {
        $peso_voti = estrai_parametri_singoli("PESO_VOTI", $dati_classe['id_classe'], 'classe');
        if ($peso_voti == "SI") {
            $peso_voti = 'PONDERATA';
        } else {
            $peso_voti = 'ARITMETICA';
        }

        $tipo_visualizzazione_voto = 'codice';

        foreach ($mat_materie as $indice_materia => $materia_tmp) {
            if ($id_materia == $materia_tmp['id_materia']) {
                $materia = $mat_materie[$indice_materia];
            }
        }

        if (stripos($_SERVER['HTTP_USER_AGENT'], 'msie') !== false) {
            $pref_classe = 'ie_';
            $browser = 'ie';
        } else {
            $pref_classe = '';
            $browser = 'moz';
        }
        if ($alto_contrasto == 'SI') {
            $pref_classe .= 'hc_';
        }
        if (!($schermo > 0)) {
            $schermo = 1024;
        }
        $width_nomi = 250;

        if ($browser == 'ie') {
            $width_nomi = $width_nomi - (5);
        }

        if ($schermo <= 800) {
            if ($alto_contrasto == 'NO') {
                $font_size = 8;
                $row_height = 15;
            } else {
                $font_size = 10;
                $row_height = 18;
            }
        } elseif ($schermo > 800 && $schermo <= 1024) {
            if ($alto_contrasto == 'NO') {
                $font_size = 8;
                $row_height = 15;
            } else {
                $font_size = 10;
                $row_height = 18;
            }
        } elseif ($schermo > 1024 && $schermo <= 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 18;
            } else {
                $font_size = 12;
                $row_height = 21;
            }
        } elseif ($schermo > 1280) {
            if ($alto_contrasto == 'NO') {
                $font_size = 10;
                $row_height = 18;
            } else {
                $font_size = 12;
                $row_height = 21;
            }
        }

        $font_size = 16;

        switch ($periodo) {
            case '1':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '2':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '3':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '4':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '5':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '6':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '7':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '8':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '9':
                $nome_pagella = 'Pagella fine anno';
                break;
            case '10':
                $nome_pagella = 'Prove strutturate';
                break;
            case '11':
                $nome_pagella = 'Esami di licenza maestro d\'arte';
                break;
            case '21':
                $nome_pagella = '1a pagellina infraquadrimestrale';
                break;
            case '22':
                $nome_pagella = '2a pagellina infraquadrimestrale';
                break;
            case '23':
                $nome_pagella = '3a pagellina infraquadrimestrale';
                break;
            case '24':
                $nome_pagella = '4a pagellina infraquadrimestrale';
                break;
            case '25':
                $nome_pagella = '5a pagellina infraquadrimestrale';
                break;
            case '26':
                $nome_pagella = '6a pagellina infraquadrimestrale';
                break;
            case '27':
                $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
                break;
            case '28':
                $nome_pagella = 'Pagella fine 2o trimestre';
                break;
            case '29':
                $nome_pagella = 'Pagella fine anno';
                break;
        }
        $nome_pagella .= ' della classe ' . $dati_classe['classe'] . ' ' . $dati_classe['sezione'] . ' ' . $dati_classe['codice'];
        $nome_pagella .= ' per la materia: <br><b>' . $materia['nome_materia_breve'] . '</b>';

        $data_inizio_calcolo_voti = estrai_parametri_singoli("DATA_INIZIO_CALCOLO_VOTI", $dati_classe['id_classe'], 'classe');
        $data_fine_calcolo_voti = estrai_parametri_singoli("DATA_FINE_CALCOLO_VOTI", $dati_classe['id_classe'], 'classe');
        $giudizio_sospeso_6_in_condotta = estrai_parametri_singoli('ABILITA_GIUDIZIO_SOSPESO_6_CONDOTTA');

        $mat_chiusura_scrutini = explode('@', $dati_classe['blocco_scrutini']);

        foreach ($mat_chiusura_scrutini as $singolo_periodo_chiusura) {
            $mat_singolo_periodo = explode('#', $singolo_periodo_chiusura);
            if ($mat_singolo_periodo[0] == $periodo) {
                $stato_chiusura_scrutini = $mat_singolo_periodo[1];
            }
        }

        $html .= "<form method='post'>\n";

        $html .= "<div width='100%' id='div_main' class='div_" . $pref_classe . "tabellone_main'>\n";
        $html .= '<table width=\'100%\'>'
                . '<tr>'
                . '<td align=\'center\'> ' . $nome_pagella
                . '</td>'
                . '</tr>'
                . '<tr><td><br></td></tr>'
                . '</table>';

//		$html .= '<span style="	position:absolute;
//											top: 0px;
//											right: 0px;
//											width:150px;
//											height:' . ($row_height * 2) . 'px;
//											text-align: center;"
//							><input type="button" value="Chiudi" onclick="document.forms[\'form_esci\'].submit();"></span>'."\n";

        $row_start = $row_height * 2;
        $pippo = "#bfdaff";

//		$html .= '<div 	class="div_'.$pref_classe.'tabellone_top"
//									style="	position: static;
//											top:'. $row_start .'px;
//											left:0px;
//											width:100%;
//											font-size: '.$font_size.';"
//							>'."\n";
        // Definisco il tipo di voto da utilizzare
        if (in_array($materia['cpm_scritto'], ['0', '1'])) {
            $vis_scritto = $materia['cpm_scritto'];
        } else {
            $vis_scritto = $materia['scritto'];
        }

        if (in_array($materia['cpm_orale'], ['0', '1'])) {
            $vis_orale = $materia['cpm_orale'];
        } else {
            $vis_orale = $materia['orale'];
        }

        if (in_array($materia['cpm_pratico'], ['0', '1'])) {
            $vis_pratico = $materia['cpm_pratico'];
        } else {
            $vis_pratico = $materia['pratico'];
        }
//calcola_media_voti_studente_singola_materia($id_studente, $id_materia, $start_interval, $end_interval, 'XX', 2);
        //Intestazione
        $border_color = "#544012";
        $html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold;" border=\'1\'>' . "\n";

        $html .= "\t" . '<tr style="background-color:' . $pippo . ';" style="font-weight: bold;">' . "\n"
                . "\t\t" . '<td align="center" style="font-size: ' . $font_size . ';font-weight: bold;">Studente' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 50px;\'>Media' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Proposta' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . ';\'>' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" style=\'border-left: 0px; font-size: ' . $font_size . '; min-width: 80px;\'>Voto' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" colspan=\'2\' style="font-size: ' . $font_size . ';font-weight: bold;">Ore (ore:min)' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '<td align="center" colspan=\'2\' style="font-size: ' . $font_size . ';font-weight: bold;">Recuperi' . "\n"
                . "\t\t" . '</td>' . "\n"
                . "\t\t" . '</tr>' . "\n";

        $fill = true;

        //{{{ <editor-fold defaultstate="collapsed" desc="riordino array per lettura verticale"
        list($parte1, $parte2) = array_chunk($mat_campi_liberi, ceil(count($mat_campi_liberi) / 2));
        $nuovo_array_base = [];
        $cont_campi = 0;
        foreach ($parte1 as $key => $value) {
            $nuovo_array_base[$cont_campi] = $parte1[$key];
            $cont_campi++;
            if ($parte2[$key]) {
                $nuovo_array_base[$cont_campi] = $parte2[$key];
                $cont_campi++;
            }
        }
        $mat_campi_liberi = $nuovo_array_base;
        //}}} </editor-fold>
        //file_put_contents('/tmp/eccezione', print_r($mat_campi_liberi_figli, true), FILE_APPEND);
        //{{{ <editor-fold defaultstate="collapsed" desc="Script">
        // Script per pulsante Copia e maschera di ore e minuti
        $html .= "<script>
                    function copiaValori(nome_classe, nome_textbox, id_studente)
                    {
                        var testo = '';
                        var len = $(\".\"+nome_classe).length;
                        for (var i = 0; i < len; i++)
                        {

                            if ($(\".\"+nome_classe)[i].selectedOptions[0].text != '---')
                            {
                                testo += $(\".\"+nome_classe)[i].selectedOptions[0].text + ' ';
                            }
                        }
                        document.getElementsByName(nome_textbox)[0].value = testo;
                        document.getElementById(\"modificato_\" + id_studente).value='SI';
                    }

                    function abilitaAreaTesto(valore_select, area_testo)
                    {
                        if (valore_select == -1)
                        {
                            area_testo.disabled = false;
                        }
                        else
                        {
                            area_testo.disabled = true;
                        }
                    }

                    function checkCondotta6(select) {
                        if (select.value == 6) {
                            alert('ATTENZIONE! \\n Con valutazione pari a sei decimi allo studente verrà assegnata la sospensione del giudizio.');
                        }
                    }

                    $(document).ready(function () {
                                // Create jqxMaskedInputs
                                $(\".hourInput\").jqxMaskedInput({mask: '####:##'});
                            });
                </script>";
        //}}} </editor-fold>

        $abilita_pulsante_salva = false;

        //{{{ <editor-fold defaultstate="collapsed" desc="blocco recuperi da metà a gosto a metà settembre"
        $data = time();
        $data_limite_agosto = mktime(0, 0, 0, 8, 15, date('Y', $data));
        $data_limite_settembre = mktime(23, 59, 59, 9, 15, date('Y', $data));
        if (($data >= $data_limite_agosto) && ($data <= $data_limite_settembre) && in_array($periodo, ['9', '29'])) {
            $blocco_recuperi = true;
        } else {
            $blocco_recuperi = false;
        }
        //}}} </editor-fold>

        foreach ($mat_studenti as $indice_studente => $studente) {
            //{{{ <editor-fold defaultstate="collapsed">
            $fill = !$fill;
            if ($fill) {
                $pippo = "#bfdaff";
            } else {
                $pippo = "#afc4ff";
            }

            if (count($materia['mat_classi_orig']) == 0) {
                $studente_ha_classe = 'si';
            } else {
                $studente_ha_classe = 'no';
                foreach ($studente['mat_classi'] as $classe_studente) {
                    foreach ($materia['mat_classi_orig'] as $id_classe_orig) {
                        if ($classe_studente['id_classe'] == $id_classe_orig) {
                            $studente_ha_classe = 'si';
                        }
                    }
                    //definire se si vuole inserire un parametro per gestire questa cosa
                    $parametro_multi_classe = 'SI';
                    if ($parametro_multi_classe == 'SI') {
                        foreach ($materia['mat_classi_abb'] as $id_classe_orig) {
                            if ($classe_studente['id_classe'] == $id_classe_orig) {
                                $studente_ha_classe = 'si';
                            }
                        }
                    }
                }
            }

            //inserire parametro da gestire ($mostra_stud_esonerati) se si vuole decidere cosa deve comparire se esonerato per le materie che non vengono fatte o se non deve comparire la riga dello studente
            $mostra_stud_esonerati = 'SI';
            if(!(($mostra_stud_esonerati == 'NO') && ($studente_ha_classe == 'no'))){
                $html .= "\t" . '<tr style="background-color:' . $pippo . '; border-top: 1px solid black;">' . "\n";
                $cont_row++;

                // Dati studente
                $html .= '<td style=\'padding-left: 5px; font-size: ' . $font_size . ';\'>';
                if ($studente['registro'] < 10) {
                    $html .= "&nbsp;" . $studente['registro'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . "\n";
                } else {
                    $html .= $studente['registro'] . ' ' . $studente['cognome'] . ' ' . $studente['nome'] . "\n";
                }
                $html .= '</td>';

                if (
                        ($materia['tipo_materia'] == 'RELIGIONE' and $studente['esonero_religione'] == 1)
                        or
                        $studente_ha_classe == 'no'
                ) {
                    $html .= '<td colspan=\'7\' align=\'center\' style=\'font-size: ' . $font_size . ';\'>--Esonerato--</td>';
                } else {
                    //{{{ <editor-fold defaultstate="collapsed" desc="materia normale">
                    $voto_modificabile = (
                            (
                            $form_stato == 'amministratore'
                            or (
                            $coordinatore == 'SI'
                            and
                            $dati_classe['consiglio_classe_attivo'] == 'SI'
                            )
                            )
                            and
                            $stato_chiusura_scrutini != 'SI'
                            );
                    $proposta_modificabile = (
                            (
                            in_array($current_user, $materia['professori'])
                            and
                            $dati_classe['consiglio_classe_attivo'] == 'NO'
                            )
                            and
                            $stato_chiusura_scrutini != 'SI'
                            );

                    if ($voto_modificabile || $proposta_modificabile) {
                        $abilita_pulsante_salva = true;
                    }

                    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella generata">
                    if (
                            ($tipo_visualizzazione == 'voto_singolo')
                            or ( $tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '1')
                    ) {
                        // Voto Unico
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Inizio inserimento proposta
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        $valore_media = calcola_media_voti_studente_singola_materia($studente['id_studente'], $materia['id_materia'], $data_inizio_calcolo_voti, $data_fine_calcolo_voti, 'XX', 2, "NO", '', $peso_voti);
                        if (intval($valore_media['totale']) == 0) {
                            $valore_media['totale'] = '--';
                        }
                        $html .= $valore_media['totale'] . '</td>';
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        if ($proposta_modificabile) {
                            $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pagellina_' . $studente['id_studente'] . '" ' . "\n";
                            $html .= "\t" . ' " onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\'; ' . "\n";
                            if ($materia['tipo_materia'] == 'CONDOTTA'
                                && $trentino_abilitato != 'SI'
                                && !in_array($dati_classe["tipo_indirizzo"], [4,6,7,8])
                                && in_array($periodo, ['9'])
                                && $giudizio_sospeso_6_in_condotta == 'SI'){
                                $html .= "\t" . ' checkCondotta6(this); ' . "\n";
                            }
                            $html .= "\t" . ' ">' . "\n";
                            if (is_array($materia['schema_voti'])) {
                                $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                foreach ($materia['schema_voti'] as $significato_voto) {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] == $significato_voto['voto']) {
                                        $html .= "\t" . "\t" . '<option selected ';
                                    } else {
                                        $html .= "\t" . "\t" . '<option ';
                                    }
    //                                    if ($tipo_visualizzazione_voto == 'codice') {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    } else {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                    $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                    if ($materia['tipo_materia'] == 'RELIGIONE'){
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['codice'] . ')</option>' . "\n";
                                    }else{
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                            }
                            $html .= "\t" . '</select>' . "\n";
                        } else {
                            if ($materia['tipo_materia'] == 'RELIGIONE'){
                                if (is_array($materia['schema_voti'])) {
                                    $significato_voto_tradotto = '';
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] == $significato_voto['voto']) {
                                            $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                            $html .= $significato_voto_tradotto . "\n";
                                            break;
                                        }
                                    }
                                }else{
                                    $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] . "\n";
                                }
                            }else{
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pagellina'] . "\n";
                            }
                        }
                        $html .= '</td>';

                        // Legenda voti
                        $html .= '<td align=\'center\' style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . '; font-weight: normal;\'>'
                                . 'Unico'
                                . '</td>' . "\n";

                        // Inizio inserimento voto
                        $html .= '<td align=\'center\' style=\'padding: 3px 0px 3px 0px; border-left: 0px; font-size: ' . $font_size . '\'>';
                        if ($voto_modificabile) {
                            $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pagellina_' . $studente['id_studente'] . '"  ' . "\n";
                            $html .= "\t" . ' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\'; ' . "\n";
                            if ($materia['tipo_materia'] == 'CONDOTTA'
                                && $trentino_abilitato != 'SI'
                                && !in_array($dati_classe["tipo_indirizzo"], [4,6,7,8])
                                && in_array($periodo, ['9'])
                                && $giudizio_sospeso_6_in_condotta == 'SI'){
                                $html .= "\t" . ' checkCondotta6(this); ' . "\n";
                            }
                            $html .= "\t" . ' ">' . "\n";
                            if (is_array($materia['schema_voti'])) {
                                $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                foreach ($materia['schema_voti'] as $significato_voto) {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $significato_voto['voto']) {
                                        $html .= "\t" . "\t" . '<option selected ';
                                    } else {
                                        $html .= "\t" . "\t" . '<option ';
                                    }
    //                                if($tipo_visualizzazione_voto == 'codice')
    //                                {
    //                                    $significato_voto_tradotto = $significato_voto['codice'];
    //                                }
    //                                else
    //                                {
    //                                    $significato_voto_tradotto = $significato_voto['valore'];
    //                                }
                                    $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                    if ($materia['tipo_materia'] == 'RELIGIONE'){
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['codice'] . ')</option>' . "\n";
                                    }else{
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                            }
                            $html .= "\t" . '</select>' . "\n";
                        } else {
                            if ($materia['tipo_materia'] == 'RELIGIONE'){
                                if (is_array($materia['schema_voti'])) {
                                    $significato_voto_tradotto = '';
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] == $significato_voto['voto']) {
                                            $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                            $html .= $significato_voto_tradotto . "\n";
                                            break;
                                        }
                                    }
                                }else{
                                    $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . "\n";
                                }
                            }else{
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . "\n";
                            }
                            //$html .= '<input type="hidden" name="voto_pagellina_' . $materia['id_materia'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pagellina'] . '">' . "\n";
                        }
                        $html .= '</td>' . "\n";
                        //}}} </editor-fold>
                    } else {
                        // Voto multiplo
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Inizio inserimento proposta
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>';
                        //$valore_media = calcola_media_voti_studente_singola_materia($studente['id_studente'], $materia['id_materia'], $data_inizio_calcolo_voti, $data_fine_calcolo_voti, 'XX', 2);
                        $valore_media = calcola_media_voti_studente_singola_materia($studente['id_studente'], $materia['id_materia'], $data_inizio_calcolo_voti, $data_fine_calcolo_voti, 'XX', 2, "NO", '', $peso_voti);
                        if (intval($valore_media['scritto']) == 0) {
                            $valore_media['scritto'] = '--';
                        }
                        if (intval($valore_media['orale']) == 0) {
                            $valore_media['orale'] = '--';
                        }
                        if (intval($valore_media['pratico']) == 0) {
                            $valore_media['pratico'] = '--';
                        }

                        $dato_medie = '';
                        if ($vis_scritto == 1) {
                            $dato_medie .= $valore_media['scritto'] . '<br>';
                        }
                        if ($vis_orale == 1) {
                            $dato_medie .= $valore_media['orale'] . '<br>';
                        }
                        if ($vis_pratico == 1) {
                            $dato_medie .= $valore_media['pratico'];
                        }

                        $html .= $dato_medie . '</td>';

                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . ';\'>';
                        if ($vis_scritto == 1) {
                            if ($proposta_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_scritto_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if ($tipo_visualizzazione_voto == 'codice') {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    } else {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_scritto_pagella'] . '<br>';
                            }
                        }
                        if ($vis_orale == 1) {
                            if ($proposta_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_orale_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if ($tipo_visualizzazione_voto == 'codice') {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    } else {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_orale_pagella'] . '<br>';
                            }
                        }
                        if ($vis_pratico == 1 && (
                                ($tipo_visualizzazione == 'scritto_orale_pratico') ||
                                ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                )) {
                            if ($proposta_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pratico_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if ($tipo_visualizzazione_voto == 'codice') {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    } else {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['proposta_voto_pratico_pagella'] . '<br>';
                            }
                        }
                        $html .= "</td>";

                        // Legenda voti
                        $html .= '<td align=\'center\' style=\'border-left: 0px; border-right: 0px; font-size: ' . $font_size . '; font-weight: normal;\'>';
                        if ($vis_scritto == 1) {
                            $html .= 'scritto<br>' . "\n";
                        }
                        if ($vis_orale == 1) {
                            $html .= 'orale<br>' . "\n";
                        }
                        if ($vis_pratico == 1 && (
                                ($tipo_visualizzazione == 'scritto_orale_pratico') ||
                                ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                )) {
                            $html .= 'pratico<br>' . "\n";
                        }
                        $html .= "</td>";

                        // Inizio inserimento voto
                        $html .= '<td align=\'center\' style=\'padding: 3px 0px 3px 0px; border-left: 0px; font-size: ' . $font_size . ';\'>';
                        if ($vis_scritto == 1) {
                            if ($voto_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_scritto_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if($tipo_visualizzazione_voto == 'codice')
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    }
    //                                    else
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . '<br>' . "\n";
                                //$html .= '<input type="hidden" name="voto_scritto_pagella_' . $studente['id_studente'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_scritto_pagella'] . '">' . "\n";
                            }
                        }
                        if ($vis_orale == 1) {
                            if ($voto_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_orale_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if($tipo_visualizzazione_voto == 'codice')
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    }
    //                                    else
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . '<br>' . "\n";
                                //$html .= '<input type="hidden" name="voto_orale_pagella_' . $studente['id_studente'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_orale_pagella'] . '">' . "\n";
                            }
                        }
                        if ($vis_pratico == 1 && (
                                ($tipo_visualizzazione == 'scritto_orale_pratico') ||
                                ($tipo_visualizzazione == 'personalizzato' and $materia['tipo_voto_personalizzato'] == '3')
                                )) {
                            if ($voto_modificabile) {
                                $html .= "\t" . '<select  style="font-size: ' . $font_size . ';font-weight: bold; width: 70px; margin: 1;" name="voto_pratico_pagella_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                if (is_array($materia['schema_voti'])) {
                                    $html .= "\t" . "\t" . '<option value=""></option>' . "\n";
                                    foreach ($materia['schema_voti'] as $significato_voto) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] == $significato_voto['voto']) {
                                            $html .= "\t" . "\t" . '<option selected ';
                                        } else {
                                            $html .= "\t" . "\t" . '<option ';
                                        }
    //                                    if($tipo_visualizzazione_voto == 'codice')
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['codice'];
    //                                    }
    //                                    else
    //                                    {
    //                                        $significato_voto_tradotto = $significato_voto['valore'];
    //                                    }
                                        $significato_voto_tradotto = $significato_voto['valore_pagella'];
                                        $html .= 'value="' . $significato_voto['voto'] . '">' . $significato_voto_tradotto . ' (' . $significato_voto['voto'] . ')</option>' . "\n";
                                    }
                                }
                                $html .= "\t" . '</select><br>' . "\n";
                            } else {
                                $html .= $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . '<br>' . "\n";
                                //$html .= '<input type="hidden" name="voto_pratico_pagella_' . $studente['id_studente'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['voto_pratico_pagella'] . '">' . "\n";
                            }
                        }
                        $html .= '</td>' . "\n";
                        //}}} </editor-fold>
                    }

                    if ($materia['tipo_materia'] == 'CONDOTTA') {
                        $id_materia_condotta = $materia['id_materia'];

                        if ($trentino_abilitato == 'SI' || in_array($dati_classe["tipo_indirizzo"], [4,6,7,8]) || !in_array($periodo, ['9']) || $giudizio_sospeso_6_in_condotta != 'SI'){
                            $html .= '<td colspan=\'5\'></td>' . "\n";
                        } else {
                            $html .= '<td colspan=\'2\'></td>' . "\n";
                            $html .= '<td align=\'right\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>'
                                    . 'Esito:'
                                    . '</td>' . "\n"
                                    . "\t" . '<td style=\'border-left: 0px; font-size: ' . $font_size . '\'>';
                            if ($proposta_modificabile || $voto_modificabile) {
                                if (($form_stato == 'professore' && $current_user == $materia['professori'][0]) || $form_stato == 'amministratore' || $coordinatore == 'SI') {
                                    $html .= "\t" . '<select style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="esito_recupero_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";

                                    switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero']) {
                                        case 'SI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NO':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'ASSENTE':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NI">Parziale</option>' . "\n";
                                            break;
                                        default:
                                            $html .= "\t" . "\t" . '<option selected value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                }
                            } else {
                                switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero']) {
                                    case 'SI':
                                        $html .= 'Positivo';
                                        break;
                                    case 'NO':
                                        $html .= 'Negativo';
                                        break;
                                    case 'ASSENTE':
                                        $html .= 'Assente';
                                        break;
                                    case 'NI':
                                        $html .= 'Parziale';
                                        break;
                                    default:
                                        $html .= '------';
                                        break;
                                }
                            }
                            $html .= '</td>' . "\n";
                        }
                    } else {
                        // Inizio inserimento ore
                        //{{{ <editor-fold defaultstate="collapsed">
                        // Assenza
                        $html .= '<td align=\'center\' style=\'border-right: 0px; font-size: ' . $font_size . '; min-width: 130px;\'>' . "\n";
                        $html .= "\t" .
                                '<input type="hidden"
                                        id="ore_assenza_' . $studente['id_studente'] . '"
                                        name="ore_assenza_' . $studente['id_studente'] . '"
                                        value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza']) . '"
                                        onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';"
                                    >' . "\n";
                        $html .= 'Assenze<br>';
                        if ($proposta_modificabile || $voto_modificabile) {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                            pattern="[0-9]"
                                            maxlength="4"
                                            id="ore_assenza_ore_' . $studente['id_studente'] . '"
                                            name="ore_assenza_ore_' . $studente['id_studente'] . '"
                                            value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] / 60) . '"
                                            onkeyup="jm_integermask(this);"
                                            onmouseup="jm_integermask(this);"
                                            onchange="
                                            document.getElementById(\'ore_assenza_' . $studente['id_studente'] . '\').value
                                            =
                                            (parseInt(document.getElementById(\'ore_assenza_ore_' . $studente['id_studente'] . '\').value)*60)
                                            +
                                            (parseInt(document.getElementById(\'ore_assenza_min_' . $studente['id_studente'] . '\').value));
                                            document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';
                                            "
                                        >' . "\n";
                        } else {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] / 60);
                        }
                        $html .= ':';
                        if ($proposta_modificabile || $voto_modificabile) {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                            pattern="[0-9]"
                                            maxlength="2"
                                            id="ore_assenza_min_' . $studente['id_studente'] . '"
                                            name="ore_assenza_min_' . $studente['id_studente'] . '"
                                            value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] % 60) . '"
                                            onkeyup="jm_integermask(this);"
                                            onmouseup="jm_integermask(this);"
                                            onchange="document.getElementById(\'ore_assenza_' . $studente['id_studente'] . '\').value
                                            =
                                            (parseInt(document.getElementById(\'ore_assenza_ore_' . $studente['id_studente'] . '\').value)*60)
                                            +
                                            (parseInt(document.getElementById(\'ore_assenza_min_' . $studente['id_studente'] . '\').value));
                                            document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';
                                            "
                                        >' . "\n";
                        } else {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['ore_assenza'] % 60);
                        }
                        $html .= '</td>' . "\n";

                        // Monteore
                        $html .= '<td align=\'center\' style=\'border-left: 0px; font-size: ' . $font_size . '; min-width: 130px;\'>' . "\n";
                        $html .= "\t" .
                                '<input type="hidden"
                                        id="monteore_totale_' . $studente['id_studente'] . '"
                                        name="monteore_totale_' . $studente['id_studente'] . '"
                                        value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale']) . '"
                                        onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';"
                                    >' . "\n";
                        $html .= 'Monteore<br>';
                        if ($proposta_modificabile || $voto_modificabile) {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . '" type="text" size="3"
                                            pattern="[0-9]"
                                            maxlength="4"
                                            id="monteore_totale_ore_' . $studente['id_studente'] . '"
                                            name="monteore_totale_ore_' . $studente['id_studente'] . '"
                                            value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] / 60) . '"
                                            onkeyup="jm_integermask(this);"
                                            onmouseup="jm_integermask(this);"
                                            onchange="
                                            document.getElementById(\'monteore_totale_' . $studente['id_studente'] . '\').value
                                            =
                                            (parseInt(document.getElementById(\'monteore_totale_ore_' . $studente['id_studente'] . '\').value)*60)
                                            +
                                            (parseInt(document.getElementById(\'monteore_totale_min_' . $studente['id_studente'] . '\').value));
                                            document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';
                                            "
                                        >' . "\n";
                        } else {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] / 60);
                        }
                        $html .= ':';
                        if ($proposta_modificabile || $voto_modificabile) {
                            $html .= "\t" .
                                    '<input  style="font-size: ' . $font_size . 'px" type="text" size="3"
                                            pattern="[0-9]"
                                            maxlength="2"
                                            id="monteore_totale_min_' . $studente['id_studente'] . '"
                                            name="monteore_totale_min_' . $studente['id_studente'] . '"
                                            value="' . intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] % 60) . '"
                                            onkeyup="jm_integermask(this);"
                                            onmouseup="jm_integermask(this);"
                                            onchange="document.getElementById(\'monteore_totale_' . $studente['id_studente'] . '\').value
                                            =
                                            (parseInt(document.getElementById(\'monteore_totale_ore_' . $studente['id_studente'] . '\').value)*60)
                                            +
                                            (parseInt(document.getElementById(\'monteore_totale_min_' . $studente['id_studente'] . '\').value));
                                            document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';
                                            "
                                        >' . "\n";
                        } else {
                            $html .= intval($mat_voti[$studente['id_studente']][$materia['id_materia']]['monteore_totale'] % 60);
                        }
                        $html .= '</td>';
                        //}}} </editor-fold>
                        // Inizio inserimento recuperi ed esiti
                        //{{{ <editor-fold defaultstate="collapsed">
                        if ($materia['tipo_materia'] == 'ALTERNANZA') {
                            $html .= '<td colspan=\'2\'></td>' . "\n";
                        } else {
                            $html .= '<td align=\'right\' style=\'border-right: 0px; font-size: ' . $font_size . '\'>'
                                    . 'Tipo:'
                                    . '<br>'
                                    . 'Esito:'
                                    . '</td>' . "\n"
                                    . "\t" . '<td style=\'border-left: 0px; font-size: ' . $font_size . '\'>';
                            if ($proposta_modificabile || $voto_modificabile) {
                                if (!$blocco_recuperi || 1 == 1) {
                                    $html .= '<select  style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="tipo_recupero_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n"
                                            . "\t" . "\t" . '<option value="">Nessun recupero necessario</option>' . "\n";

                                    foreach ($tipi_recupero as $tipo_recupero) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore']) {
                                            $html .= "\t" . "\t" . '<option selected ' . "\n";
                                        } else {
                                            $html .= "\t" . "\t" . '<option ' . "\n";
                                        }
                                        $html .= 'value="' . $tipo_recupero['valore'] . '">' . $tipo_recupero['nome'] . '</option>' . "\n";
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                } else {
                                    if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '') {
                                        $html .= '------';
                                    } else {
                                        foreach ($tipi_recupero as $tipo_recupero) {
                                            if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore']) {
                                                $html .= $tipo_recupero['nome'];
                                                $html .= '<input type="hidden" name="tipo_recupero_' . $studente['id_studente'] . '" value="' . $mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] . '">';
                                            }
                                        }
                                    }
                                }
                                $html .= "<br>";
                                if (($form_stato == 'professore' && $current_user == $materia['professori'][0]) || $form_stato == 'amministratore' || $coordinatore == 'SI') {
                                    $html .= "\t" . '<select style="font-size: ' . $font_size . ';font-weight: normal; width: 300px; margin: 1;" name="esito_recupero_' . $studente['id_studente'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";

                                    switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero']) {
                                        case 'SI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NO':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'ASSENTE':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                        case 'NI':
                                            $html .= "\t" . "\t" . '<option value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option selected value="NI">Parziale</option>' . "\n";
                                            break;
                                        default:
                                            $html .= "\t" . "\t" . '<option selected value="">Nessun esito definito/da definire</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="SI">Positivo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NO">Negativo</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="ASSENTE">Assente</option>' . "\n";
                                            $html .= "\t" . "\t" . '<option value="NI">Parziale</option>' . "\n";
                                            break;
                                    }
                                    $html .= "\t" . '</select>' . "\n";
                                }
                            } else {
                                if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == '') {
                                    $html .= '------';
                                } else {
                                    foreach ($tipi_recupero as $tipo_recupero) {
                                        if ($mat_voti[$studente['id_studente']][$materia['id_materia']]['tipo_recupero'] == $tipo_recupero['valore']) {
                                            $html .= $tipo_recupero['nome'];
                                        }
                                    }
                                }
                                $html .= '<br>';
                                switch ($mat_voti[$studente['id_studente']][$materia['id_materia']]['esito_recupero']) {
                                    case 'SI':
                                        $html .= 'Positivo';
                                        break;
                                    case 'NO':
                                        $html .= 'Negativo';
                                        break;
                                    case 'ASSENTE':
                                        $html .= 'Assente';
                                        break;
                                    case 'NI':
                                        $html .= 'Parziale';
                                        break;
                                    default:
                                        $html .= '------';
                                        break;
                                }
                            }
                            $html .= '</td>' . "\n";
                        }
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>

                    $html .= "\t" . '</tr>' . "\n";

                    // Campi liberi
                    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione campi liberi">
                    if (
                            (
                            (
                            is_array($mat_campi_liberi)
                            and ( !empty($mat_campi_liberi))
                            ) ||
                            (
                            is_array($mat_campi_liberi_parenti)
                            and ( !empty($mat_campi_liberi_parenti))
                            )
                            )
                            and (
                            $dati_classe['livello_abilitazione_campi_liberi'] == 'totale'
                            or (
                            $dati_classe['livello_abilitazione_campi_liberi'] == 'condotta'
                            and
                            $materia['tipo_materia'] == 'CONDOTTA'
                            )
                            )
                    ) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Campi liberi normali">
                        $html .= "\t" . '<tr style="background-color:' . $pippo . ';">' . "\n";
                        $cont_row++;

                        $html .= '<td  colspan="8">' . "\n" . "\n";

                        $html .= '<table width="100%" style="font-size: ' . $font_size . ';font-weight: bold">' . "\n";
                        $html .= "\t" . '<tr style="background-color:' . $pippo . ';">' . "\n";
                        $html .= "\t" . "\t" . '<td width=\'100%\'>';
                        $html .= '<table width=\'100%\' style="font-size: ' . $font_size . ';font-weight: bold; border-collapse: separate;">' . "\n";
                        $html .= "\t" . '<tr style="background-color:' . $pippo . ';">' . "\n";
                        $cont_campi = 1;
                        foreach ($mat_campi_liberi as $key => $campo_libero) {
                            if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia']) {
                                //{{{ <editor-fold defaultstate="collapsed">
                                $html .= "\t" . "\t" . '<td align=\'center\' width=\'50%\' style=\'border: 1px dotted grey; padding: 3px;\'>';
                                $html .= $campo_libero['nome'];
                                $html .= '<br>';
                                if (stripos($campo_libero['tipo_valore'], 'PRECOMPILATO') !== false) {
                                    if ($voto_modificabile || $proposta_modificabile) {
                                        // Precompilato e precompilato testo modifica
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        if (count($campo_libero['valori_precomp']) > 0) {
                                            $html .= "\t" . "\t" . "\t" . '<select style=\'width: 98%;\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\'; abilitaAreaTesto(this.value, cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . ');">' . "\n";
                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>';
                                            foreach ($campo_libero['valori_precomp'] as $valore_precomp) {
                                                if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                } else {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                }

                                                switch ($campi_liberi_visualizzazione) {
                                                    case 'valore':
                                                        $descrizione_select = $valore_precomp['valore'];
                                                        break;
                                                    case 'codice':
                                                        $descrizione_select = $valore_precomp['codice'];
                                                        break;
                                                    case 'descrizione':
                                                        $descrizione_select = $valore_precomp['descrizione'];
                                                        break;
                                                    default:
                                                        $descrizione_select = $valore_precomp['codice'];
                                                        break;
                                                }

                                                $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                            }
                                        }
                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                            if (count($campo_libero['valori_precomp']) > 0) {
                                                if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == -1) {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                } else {
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                }
                                                $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                            } else {
                                                $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'-1\'>' . "\n";
                                            }
                                        }

                                        if (count($campo_libero['valori_precomp']) > 0) {
                                            $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                            $html .= "\t" . "\t" . "\t";
                                        }
                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                            if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == -1 ||
                                                    count($campo_libero['valori_precomp']) == 0
                                            ) {
                                                $disabled = '';
                                            } else {
                                                $disabled = 'disabled';
                                            }

                                            if ($campo_libero['tipo_precompilato_testo'] == 'textarea') {
                                                $html .= '<textarea ' . $disabled . ' rows=\'5\' cols=' . $campo_libero['dimensione_precompilato_testo'] . ' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '</textarea>' . "\n";
                                            } else {
                                                $html .= '<input ' . $disabled . ' size=\'' . $campo_libero['dimensione_precompilato_testo'] . ' \' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                            }
                                        }
                                        //}}} </editor-fold>
                                    } else {
                                        // Precompilato e precompilato testo sola visualizzazione
                                        //{{{ <editor-fold defaultstate="collapsed">
                                        $descrizione_campo_libero = '';
                                        foreach ($campo_libero['valori_precomp'] as $valore_precomp) {
                                            if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                switch ($campi_liberi_visualizzazione) {
                                                    case 'valore':
                                                        $descrizione_campo_libero = $valore_precomp['valore'];
                                                        break;
                                                    case 'codice':
                                                        $descrizione_campo_libero = $valore_precomp['codice'];
                                                        break;
                                                    case 'descrizione':
                                                        $descrizione_campo_libero = $valore_precomp['descrizione'];
                                                        break;
                                                    default:
                                                        $descrizione_campo_libero = $valore_precomp['codice'];
                                                        break;
                                                }
                                            }
                                        }

                                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                            if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == -1) {
                                                $descrizione_campo_libero = $campo_libero['valore'][$studente['id_studente']]['valore_testuale'];
                                            }
                                        }

                                        if ($descrizione_campo_libero == '') {
                                            $descrizione_campo_libero = '------';
                                        }

                                        $html .= '<span style=\'font-weight: normal;\'>' . $descrizione_campo_libero . '</span>';
                                        //}}} </editor-fold>
                                    }
                                } elseif ($campo_libero['tipo_valore'] == 'NUMERO') {
                                    if ($voto_modificabile || $proposta_modificabile) {
                                        $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . intval($campo_libero['valore'][$studente['id_studente']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                    }
                                } else {
                                    // Caso solo TESTO
                                    if ($voto_modificabile || $proposta_modificabile) {
                                        if ($campo_libero['tipo_precompilato_testo'] == 'textarea') {
                                            $html .= '<textarea rows=\'5\' cols=' . $campo_libero['dimensione_precompilato_testo'] . ' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '</textarea>' . "\n";
                                        } else {
                                            $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                        }
                                    }
                                }
                                if ($campo_libero['valore'][$studente['id_studente']]['id_valore_campo_libero'] > 0) {
                                    $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . $campo_libero['valore'][$studente['id_studente']]['id_valore_campo_libero'] . '\'>' . "\n";
                                }
                                $html .= "\t" . "\t" . '</td>';
                                // creazione della nuova riga ogni due colonne
                                if (($cont_campi) % 2 == 0) {
                                    $html .= "\t" . '</tr><tr style="background-color:' . $pippo . ';">' . "\n";
                                }
                                $cont_campi++;
                                //}}} </editor-fold>
                            }
                        }
                        //verifico che sia pari perchè nel ciclo prima il contatore viene incrementato alla fine e nel caso aggiungo la cella vuota
                        if (($cont_campi) % 2 == 0) {
                            $html .= "\t" . "\t" . '<td width=\'50%\'></td>';
                        }
                        $html .= "\t" . '</tr>';
                        $html .= "</table>";
                        //}}} </editor-fold>
                        //{{{ <editor-fold defaultstate="collapsed" desc="Campi liberi padre-figlio">
                        $html .= '<table width=\'100%\' style="font-size: ' . $font_size . ';font-weight: bold;">' . "\n";
                        foreach ($mat_campi_liberi_parenti as $campo_libero) {
                            if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia']) {
                                //{{{ <editor-fold defaultstate="collapsed">
                                $html .= "\t" . '<tr><td><span style=\'font-size: 5;\'><br></span></td></tr>' . "\n";
                                $html .= "\t" . '<tr style="background-color:' . $pippo . '; border-top: 1px dotted grey;">' . "\n";
                                $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\'>';
                                $html .= $campo_libero['nome'];
                                $html .= '</td>' . "\n";
                                $html .= "\t" . '</tr>' . "\n";
                                if ($voto_modificabile || $proposta_modificabile) {
                                    $cont = 0;
                                    foreach ($campo_libero['figli'] as $campo_libero_figlio) {
                                        $html .= "\t" . '<tr style="background-color:' . $pippo . ';">' . "\n";
                                        $html .= "\t" . "\t" . '<td width=\'60%\'>';
                                        $html .= $campo_libero_figlio['nome'];
                                        if (stripos($campo_libero_figlio['tipo_valore'], 'PRECOMPILATO') !== false) {
                                            //{{{ <editor-fold defaultstate="collapsed">
                                            if (count($campo_libero_figlio['valori_precomp']) > 0) {
                                                $html .= "\t" . "\t" . "\t" . '<select style=\'width: 100%\' name=\'cl_precomp_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' class=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                                $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>."\n"';
                                                foreach ($campo_libero_figlio['valori_precomp'] as $valore_precomp) {
                                                    if ($campo_libero_figlio['valore'][$studente['id_studente']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                    } else {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                    }

                                                    switch ($campi_liberi_visualizzazione) {
                                                        case 'valore':
                                                            $descrizione_select = $valore_precomp['valore'];
                                                            break;
                                                        case 'codice':
                                                            $descrizione_select = $valore_precomp['codice'];
                                                            break;
                                                        case 'descrizione':
                                                            $descrizione_select = $valore_precomp['descrizione'];
                                                            break;
                                                        default:
                                                            $descrizione_select = $valore_precomp['codice'];
                                                            break;
                                                    }

                                                    $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                                }
                                            }
                                            if ($campo_libero_figlio['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                                if (count($campo_libero_figlio['valori_precomp']) > 0) {
                                                    if ($campo_libero_figlio['valore'][$studente['id_studente']]['id_valore_precomp'] == -1) {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                    } else {
                                                        $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                    }
                                                    $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                                } else {
                                                    $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'-1\'>' . "\n";
                                                }
                                            }

                                            if (count($campo_libero_figlio['valori_precomp']) > 0) {
                                                $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                                $html .= "\t" . "\t" . "\t";
                                            }
                                            if ($campo_libero_figlio['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                                if ($campo_libero_figlio['tipo_precompilato_testo'] == 'textarea') {
                                                    $html .= '<textarea rows=\'5\' cols=' . $campo_libero_figlio['dimensione_precompilato_testo'] . ' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . $campo_libero_figlio['valore'][$studente['id_studente']]['valore_testuale'] . '</textarea>' . "\n";
                                                } else {
                                                    $html .= '<input size=\'' . $campo_libero_figlio['dimensione_precompilato_testo'] . ' \' type=\'text\' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero_figlio['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                                }
                                            }
                                            //}}} </editor-fold>
                                        } elseif ($campo_libero_figlio['tipo_valore'] == 'NUMERO') {
                                            $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . intval($campo_libero_figlio['valore'][$studente['id_studente']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                        } else {
                                            // Caso solo TESTO
                                            if ($campo_libero_figlio['tipo_precompilato_testo'] == 'textarea') {
                                                $html .= '<textarea rows=\'5\' cols=' . $campo_libero_figlio['dimensione_precompilato_testo'] . ' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . $campo_libero_figlio['valore'][$studente['id_studente']]['valore_testuale'] . '</textarea>' . "\n";
                                            } else {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero_figlio['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                            }
                                        }

                                        if ($campo_libero_figlio['valore'][$studente['id_studente']]['id_valore_campo_libero'] > 0) {
                                            $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero_figlio['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . $campo_libero_figlio['valore'][$studente['id_studente']]['id_valore_campo_libero'] . '\'>' . "\n";
                                        }
                                        $html .= '</td>' . "\n";

                                        //-------- Celle del Copia e del riquadro di testo (inseriti qui per avere il rowspan)
                                        if ($cont == 0) {
                                            $html .= "\t" . "\t" . '<td rowspan=\'' . count($campo_libero['figli']) . '\' align=\'center\'>';
                                            if ($campo_libero['tipo_valore'] != 'RAGGRUPPAMENTO') {
                                                $html .= '<input type=\'button\' value=\'Copia ->\' style=\'height: 60px;\' onclick="copiaValori(\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\', \'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\', \'' . $studente['id_studente'] . '\'); "';
                                                $html .= 'onmouseover="document.getElementById(\'tooltip_copia\').style.display = \'\';"';
                                                $html .= 'onmouseout="document.getElementById(\'tooltip_copia\').style.display = \'none\';"';
                                                $html .= '>';
                                            }
                                            $html .= '</td>' . "\n";
                                            $html .= "\t" . "\t" . '<td rowspan=\'' . count($campo_libero['figli']) . '\'>';
                                            if (stripos($campo_libero['tipo_valore'], 'PRECOMPILATO') !== false) {
                                                //{{{ <editor-fold defaultstate="collapsed">
                                                if (count($campo_libero['valori_precomp']) > 0) {
                                                    $html .= "\t" . "\t" . "\t" . '<select style=\'width: 50%;\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                                    $html .= "\t" . "\t" . "\t" . "\t" . '<option value="">---</option>."\n"';
                                                    foreach ($campo_libero['valori_precomp'] as $valore_precomp) {
                                                        if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                        } else {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                        }

                                                        switch ($campi_liberi_visualizzazione) {
                                                            case 'valore':
                                                                $descrizione_select = $valore_precomp['valore'];
                                                                break;
                                                            case 'codice':
                                                                $descrizione_select = $valore_precomp['codice'];
                                                                break;
                                                            case 'descrizione':
                                                                $descrizione_select = $valore_precomp['descrizione'];
                                                                break;
                                                            default:
                                                                $descrizione_select = $valore_precomp['codice'];
                                                                break;
                                                        }

                                                        $html .= 'value=\'' . $valore_precomp['id_valore_precomp'] . '\'>' . $descrizione_select . '</option>' . "\n";
                                                    }
                                                }
                                                if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                                    if (count($campo_libero['valori_precomp']) > 0) {
                                                        if ($campo_libero['valore'][$studente['id_studente']]['id_valore_precomp'] == -1) {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option selected ';
                                                        } else {
                                                            $html .= "\t" . "\t" . "\t" . "\t" . '<option ';
                                                        }
                                                        $html .= 'value=\'-1\'>Altro</option>' . "\n";
                                                    } else {
                                                        $html .= '<input type=\'hidden\' name=\'cl_precomp_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'-1\'>' . "\n";
                                                    }
                                                }

                                                if (count($campo_libero['valori_precomp']) > 0) {
                                                    $html .= "\t" . "\t" . "\t" . '</select>' . "\n";
                                                    $html .= "\t" . "\t" . "\t";
                                                }
                                                if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                                                    if ($campo_libero['tipo_precompilato_testo'] == 'textarea') {
                                                        $html .= '<textarea rows=\'5\' cols=' . $campo_libero['dimensione_precompilato_testo'] . ' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '</textarea>' . "\n";
                                                    } else {
                                                        $html .= '<input size=\'' . $campo_libero['dimensione_precompilato_testo'] . ' \' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                                    }
                                                }
                                                //}}} </editor-fold>
                                            } elseif ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO') {
                                                $html .= "\t" . "\t" . "\t" . "\n";
                                            } elseif ($campo_libero['tipo_valore'] == 'NUMERO') {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'2\' type=\'text\' name=\'cl_number_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . intval($campo_libero['valore'][$studente['id_studente']]['valore_numerico']) . '\' onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                            } else {
                                                $html .= "\t" . "\t" . "\t" . '<input size=\'10\' type=\'text\' name=\'cl_text_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value="' . $campo_libero['valore'][$studente['id_studente']]['valore_testuale'] . '" onchange="document.getElementById(\'modificato_' . $studente['id_studente'] . '\').value=\'SI\';">' . "\n";
                                            }
                                            if ($campo_libero['valore'][$studente['id_studente']]['id_valore_campo_libero'] > 0) {
                                                $html .= "\t" . "\t" . "\t" . '<input type=\'hidden\' name=\'cl_id_valore_' . $campo_libero['id_campo_libero'] . '_' . $studente['id_studente'] . '\' value=\'' . $campo_libero['valore'][$studente['id_studente']]['id_valore_campo_libero'] . '\'>' . "\n";
                                            }
                                            $html .= '</td>' . "\n";
                                        }
                                        //--------
                                        $cont++;
                                    }
                                } else {
                                    $html .= "\t" . '<tr style="background-color:' . $pippo . ';">' . "\n";
                                    $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\' style=\'font-weight: normal;\'>';
                                    if (trim($campo_libero['valore'][$studente['id_studente']]['valore_testuale'] == '')) {
                                        if ($campo_libero['tipo_valore'] == 'RAGGRUPPAMENTO') {
                                            $html .= '<br></td>';
                                            foreach ($campo_libero['figli'] as $campo_del_raggruppamento) {
                                                $html .= "\t" . '<tr style="background-color:' . $bgcolor . ';">' . "\n";
                                                $html .= "\t" . "\t" . '<td colspan=\'3\' align=\'center\' style=\'font-weight: normal;\'>' . "\n";
                                                $html .= "\t" . "\t" . "\t" . '<b>' . $campo_del_raggruppamento['descrizione'] . '</b>' . "\n";
                                                $html .= "\t" . "\t" . "\t" . '<br>' . "\n";
                                                if ($campo_del_raggruppamento['valore'][$studente['id_studente']]['id_valore_precomp'] > 0) {
                                                    foreach ($campo_del_raggruppamento['valori_precomp'] as $valore_precomp) {
                                                        if ($campo_del_raggruppamento['valore'][$studente['id_studente']]['id_valore_precomp'] == $valore_precomp['id_valore_precomp']) {
                                                            $html .= $valore_precomp['descrizione'];
                                                        }
                                                    }
                                                } else {
                                                    $html .= '------';
                                                }
                                                $html .= '</td>';
                                                $html .= '</tr>';
                                            }
                                        } else {
                                            $html .= '------';
                                        }
                                    } else {
                                        $html .= $campo_libero['valore'][$studente['id_studente']]['valore_testuale'];
                                    }
                                    $html .= '</td>';
                                    $html .= '</tr>';
                                }
                                $html .= "\t" . '</tr>' . "\n";
                                //}}} </editor-fold>
                            }
                        }
                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        $html .= '</table>' . "\n" . "\n";
                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        $html .= '</table>' . "\n" . "\n";

                        $html .= '</td>' . "\n";
                        $html .= "\t" . '</tr>' . "\n";
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>

                    $html .= "<input type='hidden' id='modificato_" . $studente['id_studente'] . "' name='modificato_" . $studente['id_studente'] . "'>\n"
                            . "<input type='hidden' name='id_voto_pagellina_" . $studente['id_studente'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['id_voto_pagellina'] . "'>\n"
                            . "<input type='hidden' name='giudizio_analitico_" . $studente['id_studente'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['giudizio_analitico'] . "'>\n"
                            . "<input type='hidden' name='modificato_da_amministratore_" . $studente['id_studente'] . "' value='" . $mat_voti[$studente['id_studente']][$materia['id_materia']]['modificato_da_amministratore'] . "'>\n";
                    //}}} </editor-fold>
                }
            }
            //}}} </editor-fold>
        }

        // Tooltip pulsante copia
        $html .= "<div id='tooltip_copia' style='position: fixed; bottom: 5px; background-color: #303030; color: white; padding: 10px; display: none; left: 50%;
                    -ms-transform: translateX(-50%); -webkit-transform: translateX(-50%); -moz-transform: translateX(-50%); -o-transform: tranlslateX(-50%); transform: translateX(-50%);'>";
        $html .= "<span style='font-size: 18;'>ATTENZIONE: verrà sovrascritto il testo precedentemente inserito</span>";
        $html .= "</div>";

        // Hidden
        $html .= "<input type='hidden' name='form_stato' 			value='$form_stato'											>\n";
        $html .= "<input type='hidden' name='stato_principale'  	value='pagelle_principale'									>\n";
        $html .= "<input type='hidden' name='stato_secondario'      value='modifica_tabellone_pagelline_display'				>\n";
        $html .= "<input type='hidden' name='id_materia' 			value='" . $materia['id_materia'] . "'							>\n";
        $html .= "<input type='hidden' name='id_studente'           value=''													>\n";
        $html .= "<input type='hidden' name='indirizzo' 			value='" . $dati_classe['descrizione'] . "'						>\n";
        $html .= "<input type='hidden' name='id_indirizzo'          value='" . $dati_classe['id_indirizzo'] . "'					>\n";
        $html .= "<input type='hidden' name='classe' 				value='" . $dati_classe['classe'] . $dati_classe['sezione'] . "'	>\n";
        $html .= "<input type='hidden' name='id_classe' 			value='" . $dati_classe['id_classe'] . "'						>\n";
        $html .= "<input type='hidden' name='id_classe_interno' 	value=''													>\n";
        $html .= "<input type='hidden' name='periodo'               value='$periodo'											>\n";
        $html .= "<input type='hidden' name='visualizza_assenze' 	value='$visualizza_assenze'									>\n";
        $html .= "<input type='hidden' name='codice_descrizione' 	value='$codice_descrizione'									>\n";
        $html .= "<input type='hidden' name='screen_width'          value='$schermo'											>\n";
        $html .= "<input type='hidden' name='alto_contrasto' 		value='$alto_contrasto'										>\n";
        $html .= "<input type='hidden' name='current_user'          value='$current_user'										>\n";
        $html .= "<input type='hidden' name='current_key'           value='$current_key'										>\n";
        $html .= "<input type='hidden' name='salva' 				value='materia'												>\n";
        $html .= "<input type='hidden' name='torna_a'       		value='pagellina'											>\n";
        $html .= "</table>" . "\n";
        //$html .= "</div>"."\n";


        $html .= '<div style="position: static; width:100%;">' . "\n";
        $html .= '<table width="100%" border=\'1\'>' . "\n"
                . "\t<tr style='background-color:" . $pippo . ";'>\n";

        if ($abilita_pulsante_salva) {
            $html .= "\t\t<td align='center' style='border: none; width: 33%;'>"
                    . "<input type='button' value='Salva e torna al tabellone' onclick=\"this.disabled=true;this.form.submit();\" class='sottotitolo_testo_bold'>\n"
                    . "</td>\n";
            $html .= "\t\t<td align='center' style='border: none; width: 33%;'>"
                . "<input type='button' value='Salva' "
                .   "onclick=\"this.disabled=true; "
                .       " this.parentNode.appendChild(Object.assign(document.createElement('input'),{type:'hidden',name:'torna_a',value:'materia'})); "
                .       " this.form.submit();\" "
                    .   "class='sottotitolo_testo_bold'>\n"
                    . "</td>\n";
        }
        $html .= "</form>\n"
                . "\t\t<td align='center' style='border: none; width: 33%;'>"
                . "<input type='button' value='Annulla' onclick=\"document.forms['form_edit'].submit();\" class='sottotitolo_testo_bold'>\n"
                . "</td>\n"
                . "\t</tr>\n"
                . "</table>\n"
                . "</div>" . "\n";
    }

    return $html;
}
