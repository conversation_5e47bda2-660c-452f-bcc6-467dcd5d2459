<?php

function estrai_info_pagelle_studenti($periodo, $elenco_studenti_richiesti, $cifre_significative, $filtri_tipo_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre tutti i voti di una pagella per un gruppo di studenti completo di calcolo della media">
    $query = "SELECT DISTINCT
					studenti_completi.nome,
					studenti_completi.cognome,
					studenti_completi.id_studente,
					studenti_completi.esonero_religione,
					studenti_completi.esonero_ed_fisica,
					studenti_completi.crediti_terza,
					studenti_completi.crediti_quarta,
					studenti_completi.crediti_quinta,
					studenti_completi.crediti_sospesi_terza,
					studenti_completi.crediti_sospesi_quarta,
					studenti_completi.crediti_reintegrati_terza,
					studenti_completi.crediti_reintegrati_quarta,
					studenti_completi.crediti_finali_agg,
					studenti_completi.voto_ammissione,
					studenti_completi.ammesso_esame_qualifica,
					studenti_completi.curriculum_prima,
					studenti_completi.curriculum_seconda,
					studenti_completi.stage_professionali,
					studenti_completi.voto_qualifica,
					studenti_completi.voto_esame_sc1_qual,
					studenti_completi.voto_esame_sc2_qual,
					studenti_completi.voto_esame_or_qual,
					studenti_completi.ritirato,
					studenti_completi.ammesso_esame_quinta,
					studenti_completi.giudizio_ammissione_quinta,
					studenti_completi.stato_licenza_maestro,
					studenti_completi.giudizio_ammissione_terza,
					studenti_completi.esito_prima_media,
					studenti_completi.esito_seconda_media,
					studenti_completi.esito_terza_media,
					studenti_completi.id_classe,
					studenti_completi.ordinamento AS ordinamento_classe,

					pagelline.id_pagellina,

					voti_pagelline.id_voto_pagellina,
					voti_pagelline.voto_pagellina,
					voti_pagelline.debito,
					voti_pagelline.id_materia,
					voti_pagelline.voto_scritto_pagella,
					voti_pagelline.voto_orale_pagella,
					voti_pagelline.voto_pratico_pagella,
					voti_pagelline.ore_assenza,
					voti_pagelline.tipo_recupero,
					voti_pagelline.esito_recupero,
					voti_pagelline.giudizio_analitico,

					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.in_media_pagelle,
					materie.votazione_differenziata,
					materie.tipo_materia,
                    materie.tipo_voto_personalizzato

				FROM studenti_completi
                INNER JOIN pagelline ON pagelline.id_studente = studenti_completi.id_studente
                INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                INNER JOIN classi_prof_materie ON classi_prof_materie.id_classe = studenti_completi.id_classe
                INNER JOIN materie ON (
                    materie.id_materia = voti_pagelline.id_materia
                    AND materie.id_materia = classi_prof_materie.id_materia
                )
				WHERE (
						pagelline.periodo = '$periodo'
						OR pagelline.periodo = '" . ($periodo + 20) . "'
					)
					AND pagelline.flag_canc = 0
					AND voti_pagelline.flag_canc = 0
					AND materie.flag_canc = 0
				ORDER BY
					studenti_completi.cognome,
					studenti_completi.nome,
					studenti_completi.id_studente,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $array_stud = [];

        for ($cont = 0; $cont < $numero; $cont++) {
            $studente = pg_fetch_assoc($result, $cont);
            $key = array_search($studente['id_studente'], $elenco_studenti_richiesti);

            if ($key !== false) {
                $studente['cognome'] = decode($studente['cognome']);
                $studente['nome'] = decode($studente['nome']);
                $studente['giudizio_ammissione_quinta'] = decode($studente['giudizio_ammissione_quinta']);
                $studente['giudizio_ammissione_terza'] = decode($studente['giudizio_ammissione_terza']);
                $studente['giudizio_analitico'] = decode($studente['giudizio_analitico']);
                $studente['descrizione'] = decode($studente['descrizione']);
                $studente['descrizione_materia_straniera'] = decode($studente['descrizione_materia_straniera']);
                $array_stud[$studente['id_studente']][] = $studente;
            }
        }

        //{{{ <editor-fold defaultstate="collapsed" desc="recupero parametri per calcolo media">
        $scala_voti = estrai_parametri_singoli('SCALA_VOTI');
        $dati_scala_voti = estrai_scala_voti($scala_voti, 'estesa');

        $voto_minimo = $dati_scala_voti['voto_minimo'];
        $voto_massimo = $dati_scala_voti['voto_massimo'];
        $voto_minimo_suff = $dati_scala_voti['voto_minimo_suff'];
        $salto_6 = $dati_scala_voti['salto_6'];
        $salto_7 = $dati_scala_voti['salto_7'];
        $salto_8 = $dati_scala_voti['salto_8'];
        $salto_10 = $dati_scala_voti['salto_10'];
        $arrotondamento = $dati_scala_voti['arrotondamento'];
        $aggiustamento_media = $dati_scala_voti['aggiustamento_media'];
        $aggiustamento_ammissione = $dati_scala_voti['aggiustamento_ammissione'];
        //}}} </editor-fold>

        $array_risultati = [];
        $somma_voti = 0;
        $totale_voti = 0;

        foreach ($array_stud as $key => $studente) {
            if ($studente[0]['id_classe'] > 0) {
                //{{{ <editor-fold defaultstate="collapsed">
                $tipo_visualizzazione_voti = identifica_periodo_tipo_voto($periodo, $studente[0]['id_classe']);

                $materia_inserita = [];
                foreach ($studente as $elemento_pagella) {
                    //fare ciclo completo per calcolo
                    if (
                            (
                            (
                            $elemento_pagella['tipo_materia'] != 'RELIGIONE' && $elemento_pagella['in_media_pagelle'] == 'SI'
                            ) ||
                            (
                            $filtri_tipo_materia['religione'] == 1 && $elemento_pagella['tipo_materia'] == 'RELIGIONE'
                            )
                            ) &&
                            !in_array($elemento_pagella['id_materia'], $materia_inserita)
                    ) {
                        if ($elemento_pagella['tipo_materia'] == 'CONDOTTA' && floatval($elemento_pagella['voto_pagellina']) != 0) {
                            $somma_voti += $elemento_pagella['voto_pagellina'];
                            $totale_voti++;
                        } elseif ($tipo_visualizzazione_voti == "voto_singolo") {
                            if ($elemento_pagella['voto_pagellina'] > $voto_minimo) {
                                $somma_voti += $elemento_pagella['voto_pagellina'];
                                $totale_voti++;
                            }
                        } elseif ($tipo_visualizzazione_voti == "personalizzato") {
                            switch ($elemento_pagella['tipo_voto_personalizzato']) {
                                case 1:
                                    if ($elemento_pagella['voto_pagellina'] > $voto_minimo) {
                                        $somma_voti += $elemento_pagella['voto_pagellina'];
                                        $totale_voti++;
                                    }
                                    break;
                                case 2:
                                    if ($elemento_pagella['voto_scritto_pagella'] != "" && floatval($elemento_pagella['voto_scritto_pagella']) != 0) {
                                        $somma_voti += floatval($elemento_pagella['voto_scritto_pagella']);
                                        $totale_voti++;
                                    }

                                    if ($elemento_pagella['voto_orale_pagella'] != "" && floatval($elemento_pagella['voto_orale_pagella']) != 0) {
                                        $somma_voti += floatval($elemento_pagella['voto_orale_pagella']);
                                        $totale_voti++;
                                    }
                                    break;
                                case 3:
                                    if ($elemento_pagella['voto_scritto_pagella'] != "" && floatval($elemento_pagella['voto_scritto_pagella']) != 0) {
                                        $somma_voti += floatval($elemento_pagella['voto_scritto_pagella']);
                                        $totale_voti++;
                                    }

                                    if ($elemento_pagella['voto_orale_pagella'] != "" && floatval($elemento_pagella['voto_orale_pagella']) != 0) {
                                        $somma_voti += floatval($elemento_pagella['voto_orale_pagella']);
                                        $totale_voti++;
                                    }

                                    if ($elemento_pagella['voto_pratico_pagella'] != "" && floatval($elemento_pagella['voto_pratico_pagella']) != 0) {
                                        $somma_voti += floatval($elemento_pagella['voto_pratico_pagella']);
                                        $totale_voti++;
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            if ($elemento_pagella['voto_scritto_pagella'] != "" && floatval($elemento_pagella['voto_scritto_pagella']) != 0) {
                                $somma_voti += floatval($elemento_pagella['voto_scritto_pagella']);
                                $totale_voti++;
                            }

                            if ($elemento_pagella['voto_orale_pagella'] != "" && floatval($elemento_pagella['voto_orale_pagella']) != 0) {
                                $somma_voti += floatval($elemento_pagella['voto_orale_pagella']);
                                $totale_voti++;
                            }

                            if ($elemento_pagella['voto_pratico_pagella'] != "" && floatval($elemento_pagella['voto_pratico_pagella']) != 0) {
                                $somma_voti += floatval($elemento_pagella['voto_pratico_pagella']);
                                $totale_voti++;
                            }
                        }
                        array_push($materia_inserita, $elemento_pagella['id_materia']);
                    }
                }

                $array_risultati[$key]['dati_calcolati']['somma_voti'] = $somma_voti;
                $array_risultati[$key]['dati_calcolati']['totale_voti'] = $totale_voti;

                $media = round($somma_voti / $totale_voti, $cifre_significative);
                $media_voti = $media;

                if (is_int($somma_voti / $totale_voti)) {
                    switch ($cifre_significative) {
                        case '0':
                            $media_voti = $media;
                            break;
                        case '1':
                            $media_voti = "{$media},0";
                            break;
                        case '2':
                            $media_voti = "{$media},00";
                            break;
                        case '3':
                            $media_voti = "{$media},000";
                            break;
                        case '4':
                            $media_voti = "{$media},0000";
                            break;
                    }
                }

                if ($totale_voti > 0) {
                    $array_risultati[$key]['dati_calcolati']['media_voti'] = $media_voti;
                } else {
                    $array_risultati[$key]['dati_calcolati']['media_voti'] = 'Voti non inseriti';
                }

                $somma_voti = 0;
                $totale_voti = 0;
                //}}} </editor-fold>
            }
        }
    } else {
        $array_risultati = 'NESSUN RISULTATO';
    }

    return $array_risultati;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_istituto_per_excel($periodo, $limiti = []) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di un periodo per tutto l'istituto">
    if (isset($limiti['indirizzi']) && count($limiti['indirizzi'] > 0)){
        $where = " AND id_indirizzo IN (" . implode(", ", $limiti['indirizzi']) . ") ";
    }else{
        $where = "";
    }

    $query_stud = "SELECT
						studenti_completi.id_studente,
						studenti_completi.cognome,
						studenti_completi.nome,
						studenti_completi.sesso,
						studenti_completi.codice_comune_residenza,
						studenti_completi.descrizione_residenza,
						studenti_completi.data_nascita,
						studenti_completi.codice_fiscale,
						studenti_completi.classe,
						studenti_completi.sezione,
						studenti_completi.descrizione_indirizzi,
						materie.id_materia,
						materie.descrizione,
						materie.descrizione_materia_straniera,
						voti_pagelline.voto_pagellina,
						voti_pagelline.voto_scritto_pagella,
						voti_pagelline.voto_orale_pagella,
						voti_pagelline.voto_pratico_pagella
					FROM
						studenti_completi,
						pagelline,
						voti_pagelline,
						classi_prof_materie,
						materie
					WHERE studenti_completi.id_studente = pagelline.id_studente
						AND pagelline.flag_canc = 0
                        AND pagelline.periodo like '%{$periodo}'
						AND voti_pagelline.id_pagellina = pagelline.id_pagellina
						AND voti_pagelline.id_materia = classi_prof_materie.id_materia
						AND voti_pagelline.flag_canc = 0
						AND studenti_completi.id_classe = classi_prof_materie.id_classe
						AND materie.id_materia = voti_pagelline.id_materia
						AND materie.flag_canc = 0
                                                " . $where . "
					ORDER BY
						studenti_completi.cognome,
						studenti_completi.nome,
						studenti_completi.id_studente";

    $result_stud = pgsql_query($query_stud) or die("Invalid $query_stud");
    $numero_stud = pg_num_rows($result_stud);
    if ($numero_stud > 0) {
        for ($cont_stud = 0; $cont_stud < $numero_stud; $cont_stud++) {
            $id_studente = trim(pg_fetch_result($result_stud, $cont_stud, "id_studente"));
            $id_materia = trim(pg_fetch_result($result_stud, $cont_stud, "id_materia"));

            $voti_totali[$id_studente][$id_materia]['cognome'] = trim(pg_fetch_result($result_stud, $cont_stud, "cognome"));
            $voti_totali[$id_studente][$id_materia]['nome'] = trim(pg_fetch_result($result_stud, $cont_stud, "nome"));
            $voti_totali[$id_studente][$id_materia]['classe'] = trim(pg_fetch_result($result_stud, $cont_stud, "classe"));
            $voti_totali[$id_studente][$id_materia]['sezione'] = trim(pg_fetch_result($result_stud, $cont_stud, "sezione"));
            $voti_totali[$id_studente][$id_materia]['descrizione_indirizzo'] = trim(pg_fetch_result($result_stud, $cont_stud, "descrizione_indirizzi"));
            $voti_totali[$id_studente][$id_materia]['sesso'] = trim(pg_fetch_result($result_stud, $cont_stud, "sesso"));
            $voti_totali[$id_studente][$id_materia]['comune'] = pg_fetch_result($result_stud, $cont_stud, "descrizione_residenza");
            $voti_totali[$id_studente][$id_materia]['data_nascita'] = date('d/m/Y', trim(pg_fetch_result($result_stud, $cont_stud, "data_nascita")));
            $voti_totali[$id_studente][$id_materia]['codice_fiscale'] = trim(pg_fetch_result($result_stud, $cont_stud, "codice_fiscale"));
            $voti_totali[$id_studente][$id_materia]['voto_pagellina'] = trim(pg_fetch_result($result_stud, $cont_stud, "voto_pagellina"));
            $voti_totali[$id_studente][$id_materia]['voto_scritto_pagella'] = trim(pg_fetch_result($result_stud, $cont_stud, "voto_scritto_pagella"));
            $voti_totali[$id_studente][$id_materia]['voto_orale_pagella'] = trim(pg_fetch_result($result_stud, $cont_stud, "voto_orale_pagella"));
            $voti_totali[$id_studente][$id_materia]['voto_pratico_pagella'] = trim(pg_fetch_result($result_stud, $cont_stud, "voto_pratico_pagella"));
        }
    }

    return $voti_totali;
    //}}} </editor-fold>
}

function estrai_dati_studente_calcola_media_esito($id_studente, $periodo, $anno_fine) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per calcolare media e esito studente">
    $query = "SELECT * FROM studenti_completi
				WHERE id_studente = " . $id_studente;

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $dati_studente = pg_fetch_assoc($result, $cont);
        }
    }

    //esiti possibili
    //1=ammesso alla classe successiva
    //2=non ammesso alla classe successiva
    //3=ritirato prima del 15/03
    //4=trasferito prima del 15/03
    //5=ammesso all'esame di terza media
    //6=non ammesso all'esame di terza media
    //
	//#########################################
    //tipi di indirizzo  contemplati
    //0=Indirizzo standard
    //1=Liceo Classico
    //2=Istituto Professionale
    //3=Istituto Arte
    //4=Scuola Media
    //5=Accademia d'arte / Liceo breve
    //6=Scuola elementare
    //7=Scuola infanzia

    switch ($dati_studente['tipo_indirizzo']) {
        //{{{ <editor-fold defaultstate="collapsed" desc="situazioni specifiche">
        case "0":
            //{{{ <editor-fold defaultstate="collapsed" desc="Indirizzo standard">
            //}}} </editor-fold>
            break;
        case "1":
            //{{{ <editor-fold defaultstate="collapsed" desc="Liceo Classico">
            //}}} </editor-fold>
            break;
        case "2":
            //{{{ <editor-fold defaultstate="collapsed" desc="Istituto Professionale">
            //}}} </editor-fold>
            break;
        case "3":
            //{{{ <editor-fold defaultstate="collapsed" desc="Istituto Arte">
            //}}} </editor-fold>
            break;
        case "4":
            // {{{ <editor-fold defaultstate="collapsed" desc="Scuola Media">
            //esito
            if (($dati_studente['ritirato'] == '1') and ( $dati_studente['data_ritiro'] <= mktime(0, 0, 0, 3, 15, $anno_fine))) {
                $dati_studente['esito_anno'] = 3;
            } elseif (($dati_studente['ritirato'] == '2') and ( $dati_studente['data_ritiro'] <= mktime(0, 0, 0, 3, 15, $anno_fine))) {
                $dati_studente['esito_anno'] = 4;
            } elseif ($dati_studente['classe'] == '1') {
                if ($dati_studente['esito_prima_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 1;
                } else {
                    $dati_studente['esito_anno'] = 2;
                }
            } elseif ($dati_studente['classe'] == '2') {
                if ($dati_studente['esito_seconda_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 1;
                } else {
                    $dati_studente['esito_anno'] = 2;
                }
            } elseif ($dati_studente['classe'] == '3') {
                if ($dati_studente['esito_terza_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 6;
                } else {
                    $dati_studente['esito_anno'] = 5;
                }
            }
            //}}} </editor-fold>
            break;
        case "5":
            //{{{ <editor-fold defaultstate="collapsed" desc="Accademia d'arte / Liceo Breve">
            //}}} </editor-fold>
            break;
        case "6":
            //{{{ <editor-fold defaultstate="collapsed" desc="Scuola elementare">
            //}}} </editor-fold>
            break;
        case "7":
            //{{{ <editor-fold defaultstate="collapsed" desc="Scuola infanzia">
            //}}} </editor-fold>
            break;
        default:
            break;
        //}}} </editor-fold>
    }

    return $dati_studente;
    //}}} </editor-fold>
}

function calcola_media_esito_studente($id_studente, $periodo, $anno_fine) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per calcolare media e esito studente">
    $dati_studente = estrai_dati_studente_completi($id_studente);

    //esiti possibili
    //1=ammesso alla classe successiva
    //2=non ammesso alla classe successiva
    //3=ritirato prima del 15/03
    //4=trasferito prima del 15/03
    //5=ammesso all'esame di terza media
    //6=non ammesso all'esame di terza media
    //
	//#########################################
    //tipi di indirizzo  contemplati
    //0=Indirizzo standard
    //1=Liceo Classico
    //2=Istituto Professionale
    //3=Istituto Arte
    //4=Scuola Media
    //5=Accademia d'arte / Liceo Breve
    //6=Scuola elementare
    //7=Scuola infanzia

    switch ($dati_studente['tipo_indirizzo']) {
        //{{{ <editor-fold defaultstate="collapsed" desc="situazioni specifiche">
        case "0":
            //{{{ <editor-fold defaultstate="collapsed" desc="Indirizzo standard">
            //}}} </editor-fold>
            break;
        case "1":
            //{{{ <editor-fold defaultstate="collapsed" desc="Liceo Classico">
            //}}} </editor-fold>
            break;
        case "2":
            //{{{ <editor-fold defaultstate="collapsed" desc="Istituto Professionale">
            //}}} </editor-fold>
            break;
        case "3":
            //{{{ <editor-fold defaultstate="collapsed" desc="Istituto Arte">
            //}}} </editor-fold>
            break;
        case "4":
            // {{{ <editor-fold defaultstate="collapsed" desc="Scuola Media">
            //esito
            if (($dati_studente['ritirato'] == '1') and ( $dati_studente['data_ritiro'] <= mktime(0, 0, 0, 3, 15, $anno_fine))) {
                $dati_studente['esito_anno'] = 3;
            } elseif (($dati_studente['ritirato'] == '2') and ( $dati_studente['data_ritiro'] <= mktime(0, 0, 0, 3, 15, $anno_fine))) {
                $dati_studente['esito_anno'] = 4;
            } elseif ($dati_studente['classe'] == '1') {
                if ($dati_studente['esito_prima_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 1;
                } else {
                    $dati_studente['esito_anno'] = 2;
                }
            } elseif ($dati_studente['classe'] == '2') {
                if ($dati_studente['esito_seconda_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 1;
                } else {
                    $dati_studente['esito_anno'] = 2;
                }
            } elseif ($dati_studente['classe'] == '3') {
                if ($dati_studente['esito_terza_media'] == 'SI') {
                    $dati_studente['esito_anno'] = 6;
                } else {
                    $dati_studente['esito_anno'] = 5;
                }
            }
            //}}} </editor-fold>
            break;
        case "5":
            //{{{ <editor-fold defaultstate="collapsed" desc="Accademia d'arte / Liceo breve">
            //}}} </editor-fold>
            break;
        case "6":
            //{{{ <editor-fold defaultstate="collapsed" desc="Scuola elementare">
            //}}} </editor-fold>
            break;
        case "7":
            //{{{ <editor-fold defaultstate="collapsed" desc="Scuola infanzia">
            //}}} </editor-fold>
            break;
        default:
            break;
        //}}} </editor-fold>
    }
    return $dati_studente;
    //}}} </editor-fold>
}

function estrai_tipi_recupero() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i tipi di recupero della scuola">
    $query = "SELECT * FROM tipi_recupero WHERE	flag_canc = 0";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $tipi_recupero = pg_fetch_assoc($result, $cont);
            $mat_recupero[$tipi_recupero['id_tipo_recupero']] = [
                'nome' => decode($tipi_recupero['descrizione']),
                'valore' => decode($tipi_recupero['codice']),
                'recupero_predefinito' => decode($tipi_recupero['recupero_predefinito'])
            ];
        }
    }

    return $mat_recupero;
    //}}} </editor-fold>
}

function estrai_tipo_recupero_singolo($codice) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre un tipo di recupero della scuola">
    $query = "SELECT descrizione FROM tipi_recupero
                    WHERE codice = '" . encode($codice) . "'
                        AND flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $mat_recupero = pg_fetch_assoc($result, 0);
        $tipo_recupero = decode($mat_recupero['descrizione']);
    }

    return $tipo_recupero;
    //}}} </editor-fold>
}

function salva_tipi_recupero($mat_tipi, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i tipi di recupero della scuola">
    $query = "UPDATE tipi_recupero
				SET flag_canc = " . time() . "
				WHERE flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");

    foreach ($mat_tipi as $key => $value) {
        if (strlen(trim($value['codice'])) > 0 and strlen(trim($value['descrizione'])) > 0) {
            $query = "SELECT * FROM tipi_recupero
						WHERE id_tipo_recupero = " . $key;

            $result = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($result);

            if ($numero > 0) {
                $mat_recupero = pg_fetch_assoc($result, 0);
                $query = "UPDATE tipi_recupero
							SET codice = '" . encode($value['codice']) . "',
								descrizione = '" . encode($value['descrizione']) . "',
                                recupero_predefinito = " . $value['recupero_predefinito'] . ",
								flag_canc = 0
							WHERE id_tipo_recupero = " . $key;

                $result = pgsql_query($query) or die("Invalid $query");

                $mat_oggetti = [];
                $mat_oggetti["id_tipo_recupero"] = $key;
                $tipo_sorgente = "INTERFACCIA";
                $operazione = "MODIFICA";
                inserisci_log($mat_oggetti, "tipi_recupero", $current_user, $tipo_sorgente, $operazione);

                $query = "UPDATE voti_pagelline
							SET tipo_recupero = '" . encode($value['codice']) . "'
							WHERE tipo_recupero = '" . $mat_recupero['codice'] . "'";

                $result = pgsql_query($query) or die("Invalid $query");

                $mat_oggetti = [];
                $mat_oggetti["tipo_recupero"] = encode($value['codice']);
                $tipo_sorgente = "INTERFACCIA";
                $operazione = "MODIFICA";
                inserisci_log($mat_oggetti, "voti_pagelline", $current_user, $tipo_sorgente, $operazione);
            } else {
                $query = "INSERT INTO tipi_recupero (
								id_tipo_recupero,
								codice,
								descrizione
							) VALUES (
								" . $key . ",
								'" . encode($value['codice']) . "',
								'" . encode($value['descrizione']) . "'
							)";

                $result = pgsql_query($query) or die("Invalid $query");

                $mat_oggetti = [];
                $mat_oggetti["id_tipo_recupero"] = $key;
                $tipo_sorgente = "INTERFACCIA";
                $operazione = "INSERIMENTO";
                inserisci_log($mat_oggetti, "tipi_recupero", $current_user, $tipo_sorgente, $operazione);
            }
        }
    }

    return $mat_recupero;
    //}}} </editor-fold>
}

function estrai_chiusura_scrutini($id_classe) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre lo stato delle chiusure da una classe">
    $query = "SELECT * FROM classi
				WHERE id_classe = '" . $id_classe . "'
					AND flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $classe = pg_fetch_assoc($result, 0);
    }

    $chiusura = explode("@", $classe["blocco_scrutini"]);

    $array_chiusura_finale = [];

    for ($cont = 1; $cont <= 11; $cont++) {
        $array_chiusura_finale[$cont] = 'NO';
    }

    for ($cont = 21; $cont < 30; $cont++) {
        $array_chiusura_finale[$cont] = 'NO';
    }

    if (count($chiusura) > 1) {
        foreach ($chiusura as $singola_chiusura) {
            $temp_array = [];
            $temp_array = explode("#", $singola_chiusura);
            if (array_key_exists(intval($temp_array[0]), $array_chiusura_finale) and strlen($temp_array[1]) == 2) {
                $array_chiusura_finale[intval($temp_array[0])] = $temp_array[1];
            }
        }
    }
    return $array_chiusura_finale;
    //}}} </editor-fold>
}

function aggiorna_chiusura_scrutini($id_classe, $stringa, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per aggiornare lo stato delle chiusure da una classe">
    $query = "UPDATE classi
				SET blocco_scrutini='" . $stringa . "'
				WHERE id_classe='" . $id_classe . "'";

    pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_classe"] = $id_classe;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "MODIFICA";
    inserisci_log($mat_oggetti, "classi", $current_user, $tipo_sorgente, $operazione);

    $query = "UPDATE voti_pagelline
				SET chi_modifica = " . $current_user . ",
					data_modifica = " . time() . "
				WHERE flag_canc = 0
					AND id_pagellina IN (
						SELECT id_pagellina
						FROM pagelline
						WHERE flag_canc = 0
							AND id_studente IN (
								SELECT DISTINCT id_studente
								FROM studenti_completi
								WHERE id_classe = " . $id_classe . "
							)
					)";

    $result = pgsql_query($query) or die("Invalid $query");

    return $result;
    //}}} </editor-fold>
}

function inserisci_debito($id_materia, $descrizione_materia, $id_studente, $tipo_debito, $recupero_debito, $anno_scolastico, $current_user, $tipo_carenza = '') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire un nuovo debito nell'elenco">
    $nome_materia = encode($descrizione_materia);

    $query = "INSERT INTO debiti (
					id_materia,
					descrizione_materia,
					id_studente,
					tipo_debito,
					debito_recuperato,
					tipo_carenza,
					anno_scolastico
				) VALUES (
					$id_materia,
					'$nome_materia',
					$id_studente,
					'$tipo_debito',
					'$recupero_debito',
					'$tipo_carenza',
					'$anno_scolastico'
				)";

    $result = pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["descrizione_materia"] = $nome_materia;
    $mat_oggetti["id_studente"] = $id_studente;
    $mat_oggetti["anno_scolastico"] = $anno_scolastico;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "INSERIMENTO";
    inserisci_log($mat_oggetti, "debiti", $current_user, $tipo_sorgente, $operazione);

    return $result;
    //}}} </editor-fold>
}

function modifica_debito($id_debito_selezionato, $recupero_debito, $tipo_appello, $data_selezionata, $current_user, $tipo_carenza) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare un debito nell'elenco">
    $query = "UPDATE debiti
				SET debito_recuperato='$recupero_debito',
					data_recupero_debito='$data_selezionata',
					tipo_appello='$tipo_appello',
                    tipo_carenza='$tipo_carenza'
				WHERE id_debito='$id_debito_selezionato'";

    $result = pgsql_query($query) or die("Invalid $query");
    echo
    $mat_oggetti["id_debito"] = $id_debito_selezionato;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "MODIFICA";
    inserisci_log($mat_oggetti, "debiti", $current_user, $tipo_sorgente, $operazione);

    return $result;
    //}}} </editor-fold>
}

function elimina_debito_specifico($id_materia, $descrizione, $id_studente, $anno_scolastico, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare un debito specifico">
    $query = "UPDATE debiti
				SET
					flag_canc=" . time() . "
				WHERE
					id_materia='" . $id_materia . "'
					AND
					descrizione_materia='" . $descrizione . "'
					AND
					id_studente='" . $id_studente . "'
					AND
					anno_scolastico='" . $anno_scolastico . "'";

    $result = pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_materia"] = $id_materia;
    $mat_oggetti["descrizione_materia"] = $descrizione;
    $mat_oggetti["id_studente"] = $id_studente;
    $mat_oggetti["anno_scolastico"] = $anno_scolastico;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "ELIMINAZIONE";
    inserisci_log($mat_oggetti, "debiti", $current_user, $tipo_sorgente, $operazione);

    return $result;
    //}}} </editor-fold>
}

function elimina_debito($id_debito, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare un debito">
    $query = "UPDATE debiti
				SET flag_canc=" . time() . "
				WHERE id_debito='$id_debito'";

    $result = pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_debito"] = $id_debito;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "ELIMINAZIONE";
    inserisci_log($mat_oggetti, "debiti", $current_user, $tipo_sorgente, $operazione);

    return $result;
    //}}} </editor-fold>
}

function estrai_debiti_studente($id_studente, $anno_scolastico = "tutti") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i debiti di una classe">
    if ($anno_scolastico == "tutti") {
        $query_debiti = "SELECT * FROM debiti
                         WHERE id_studente = '" . $id_studente . "'
                            AND flag_canc = 0";
    } else {
        $query_debiti = "SELECT * FROM debiti
                         WHERE id_studente = '" . $id_studente . "'
                            AND flag_canc = 0
                            AND anno_scolastico = '" . $anno_scolastico . "'";
    }
    $query_debiti .= " ORDER BY anno_scolastico, descrizione_materia, data_recupero_debito";

    $result_debiti = pgsql_query($query_debiti) or die("Invalid $query_debiti");
    $numero_debiti = pg_num_rows($result_debiti);

    if ($numero_debiti > 0) {
        for ($cont_debiti = 0; $cont_debiti < $numero_debiti; $cont_debiti++) {
            $debiti[$cont_debiti] = pg_fetch_assoc($result_debiti, $cont_debiti);
        }
    }

    return $debiti;
    //}}} </editor-fold>
}

function verifica_presenza_debito($id_studente, $id_materia, $anno_scolastico = false, $escludi_recuperati = 'NO') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per verificare la presenta di un debito (trentino) per studente, materia ed anno scolastico">
    if (!$anno_scolastico) {
        $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    }

    $filtro_recuperati = ($escludi_recuperati == "SI") ? " AND debito_recuperato != 'SI'" : "";

    $query_debiti = "SELECT * FROM debiti
                     WHERE id_studente = {$id_studente}
                        AND id_materia = {$id_materia}
                        AND flag_canc = 0
                        AND anno_scolastico = '{$anno_scolastico}'
                        {$filtro_recuperati}";

    $result_debiti = pgsql_query($query_debiti) or die("Invalid $query_debiti");
    $numero_debiti = pg_num_rows($result_debiti);

    if ($numero_debiti > 0) {
        return true;
    } else {
        return false;
    }
    //}}} </editor-fold>
}

function estrai_elenco_studenti_con_debiti($anno_scolastico = "tutti") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i debiti di una classe">
    if ($anno_scolastico == "tutti") {
        $query_debiti = "SELECT
								distinct(studenti.id_studente),
								studenti.nome,
								studenti.cognome
                        FROM studenti
                        INNER JOIN debiti ON studenti.id_studente = debiti.id_studente
                        WHERE debiti.flag_canc = 0
                            AND studenti.flag_canc = 0
							ORDER BY
								studenti.cognome,
								studenti.nome";
    } else {
        $query_debiti = "SELECT
								distinct(studenti.id_studente),
								studenti.nome,
								studenti.cognome
							FROM studenti
                            INNER JOIN debiti ON studenti.id_studente = debiti.id_studente
							WHERE debiti.flag_canc = 0
								AND studenti.flag_canc = 0
								AND debiti.anno_scolastico = '" . $anno_scolastico . "'
							ORDER BY
								studenti.cognome,
								studenti.nome";
    }
    $result_debiti = pgsql_query($query_debiti) or die("Invalid $query_debiti");
    $numero_debiti = pg_num_rows($result_debiti);

    if ($numero_debiti > 0) {
        for ($cont_debiti = 0; $cont_debiti < $numero_debiti; $cont_debiti++) {
            $debiti[$cont_debiti] = pg_fetch_assoc($result_debiti, $cont_debiti);
            foreach ($debiti[$cont_debiti] as $key => $value) {
                $debiti[$cont_debiti] = decode($value);
            }
        }
    }
    return $debiti;
    //}}} </editor-fold>
}

function estrai_debiti_classe_materia($id_classe, $id_materia, $anno_scolastico = "tutti") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i debiti di una classe in una materia">
    if ($anno_scolastico == "tutti") {
        $query_stud = "SELECT
							studenti.*,
							classi_studenti.registro,
							debiti.tipo_debito,
							debiti.anno_scolastico
						FROM studenti
							INNER JOIN classi_studenti ON classi_studenti.id_studente=studenti.id_studente
							INNER JOIN debiti ON debiti.id_studente=studenti.id_studente
						WHERE
							classi_studenti.id_classe = '$id_classe'
							AND classi_studenti.flag_canc = 0
							AND studenti.flag_canc = 0
							AND debiti.id_materia = " . $id_materia . "
							AND debiti.flag_canc = 0
						ORDER BY
							classi_studenti.registro,
							studenti.id_studente";
    } else {
        $query_stud = "SELECT
							studenti.*,
							classi_studenti.registro,
							debiti.tipo_debito,
							debiti.anno_scolastico
						FROM studenti
							INNER JOIN classi_studenti ON classi_studenti.id_studente = studenti.id_studente
							INNER JOIN debiti ON debiti.id_studente = studenti.id_studente
						WHERE classi_studenti.id_classe='$id_classe'
							AND classi_studenti.flag_canc=0
							AND studenti.flag_canc=0
							AND debiti.id_materia=" . $id_materia . "
							AND debiti.anno_scolastico='" . $anno_scolastico . "'
							AND debiti.flag_canc=0
						ORDER BY
							classi_studenti.registro,
							studenti.id_studente";
    }
    $result_stud = pgsql_query($query_stud) or die("Invalid $query_stud");
    $numero_stud = pg_num_rows($result_stud);
    if ($numero_stud > 0) {
        for ($cont_stud = 0; $cont_stud < $numero_stud; $cont_stud++) {
            $debiti[$cont_stud] = pg_fetch_assoc($result_stud, $cont_stud);
            foreach ($debiti[$cont_stud] as $key => $value) {
                $debiti[$cont_stud] = decode($value);
            }
        }
    }
    return $debiti;
    //}}} </editor-fold>
}

function estrai_debiti_classe($id_classe, $anno_scolastico = "tutti") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i debiti di una classe">
    $query_stud = "SELECT
						studenti.*,
						classi_studenti.registro
					FROM studenti
						INNER JOIN classi_studenti ON classi_studenti.id_studente=studenti.id_studente
					WHERE classi_studenti.id_classe='$id_classe'
						AND classi_studenti.flag_canc=0
						AND studenti.flag_canc=0
					ORDER BY
						classi_studenti.registro,
						studenti.id_studente";

    $result_stud = pgsql_query($query_stud) or die("Invalid $query_stud");
    $numero_stud = pg_num_rows($result_stud);

    if ($numero_stud > 0) {
        for ($cont_stud = 0; $cont_stud < $numero_stud; $cont_stud++) {

            $debiti[$cont_stud]["id_studente"] = trim(pg_fetch_result($result_stud, $cont_stud, "id_studente"));
            $debiti[$cont_stud]["registro"] = trim(pg_fetch_result($result_stud, $cont_stud, "registro"));
            $debiti[$cont_stud]["nome"] = decode(trim(pg_fetch_result($result_stud, $cont_stud, "nome")));
            $debiti[$cont_stud]["cognome"] = decode(trim(pg_fetch_result($result_stud, $cont_stud, "cognome")));
            $debiti[$cont_stud]["id_classe"] = $id_classe;

            if ($anno_scolastico == "tutti") {
                $query_debiti = "SELECT	* FROM debiti
								 WHERE id_studente = '" . $debiti[$cont_stud]["id_studente"] . "'
                                    AND flag_canc = 0
								 ORDER BY anno_scolastico, descrizione_materia";
            } else {
                $query_debiti = "SELECT	* FROM debiti
								 WHERE id_studente = '" . $debiti[$cont_stud]["id_studente"] . "'
                                    AND anno_scolastico = '" . $anno_scolastico . "'
									AND flag_canc = 0
								 ORDER BY anno_scolastico, descrizione_materia";
            }
            $result_debiti = pgsql_query($query_debiti) or die("Invalid $query_debiti");
            $numero_debiti = pg_num_rows($result_debiti);

            if ($numero_debiti > 0) {
                for ($cont_debiti = 0; $cont_debiti < $numero_debiti; $cont_debiti++) {
                    $debiti[$cont_stud]["debiti"][$cont_debiti] = pg_fetch_assoc($result_debiti, $cont_debiti);
                }
            }
        }
    }

    return $debiti;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_classe($id_classe, $periodo, $id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di una singola materia">
    $query_stud = "SELECT
						classi_studenti.registro,
						studenti.nome,
						studenti.cognome,
						studenti.id_studente
					FROM studenti
                    INNER JOIN classi_studenti ON classi_studenti.id_studente = studenti.id_studente
					WHERE classi_studenti.id_classe = '$id_classe'
						AND classi_studenti.flag_canc = 0
						AND studenti.flag_canc = 0
					ORDER BY
						classi_studenti.registro,
						studenti.id_studente";

    $result_stud = pgsql_query($query_stud) or die("Invalid $query_stud");
    $numero_stud = pg_num_rows($result_stud);

    if ($numero_stud > 0) {
        for ($cont_stud = 0; $cont_stud < $numero_stud; $cont_stud++) {
            $voti_pagelline[$cont_stud][0] = trim(pg_fetch_result($result_stud, $cont_stud, "id_studente"));
            $voti_pagelline[$cont_stud][1] = trim(pg_fetch_result($result_stud, $cont_stud, "registro"));
            $voti_pagelline[$cont_stud][2] = decode(trim(pg_fetch_result($result_stud, $cont_stud, "nome")));
            $voti_pagelline[$cont_stud][3] = decode(trim(pg_fetch_result($result_stud, $cont_stud, "cognome")));
            $voti_pagelline[$cont_stud][13] = $id_classe;
            $voti_pagelline[$cont_stud][14] = $periodo;

            $query_pag = "SELECT id_pagellina FROM pagelline
						  WHERE id_studente = '" . $voti_pagelline[$cont_stud][0] . "'
                            AND periodo = '$periodo'
                            AND flag_canc = 0";

            $result_pag = pgsql_query($query_pag) or die("Invalid $query_pag");
            $numero_pag = pg_num_rows($result_pag);

            if ($numero_pag > 0) {
                $voti_pagelline[$cont_stud][4] = trim(pg_fetch_result($result_pag, 0, "id_pagellina"));

                $query_int = "SELECT
									voti_pagelline.id_voto_pagellina,
									voti_pagelline.voto_pagellina,
									voti_pagelline.debito,
									voti_pagelline.id_materia,
									voti_pagelline.data,
									voti_pagelline.ore_assenza,
									voti_pagelline.giudizio_analitico,
									significati_voti.valore
								FROM voti_pagelline
                                INNER JOIN significati_voti ON voti_pagelline.voto_pagellina = significati_voti.voto
								WHERE voti_pagelline.id_pagellina = '" . $voti_pagelline[$cont_stud][4] . "'
									AND voti_pagelline.id_materia = '$id_materia'
									AND voti_pagelline.flag_canc = 0";

                $result_int = pgsql_query($query_int) or die("Invalid $query_int");
                $numero_int = pg_num_rows($result_int);

                if ($numero_int > 0) {
                    $voti_pagelline[$cont_stud][5] = trim(pg_fetch_result($result_int, 0, "id_voto_pagellina"));
                    $voti_pagelline[$cont_stud][6] = trim(pg_fetch_result($result_int, 0, "voto_pagellina"));
                    $voti_pagelline[$cont_stud][7] = trim(pg_fetch_result($result_int, 0, "debito"));
                    $voti_pagelline[$cont_stud][8] = trim(pg_fetch_result($result_int, 0, "id_materia"));
                    $voti_pagelline[$cont_stud][10] = trim(pg_fetch_result($result_int, 0, "valore"));
                    $voti_pagelline[$cont_stud][11] = trim(pg_fetch_result($result_int, 0, "data"));
                    $voti_pagelline[$cont_stud][12] = trim(pg_fetch_result($result_int, 0, "ore_assenza"));
                    $voti_pagelline[$cont_stud]['giudizio_analitico'] = decode(trim(pg_fetch_result($result_int, 0, "giudizio_analitico")));
                } else {
                    $voti_pagelline[$cont_stud][5] = "INESISTENTE";
                }
            } else {
                $voti_pagelline[$cont_stud][4] = "INESISTENTE";
                $voti_pagelline[$cont_stud][5] = "INESISTENTE";
            }
        }
    }

    return $voti_pagelline;
    //}}} </editor-fold>
}

function estrai_voti_tabellone_pagellina_materia($id_classe, $periodo, $array_studenti, $id_materia) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di tutte le materie">
    $campi_liberi = estrai_codici_campi_liberi_con_valori($periodo);
    $studenti = estrai_studenti_classe($id_classe);

    foreach ($studenti as $indice => $studente) {
        $studenti_corretto[$studente["id_studente"]] = $studenti[$indice];
    }

    $query = "SELECT
					classi_studenti.registro,

					studenti.nome,
					studenti.cognome,
					studenti.id_studente,
					studenti.esonero_religione,
					studenti.esonero_ed_fisica,

					pagelline.id_pagellina,

					voti_pagelline.id_voto_pagellina,
					voti_pagelline.voto_pagellina,
					voti_pagelline.debito,
					voti_pagelline.id_materia,
					voti_pagelline.voto_scritto_pagella,
					voti_pagelline.voto_orale_pagella,
					voti_pagelline.voto_pratico_pagella,
					voti_pagelline.ore_assenza,
					voti_pagelline.tipo_recupero,
					voti_pagelline.esito_recupero,
					voti_pagelline.giudizio_analitico,

					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.tipo_voto_personalizzato,
					materie.in_media_pagelle,
					materie.votazione_differenziata
				FROM
					studenti
					INNER JOIN classi_studenti ON classi_studenti.id_studente=studenti.id_studente
					INNER JOIN pagelline ON pagelline.id_studente=studenti.id_studente
					INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina=pagelline.id_pagellina
					INNER JOIN materie ON materie.id_materia=voti_pagelline.id_materia
				WHERE
					classi_studenti.id_classe='$id_classe'
					AND
					pagelline.periodo = '$periodo'
					AND
					voti_pagelline.id_materia = '$id_materia'
					AND
					classi_studenti.flag_canc=0
					AND
					studenti.flag_canc=0
					AND
					voti_pagelline.flag_canc=0
					AND
					materie.flag_canc=0
					AND
					pagelline.flag_canc=0
				ORDER BY
					classi_studenti.registro,
					studenti.cognome,
					studenti.nome,
					studenti.id_studente";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_voto_pagellina = trim(pg_fetch_result($result, $cont, "id_voto_pagellina"));
            $id_studente = trim(pg_fetch_result($result, $cont, "id_studente"));
            $studenti_corretto[$id_studente]["id_studente"] = $id_studente;
            $studenti_corretto[$id_studente]["id_pagellina"] = trim(pg_fetch_result($result, $cont, "id_pagellina"));
            $studenti_corretto[$id_studente]["id_voto_pagellina"] = $id_voto_pagellina;
            $studenti_corretto[$id_studente]["voto_pagellina"] = trim(pg_fetch_result($result, $cont, "voto_pagellina"));
            $studenti_corretto[$id_studente]["voto_scritto_pagella"] = trim(pg_fetch_result($result, $cont, "voto_scritto_pagella"));
            $studenti_corretto[$id_studente]["voto_orale_pagella"] = trim(pg_fetch_result($result, $cont, "voto_orale_pagella"));
            $studenti_corretto[$id_studente]["voto_pratico_pagella"] = trim(pg_fetch_result($result, $cont, "voto_pratico_pagella"));
            $studenti_corretto[$id_studente]["ore_assenza"] = trim(pg_fetch_result($result, $cont, "ore_assenza"));
            $studenti_corretto[$id_studente]["scritto"] = trim(pg_fetch_result($result, $cont, "scritto"));
            $studenti_corretto[$id_studente]["orale"] = trim(pg_fetch_result($result, $cont, "orale"));
            $studenti_corretto[$id_studente]["pratico"] = trim(pg_fetch_result($result, $cont, "pratico"));
            $studenti_corretto[$id_studente]["tipo_recupero"] = trim(pg_fetch_result($result, $cont, "tipo_recupero"));
            $studenti_corretto[$id_studente]["esito_recupero"] = trim(pg_fetch_result($result, $cont, "esito_recupero"));
            $studenti_corretto[$id_studente]["giudizio_analitico"] = decode(trim(pg_fetch_result($result, $cont, "giudizio_analitico")));
            $studenti_corretto[$id_studente]["tipo_valutazione"] = trim(pg_fetch_result($result, $cont, "tipo_valutazione"));
            $studenti_corretto[$id_studente]["tipo_voto_personalizzato"] = trim(pg_fetch_result($result, $cont, "tipo_voto_personalizzato"));

            $elenco_valori = estrai_valori_campi_liberi_materia_studente_per_stampa($id_voto_pagellina);

            $campi_liberi_temp = $campi_liberi;

            if (is_array($elenco_valori)) {
                foreach ($elenco_valori as $valore) {
                    if ($valore["id_valore_precomp"] != "-1") {
                        if (strlen($valore["id_valore_precomp"]) > 0) {
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"][$valore["id_valore_precomp"]]["selezionato"] = "SI";
                        }
                    } else {
                        $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["id_valore_precomp"] = "-1";
                        $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["selezionato"] = "SI";
                        $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["valore_testuale"] = $valore["valore_testuale"];
                        $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["id_valore_precomp"] = "meno_uno";
                        $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["valore_testuale"] = $valore["valore_testuale"];
                    }
                }
            }
            $studenti_corretto[$id_studente]["campi_liberi"] = $campi_liberi_temp;
        }
    }

    return $studenti_corretto;
    //}}} </editor-fold>
}

function estrai_voti_tabellone_pagellina_classe_rivisto($id_classe, $periodo, $array_studenti, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di tutte le materie">
    $query = "SELECT
					classi_studenti.registro,

					studenti.nome,
					studenti.cognome,
					studenti.id_studente,
					studenti.esonero_religione,
					studenti.esonero_ed_fisica,
					studenti.crediti_terza,
					studenti.crediti_quarta,
					studenti.crediti_quinta,
					studenti.crediti_sospesi_terza,
					studenti.crediti_sospesi_quarta,
					studenti.crediti_reintegrati_terza,
					studenti.crediti_reintegrati_quarta,
					studenti.crediti_finali_agg,
					studenti.voto_ammissione,
					studenti.ammesso_esame_qualifica,
					studenti.curriculum_prima,
					studenti.curriculum_seconda,
					studenti.stage_professionali,
					studenti.voto_qualifica,
					studenti.voto_esame_sc1_qual,
					studenti.voto_esame_sc2_qual,
					studenti.voto_esame_or_qual,
					studenti.ritirato,
					studenti.ammesso_esame_quinta,
					studenti.giudizio_ammissione_quinta,
					studenti.stato_licenza_maestro,
					studenti.giudizio_ammissione_terza,
					studenti.esito_prima_media,
					studenti.esito_seconda_media,
					studenti.esito_terza_media,

					pagelline.id_pagellina,

					voti_pagelline.id_voto_pagellina,
					voti_pagelline.voto_pagellina,
					voti_pagelline.debito,
					voti_pagelline.id_materia,
					voti_pagelline.voto_scritto_pagella,
					voti_pagelline.voto_orale_pagella,
					voti_pagelline.voto_pratico_pagella,
					voti_pagelline.ore_assenza,
					voti_pagelline.tipo_recupero,
					voti_pagelline.esito_recupero,
					voti_pagelline.giudizio_analitico,

					materie.scritto,
					materie.orale,
					materie.pratico,
					materie.codice,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					materie.ordinamento,
					materie.tipo_valutazione,
					materie.in_media_pagelle,
					materie.votazione_differenziata,
                    materie.tipo_voto_personalizzato,
                    materie.nome_materia_sito,
                    materie.nome_materia_breve
				FROM studenti
					INNER JOIN classi_studenti ON classi_studenti.id_studente=studenti.id_studente
					INNER JOIN pagelline ON pagelline.id_studente=studenti.id_studente
					INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina=pagelline.id_pagellina
					INNER JOIN materie ON materie.id_materia=voti_pagelline.id_materia
				WHERE classi_studenti.id_classe='$id_classe'
					AND pagelline.periodo = '$periodo'
					AND classi_studenti.flag_canc=0
					AND studenti.flag_canc=0
					AND voti_pagelline.flag_canc=0
					AND materie.flag_canc=0
					AND materie.in_media_pagelle <> 'NV'
					AND pagelline.flag_canc=0
				ORDER BY
					classi_studenti.registro,
					studenti.cognome,
					studenti.nome,
					studenti.id_studente,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $id_studente_new = "-1";
        $id_studente_old = "-1";
        $cont_stud = -1;
        $studente_valido = "SI";
        for ($cont = 0; $cont < $numero; $cont++) {
            $id_studente_new = trim(pg_fetch_result($result, $cont, "id_studente"));

            //controllo se appartiene agli studenti selezionati, altrimenti non lo considero
            if (count($array_studenti) > 0) {
                $studente_valido = "NO";
                for ($cont_array_studenti = 0; $cont_array_studenti < count($array_studenti); $cont_array_studenti++) {
                    if ($id_studente_new == $array_studenti[$cont_array_studenti][0]) {
                        $studente_valido = "SI";
                    }
                }
            }

            if ($studente_valido == "SI") {
                if ($id_studente_new != $id_studente_old) {
                    $cont_stud = $cont_stud + 1;
                    $cont_pag = 0;
                    $id_studente_old = $id_studente_new;
                }

                $id_studente_interno = trim(pg_fetch_result($result, $cont, "id_studente"));
                $voti_pagelline[$id_studente_interno][0][0] = trim(pg_fetch_result($result, $cont, "id_studente"));
                $voti_pagelline[$id_studente_interno][0][1] = trim(pg_fetch_result($result, $cont, "registro"));
                $voti_pagelline[$id_studente_interno][0][2] = decode(trim(pg_fetch_result($result, $cont, "nome")));
                $voti_pagelline[$id_studente_interno][0][3] = decode(trim(pg_fetch_result($result, $cont, "cognome")));
                $voti_pagelline[$id_studente_interno][0][4] = trim(pg_fetch_result($result, $cont, "esonero_religione"));
                $voti_pagelline[$id_studente_interno][0][5] = trim(pg_fetch_result($result, $cont, "esonero_ed_fisica"));
                $voti_pagelline[$id_studente_interno][0][6] = trim(pg_fetch_result($result, $cont, "id_pagellina"));

                $voti_pagelline[$id_studente_interno][0]["id_studente"] = trim(pg_fetch_result($result, $cont, "id_studente"));
                $voti_pagelline[$id_studente_interno][0]["ritirato"] = trim(pg_fetch_result($result, $cont, "ritirato"));
                $voti_pagelline[$id_studente_interno][0]["crediti_terza"] = trim(pg_fetch_result($result, $cont, "crediti_terza"));
                $voti_pagelline[$id_studente_interno][0]["crediti_quarta"] = trim(pg_fetch_result($result, $cont, "crediti_quarta"));
                $voti_pagelline[$id_studente_interno][0]["crediti_quinta"] = trim(pg_fetch_result($result, $cont, "crediti_quinta"));
                $voti_pagelline[$id_studente_interno][0]["crediti_sospesi_terza"] = trim(pg_fetch_result($result, $cont, "crediti_sospesi_terza"));
                $voti_pagelline[$id_studente_interno][0]["crediti_sospesi_quarta"] = trim(pg_fetch_result($result, $cont, "crediti_sospesi_quarta"));
                $voti_pagelline[$id_studente_interno][0]["crediti_reintegrati_terza"] = trim(pg_fetch_result($result, $cont, "crediti_reintegrati_terza"));
                $voti_pagelline[$id_studente_interno][0]["crediti_reintegrati_quarta"] = trim(pg_fetch_result($result, $cont, "crediti_reintegrati_quarta"));
                $voti_pagelline[$id_studente_interno][0]["crediti_finali_agg"] = trim(pg_fetch_result($result, $cont, "crediti_finali_agg"));

                $voti_pagelline[$id_studente_interno][0]["giudizio_ammissione_terza"] = decode(trim(pg_fetch_result($result, $cont, "giudizio_ammissione_terza")));
                $voti_pagelline[$id_studente_interno][0]["voto_ammissione"] = trim(pg_fetch_result($result, $cont, "voto_ammissione"));
                $voti_pagelline[$id_studente_interno][0]["ammesso_esame_qualifica"] = trim(pg_fetch_result($result, $cont, "ammesso_esame_qualifica"));
                $voti_pagelline[$id_studente_interno][0]["curriculum_prima"] = trim(pg_fetch_result($result, $cont, "curriculum_prima"));
                $voti_pagelline[$id_studente_interno][0]["curriculum_seconda"] = trim(pg_fetch_result($result, $cont, "curriculum_seconda"));
                $voti_pagelline[$id_studente_interno][0]["stage_professionali"] = trim(pg_fetch_result($result, $cont, "stage_professionali"));
                $voti_pagelline[$id_studente_interno][0]["voto_qualifica"] = trim(pg_fetch_result($result, $cont, "voto_qualifica"));
                $voti_pagelline[$id_studente_interno][0]["voto_esame_sc1_qual"] = trim(pg_fetch_result($result, $cont, "voto_esame_sc1_qual"));
                $voti_pagelline[$id_studente_interno][0]["voto_esame_sc2_qual"] = trim(pg_fetch_result($result, $cont, "voto_esame_sc2_qual"));
                $voti_pagelline[$id_studente_interno][0]["voto_esame_or_qual"] = trim(pg_fetch_result($result, $cont, "voto_esame_or_qual"));

                $voti_pagelline[$id_studente_interno][$cont_pag][7] = trim(pg_fetch_result($result, $cont, "id_voto_pagellina"));
                $voti_pagelline[$id_studente_interno][$cont_pag][8] = trim(pg_fetch_result($result, $cont, "voto_pagellina"));
                $voti_pagelline[$id_studente_interno][$cont_pag][9] = trim(pg_fetch_result($result, $cont, "debito"));
                $voti_pagelline[$id_studente_interno][$cont_pag][10] = trim(pg_fetch_result($result, $cont, "id_materia"));
                $voti_pagelline[$id_studente_interno][$cont_pag][11] = trim(pg_fetch_result($result, $cont, "voto_scritto_pagella"));
                $voti_pagelline[$id_studente_interno][$cont_pag][12] = trim(pg_fetch_result($result, $cont, "voto_orale_pagella"));
                $voti_pagelline[$id_studente_interno][$cont_pag][13] = trim(pg_fetch_result($result, $cont, "voto_pratico_pagella"));
                $voti_pagelline[$id_studente_interno][$cont_pag][14] = trim(pg_fetch_result($result, $cont, "ore_assenza"));
                $voti_pagelline[$id_studente_interno][$cont_pag][15] = trim(pg_fetch_result($result, $cont, "codice"));
                $voti_pagelline[$id_studente_interno][$cont_pag][16] = trim(pg_fetch_result($result, $cont, "descrizione"));
                $voti_pagelline[$id_studente_interno][$cont_pag]['descrizione_materia_straniera'] = trim(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
                $voti_pagelline[$id_studente_interno][$cont_pag][17] = trim(pg_fetch_result($result, $cont, "votazione_differenziata"));
                $voti_pagelline[$id_studente_interno][$cont_pag][25] = trim(pg_fetch_result($result, $cont, "in_media_pagelle"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["scritto"] = trim(pg_fetch_result($result, $cont, "scritto"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["orale"] = trim(pg_fetch_result($result, $cont, "orale"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["pratico"] = trim(pg_fetch_result($result, $cont, "pratico"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["codice"] = trim(pg_fetch_result($result, $cont, "codice"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["id_materia"] = trim(pg_fetch_result($result, $cont, "id_materia"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["tipo_valutazione"] = trim(pg_fetch_result($result, $cont, "tipo_valutazione"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["tipo_recupero"] = trim(pg_fetch_result($result, $cont, "tipo_recupero"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["esito_recupero"] = trim(pg_fetch_result($result, $cont, "esito_recupero"));
                $voti_pagelline[$id_studente_interno][$cont_pag]["giudizio_analitico"] = trim(pg_fetch_result($result, $cont, "giudizio_analitico"));
                $voti_pagelline[$id_studente_interno][$cont_pag]['in_media_pagelle'] = trim(pg_fetch_result($result, $cont, "in_media_pagelle"));
                $voti_pagelline[$id_studente_interno][$cont_pag]['tipo_voto_personalizzato'] = trim(pg_fetch_result($result, $cont, "tipo_voto_personalizzato"));
                $voti_pagelline[$id_studente_interno][$cont_pag]['nome_materia_sito'] = trim(pg_fetch_result($result, $cont, "nome_materia_sito"));
                $voti_pagelline[$id_studente_interno][$cont_pag]['nome_materia_breve'] = trim(pg_fetch_result($result, $cont, "nome_materia_breve"));

                $voti_pagelline[$id_studente_interno][0]["ammesso_esame_quinta"] = trim(pg_fetch_result($result, $cont, "ammesso_esame_quinta"));
                $voti_pagelline[$id_studente_interno][0]["giudizio_ammissione_quinta"] = trim(pg_fetch_result($result, $cont, "giudizio_ammissione_quinta"));
                $voti_pagelline[$id_studente_interno][0]["stato_licenza_maestro"] = trim(pg_fetch_result($result, $cont, "stato_licenza_maestro"));
                $voti_pagelline[$id_studente_interno][0]["esito_prima_media"] = trim(pg_fetch_result($result, $cont, "esito_prima_media"));
                $voti_pagelline[$id_studente_interno][0]["esito_seconda_media"] = trim(pg_fetch_result($result, $cont, "esito_seconda_media"));
                $voti_pagelline[$id_studente_interno][0]["esito_terza_media"] = trim(pg_fetch_result($result, $cont, "esito_terza_media"));

                $cont_pag = $cont_pag + 1;
            }
        }
    }

    return $voti_pagelline;
    //}}} </editor-fold>
}

//aggiunti campi per sostituire vecchia funzione di estrazione per stampa tabellone
function estrai_voti_tabellone_pagellina_classe_finale($id, $periodo, $current_user, $tipo = 'classe', $filtro = 'NO') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di tutte le materie">
    //
    $sql = "SELECT DISTINCT id_tipo_voto
            FROM significati_voti
            WHERE flag_canc = 0";

    $res = pgsql_query($sql) or die("Invalid $sql");
    $tipi_voto = pg_fetch_all($res);

    $arr_significati_voti = [];

    foreach ($tipi_voto as $id_tipo_voto) {
        $id_tipo = $id_tipo_voto['id_tipo_voto'];
        $arr_significati_voti[$id_tipo] = estrai_significati_voti_pagelle($id_tipo, "solo_abilitati", $periodo);
    }

    $esistenza_campi_liberi = verifica_esistenza_valori_campi_liberi();

    if ($tipo == 'classe') {
        //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT
                        classi_studenti.id_studente,

                        pagelline.id_pagellina,

                        voti_pagelline.id_voto_pagellina,
                        voti_pagelline.voto_pagellina,
                        voti_pagelline.proposta_voto_pagellina,
                        voti_pagelline.debito,
                        voti_pagelline.id_materia,
                        voti_pagelline.voto_scritto_pagella,
                        voti_pagelline.voto_orale_pagella,
                        voti_pagelline.voto_pratico_pagella,
                        voti_pagelline.proposta_voto_scritto_pagella,
                        voti_pagelline.proposta_voto_orale_pagella,
                        voti_pagelline.proposta_voto_pratico_pagella,
                        voti_pagelline.ore_assenza,
                        voti_pagelline.monteore_totale,
                        voti_pagelline.tipo_recupero,
                        voti_pagelline.esito_recupero,
                        voti_pagelline.modificato_da_amministratore,
                        voti_pagelline.giudizio_analitico,

                        materie.codice,
                        materie.tipo_valutazione,
                        materie.descrizione,
                        materie.nome_materia_breve,
                        materie.descrizione_materia_straniera,
                        materie.scritto,
                        materie.orale,
                        materie.pratico,
                        materie.in_media_pagelle,
                        materie.ordinamento,
                        materie.tipo_materia,
                        materie.tipo_voto_personalizzato,

                        COALESCE((
                            SELECT ordinamento
                            FROM classi_prof_materie
                            WHERE id_classe = {$id}
                                    AND id_materia = voti_pagelline.id_materia
                                    AND flag_canc = 0
                            ORDER BY data_modifica DESC
                            LIMIT 1),0
                        ) AS ordinamento_abbinamento
                    FROM classi_studenti
                    INNER JOIN pagelline ON pagelline.id_studente = classi_studenti.id_studente
                    INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                    INNER JOIN materie ON voti_pagelline.id_materia = materie.id_materia
                    INNER JOIN studenti_completi ON studenti_completi.id_studente = classi_studenti.id_studente ";

        if ($filtro == 'tipi_recupero_valorizzati') {
            $query .= " WHERE classi_studenti.id_classe = {$id}
                            AND pagelline.periodo = '{$periodo}'
                            AND voti_pagelline.tipo_recupero <> ''
                            AND classi_studenti.flag_canc = 0
                            AND voti_pagelline.flag_canc = 0
                            AND pagelline.flag_canc = 0
                            AND materie.flag_canc = 0
                        ORDER BY
                            studenti_completi.cognome,
                            studenti_completi.nome,
                            ordinamento_abbinamento,
                            ordinamento;";
        } else {
            $query .= " WHERE classi_studenti.id_classe = {$id}
                            AND pagelline.periodo = '{$periodo}'
                            AND classi_studenti.flag_canc = 0
                            AND voti_pagelline.flag_canc = 0
                            AND pagelline.flag_canc = 0
                            AND materie.flag_canc = 0
                        ORDER BY
                            studenti_completi.cognome,
                            studenti_completi.nome,
                            ordinamento_abbinamento,
                            ordinamento;";
        }
        //}}} </editor-fold>
    } else {
        //{{{ <editor-fold defaultstate="collapsed">
        $query = "SELECT
                        pagelline.id_studente,
                        pagelline.id_pagellina,

                        voti_pagelline.id_voto_pagellina,
                        voti_pagelline.voto_pagellina,
                        voti_pagelline.proposta_voto_pagellina,
                        voti_pagelline.debito,
                        voti_pagelline.id_materia,
                        voti_pagelline.voto_scritto_pagella,
                        voti_pagelline.voto_orale_pagella,
                        voti_pagelline.voto_pratico_pagella,
                        voti_pagelline.proposta_voto_scritto_pagella,
                        voti_pagelline.proposta_voto_orale_pagella,
                        voti_pagelline.proposta_voto_pratico_pagella,
                        voti_pagelline.ore_assenza,
                        voti_pagelline.monteore_totale,
                        voti_pagelline.tipo_recupero,
                        voti_pagelline.esito_recupero,
                        voti_pagelline.modificato_da_amministratore,
                        voti_pagelline.giudizio_analitico,

                        materie.codice,
                        materie.tipo_valutazione,
                        materie.descrizione,
                        materie.descrizione_materia_straniera,
                        materie.nome_materia_breve,
                        materie.scritto,
                        materie.orale,
                        materie.pratico,
                        materie.in_media_pagelle,
                        materie.ordinamento,
                        materie.tipo_materia,
                        materie.tipo_voto_personalizzato,

                        COALESCE((
                                SELECT ordinamento
                                FROM classi_prof_materie
                                WHERE id_classe IN (SELECT id_classe FROM studenti_completi WHERE id_studente = {$id})
                                    AND id_materia = voti_pagelline.id_materia
                                    AND flag_canc=0
                                ORDER BY data_modifica DESC
                                LIMIT 1),0
                        ) AS ordinamento_abbinamento

                    FROM pagelline
                    INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina = pagelline.id_pagellina
                    INNER JOIN materie ON voti_pagelline.id_materia = materie.id_materia ";

        if ($filtro == 'tipi_recupero_valorizzati') {
            $query .= "
                            WHERE pagelline.id_studente = {$id}
                                AND pagelline.periodo = '{$periodo}'
                                AND voti_pagelline.flag_canc = 0
                                AND voti_pagelline.tipo_recupero <> ''
                                AND pagelline.flag_canc = 0
                                AND materie.flag_canc = 0
                            ORDER BY
                                ordinamento_abbinamento,
                                ordinamento";
        } else {
            $query .= "
                            WHERE pagelline.id_studente = {$id}
                                AND pagelline.periodo = '{$periodo}'
                                AND voti_pagelline.flag_canc = 0
                                AND pagelline.flag_canc = 0
                                AND materie.flag_canc = 0
                            ORDER BY
                                ordinamento_abbinamento,
                                coalesce(cast(nullif(materie.ordinamento,'') as integer),0)";
        }
        //}}} </editor-fold>
    }
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $voto_pagellina = pg_fetch_assoc($result, $cont);

            foreach ($voto_pagellina as $key => $value) {
                $voto_pagellina[$key] = decode($value);
            }

            if ($voto_pagellina['esito_recupero'] == 'SI') {
                $voto_pagellina['esito_recupero_tradotto'] = 'Positivo';
            } elseif ($voto_pagellina['esito_recupero'] == 'NO') {
                $voto_pagellina['esito_recupero_tradotto'] = 'Negativo';
            } elseif ($voto_pagellina['esito_recupero'] == 'ASSENTE') {
                $voto_pagellina['esito_recupero_tradotto'] = 'Assente';
            } elseif ($voto_pagellina['esito_recupero'] == 'NI') {
                $voto_pagellina['esito_recupero_tradotto'] = 'Parziale';
            } else {
                $voto_pagellina['esito_recupero_tradotto'] = 'Non definito/da definire';
            }

            $voto_pagellina['tipo_recupero_tradotto'] = estrai_tipo_recupero_singolo($voto_pagellina['tipo_recupero']);
            $significati_voto = $arr_significati_voti[$voto_pagellina["tipo_valutazione"]];
            $campi_liberi = $esistenza_campi_liberi && is_numeric($voto_pagellina['id_voto_pagellina']) ? estrai_valori_campi_liberi_materia_studente_per_certificati($voto_pagellina['id_voto_pagellina']) : NULL;

            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']] = [
                "id_pagellina" => $voto_pagellina['id_pagellina'],
                "id_voto_pagellina" => $voto_pagellina['id_voto_pagellina'],
                "voto_pagellina" => $voto_pagellina['voto_pagellina'],
                "proposta_voto_pagellina" => $voto_pagellina['proposta_voto_pagellina'],
                "debito" => $voto_pagellina['debito'],
                "voto_scritto_pagella" => $voto_pagellina['voto_scritto_pagella'],
                "voto_orale_pagella" => $voto_pagellina['voto_orale_pagella'],
                "voto_pratico_pagella" => $voto_pagellina['voto_pratico_pagella'],
                "proposta_voto_scritto_pagella" => $voto_pagellina['proposta_voto_scritto_pagella'],
                "proposta_voto_orale_pagella" => $voto_pagellina['proposta_voto_orale_pagella'],
                "proposta_voto_pratico_pagella" => $voto_pagellina['proposta_voto_pratico_pagella'],
                "ore_assenza" => $voto_pagellina['ore_assenza'],
                "monteore_totale" => $voto_pagellina['monteore_totale'],
                "tipo_recupero" => $voto_pagellina['tipo_recupero'],
                "esito_recupero" => $voto_pagellina['esito_recupero'],
                "tipo_recupero_tradotto" => $voto_pagellina['tipo_recupero_tradotto'],
                "esito_recupero_tradotto" => $voto_pagellina['esito_recupero_tradotto'],
                "modificato_da_amministratore" => $voto_pagellina['modificato_da_amministratore'],
                "giudizio_analitico" => $voto_pagellina['giudizio_analitico'],
                "descrizione_materia" => $voto_pagellina['descrizione'],
                "nome_materia_breve" => $voto_pagellina['nome_materia_breve'],
                "descrizione_materia_straniera" => $voto_pagellina['descrizione_materia_straniera'],
                "in_media_pagelle" => $voto_pagellina['in_media_pagelle'],
                "scritto" => $voto_pagellina['scritto'],
                "orale" => $voto_pagellina['orale'],
                "pratico" => $voto_pagellina['pratico'],
                "tipo_voto_personalizzato" => $voto_pagellina['tipo_voto_personalizzato'],
                "significati_voto" => $significati_voto,
                'campi_liberi' => $campi_liberi
            ];
            //{{{ <editor-fold defaultstate="collapsed" desc="campi aggiuuntivi per adeguamento a vecchia funzione estrai_voti_pagellina_studente_vecchio">
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][0] = $voto_pagellina['id_materia'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][1] = $voto_pagellina['codice'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][2] = $voto_pagellina['descrizione'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["descrizione"] = $voto_pagellina['descrizione'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["tipo_materia"] = $voto_pagellina['tipo_materia'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["descrizione_scuola_media"] = $voto_pagellina['descrizione_scuola_media'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["tipologia_aggregamento"] = $voto_pagellina['tipologia_aggregamento'];

            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["id_voto_pagellina"] = $voto_pagellina['id_voto_pagellina'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["id_materia"] = $voto_pagellina['id_materia'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][12] = $voto_pagellina['ore_assenza'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][14] = $periodo;

            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][6] = $voto_pagellina['voto_pagellina'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][50] = $voto_pagellina['voto_scritto_pagella'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][51] = $voto_pagellina['voto_orale_pagella'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][52] = $voto_pagellina['voto_pratico_pagella'];
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]['tipo_voto_personalizzato'] = $voto_pagellina['tipo_voto_personalizzato'];

            //questi sono gli unici campi che non sono stati portati
            //$voti_pagelline[$cont_materia][13] = $id_classe;
            //$voti_pagelline[$cont_materia][15] = trim(pg_fetch_result($result_materia,$cont_materia,"indirizzo"));
            //$voti_pagelline[$cont_materia][16] = trim(pg_fetch_result($result_materia,$cont_materia,"classe")) . trim(pg_fetch_result($result_materia,$cont_materia,"sezione"));
            //$voti_pagelline[$cont_materia][11] = trim(pg_fetch_result($result_voto,0,"data"));

            for ($cont_significati = 0; $cont_significati < count($significati_voto); $cont_significati++) {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][77][$cont_significati][0] = $significati_voto[$cont_significati]['voto'];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][77][$cont_significati][1] = $significati_voto[$cont_significati]['valore_pagella'];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][77][$cont_significati][2] = $significati_voto[$cont_significati]['codice_pagella'];

                $voto_tmp = (string) $significati_voto[$cont_significati]['voto'];
                $significati_materia[$voto_tmp]["codice"] = $significati_voto[$cont_significati]['codice'];
                $significati_materia[$voto_tmp]["valore"] = $significati_voto[$cont_significati]['valore_pagella'];
                $significati_materia[$voto_tmp]["valore_pagella"] = $significati_voto[$cont_significati]['valore_pagella'];
            }

            //voto unico
            $voto_tmp2 = (string) $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["voto_pagellina"];

            if (is_array($significati_materia[$voto_tmp2])) {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][10] = decode($significati_materia[$voto_tmp2]["codice"]);
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][1000] = decode($significati_materia[$voto_tmp2]["valore"]);
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][110] = decode($significati_materia[$voto_tmp2]["valore_pagella"]);
            } else {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][10] = $voto_tmp2;
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][110] = $voto_tmp2;
            }

            //voto scritto
            $voto_tmp2 = (string) $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["voto_scritto_pagella"];
            if (is_array($significati_materia[$voto_tmp2])) {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][60] = $significati_materia[$voto_tmp2]["codice"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][6000] = $significati_materia[$voto_tmp2]["valore"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][160] = $significati_materia[$voto_tmp2]["valore_pagella"];
            } else {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][60] = $voto_tmp2;
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][160] = $voto_tmp2;
            }

            //voto orale
            $voto_tmp2 = (string) $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["voto_orale_pagella"];
            if (is_array($significati_materia[$voto_tmp2])) {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][61] = $significati_materia[$voto_tmp2]["codice"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][6100] = $significati_materia[$voto_tmp2]["valore"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][161] = $significati_materia[$voto_tmp2]["valore_pagella"];
            } else {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][61] = $voto_tmp2;
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][161] = $voto_tmp2;
            }

            //voto pratico
            $voto_tmp2 = (string) $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]["voto_pratico_pagella"];
            if (is_array($significati_materia[$voto_tmp2])) {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][62] = $significati_materia[$voto_tmp2]["codice"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][6200] = $significati_materia[$voto_tmp2]["valore"];
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][162] = $significati_materia[$voto_tmp2]["valore_pagella"];
            } else {
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][62] = $voto_tmp2;
                $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']][162] = $voto_tmp2;
            }
            //}}} </editor-fold>
        }
    }

    return $voti_pagelline;
    //}}} </editor-fold>
}

function estrai_voti_tabellone_pagellina_classe_finale_con_correzione_dati_multipli($id_classe, $periodo, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di tutte le materie">
    $query = "SELECT
					classi_studenti.id_studente,

					pagelline.id_pagellina,

					voti_pagelline.id_voto_pagellina,
					voti_pagelline.voto_pagellina,
					voti_pagelline.proposta_voto_pagellina,
					voti_pagelline.debito,
					voti_pagelline.id_materia,
					voti_pagelline.voto_scritto_pagella,
					voti_pagelline.voto_orale_pagella,
					voti_pagelline.voto_pratico_pagella,
					voti_pagelline.proposta_voto_scritto_pagella,
					voti_pagelline.proposta_voto_orale_pagella,
					voti_pagelline.proposta_voto_pratico_pagella,
					voti_pagelline.ore_assenza,
					voti_pagelline.monteore_totale,
					voti_pagelline.tipo_recupero,
					voti_pagelline.esito_recupero,
					voti_pagelline.modificato_da_amministratore,
					voti_pagelline.giudizio_analitico
				FROM
					classi_studenti
					INNER JOIN pagelline ON pagelline.id_studente=classi_studenti.id_studente
					INNER JOIN voti_pagelline ON voti_pagelline.id_pagellina=pagelline.id_pagellina
				WHERE
					classi_studenti.id_classe='$id_classe'
					AND
					pagelline.periodo='$periodo'
					AND
					classi_studenti.flag_canc=0
					AND
					voti_pagelline.flag_canc=0
					AND
					pagelline.flag_canc=0";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $id_pagellina_riferimento = [];
        $mat_pagelline = [];
        $voti_pagelline = [];
        for ($cont = 0; $cont < $numero; $cont++) {
            //{{{ <editor-fold defaultstate="collapsed">
            $voto_pagellina = pg_fetch_assoc($result, $cont);
            if (!($id_pagellina_riferimento[$voto_pagellina['id_studente']] > 0)) {
                $id_pagellina_riferimento[$voto_pagellina['id_studente']] = $voto_pagellina['id_pagellina'];
            } else {
                if ($voto_pagellina['id_pagellina'] != $id_pagellina_riferimento[$voto_pagellina['id_studente']]) {
                    $mat_pagelline[$voto_pagellina['id_pagellina']] = $voto_pagellina['id_pagellina'];
                }
            }

            if ($voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]['id_voto_pagellina'] > 0) {
                $query_update = "UPDATE voti_pagelline
									SET
										flag_canc = " . time() . ",
										chi_modifica = -100
									WHERE
										id_voto_pagellina =" . $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']]['id_voto_pagellina'];
                pgsql_query($query_update) or die("Invalid $query_update");
                //echo " Trovato doppio<br>\n";
            }
            $voti_pagelline[$voto_pagellina['id_studente']][$voto_pagellina['id_materia']] = [
                "id_pagellina" => $id_pagellina_riferimento[$voto_pagellina['id_studente']],
                "id_voto_pagellina" => $voto_pagellina['id_voto_pagellina'],
                "voto_pagellina" => $voto_pagellina['voto_pagellina'],
                "proposta_voto_pagellina" => $voto_pagellina['proposta_voto_pagellina'],
                "debito" => $voto_pagellina['debito'],
                "voto_scritto_pagella" => $voto_pagellina['voto_scritto_pagella'],
                "voto_orale_pagella" => $voto_pagellina['voto_orale_pagella'],
                "voto_pratico_pagella" => $voto_pagellina['voto_pratico_pagella'],
                "proposta_voto_scritto_pagella" => $voto_pagellina['proposta_voto_scritto_pagella'],
                "proposta_voto_orale_pagella" => $voto_pagellina['proposta_voto_orale_pagella'],
                "proposta_voto_pratico_pagella" => $voto_pagellina['proposta_voto_pratico_pagella'],
                "ore_assenza" => $voto_pagellina['ore_assenza'],
                "monteore_totale" => $voto_pagellina['monteore_totale'],
                "tipo_recupero" => $voto_pagellina['tipo_recupero'],
                "esito_recupero" => $voto_pagellina['esito_recupero'],
                "modificato_da_amministratore" => $voto_pagellina['modificato_da_amministratore'],
                "giudizio_analitico" => $voto_pagellina['giudizio_analitico']
            ];
            $query_update = "UPDATE voti_pagelline
								SET
									id_pagellina = " . $id_pagellina_riferimento[$voto_pagellina['id_studente']] . "
								WHERE
									id_voto_pagellina = " . $voto_pagellina['id_voto_pagellina'];
            $result_update = pgsql_query($query_update) or die("Invalid $query_update");
            $indice_elenco_campi = $id_classe . '-' . $periodo . '-' . $voto_pagellina['id_studente'];

            if (!is_array($elenco_campi_liberi[$indice_elenco_campi])) {
                $elenco_campi_liberi[$indice_elenco_campi] = estrai_elenco_campi_liberi($id_classe, $periodo, $voto_pagellina['id_studente']);
            }

            if (is_array($elenco_campi_liberi[$indice_elenco_campi])) {
                foreach ($elenco_campi_liberi[$indice_elenco_campi] as $campo_libero) {
                    $query_campi_liberi = "SELECT
												id_valore_campo_libero,
												chi_inserisce
											FROM valori_campi_liberi
											WHERE id_oggetto = " . $voto_pagellina['id_voto_pagellina'] . "
												AND id_campo_libero = " . $campo_libero['id_campo_libero'] . "
												AND flag_canc = 0";

                    $result_campi_liberi = pgsql_query($query_campi_liberi) or die("Invalid $query_campi_liberi");
                    $cont_campi_liberi = pg_num_rows($result_campi_liberi);

                    if ($cont_campi_liberi > 1) {
                        for ($cont_campi = 1; $cont_campi < $cont_campi_liberi; $cont_campi++) {
                            $campo_libero_da_eliminare = pg_fetch_assoc($result_campi_liberi, $cont_campi);
                            $query_update = "UPDATE valori_campi_liberi
												SET
													flag_canc = " . time() . ",
													chi_modifica = -100
												WHERE
													id_valore_campo_libero = " . $campo_libero_da_eliminare['id_valore_campo_libero'];
                            pgsql_query($query_update) or die("Invalid $query_update");
                            //echo " Eliminato campo libero inserito da ".$campo_libero_da_eliminare['chi_inserisce']."<br>\n";
                        }
                    }
                }
            }
            //}}} </editor-fold>
        }

        foreach ($mat_pagelline as $pagellina) {
            $query_update = "UPDATE pagelline
								SET flag_canc = " . time() . ",
									chi_modifica = -100
								WHERE id_pagellina =" . $pagellina;

            pgsql_query($query_update) or die("Invalid $query_update");
        }
    }

    $query = "UPDATE pagelline
				SET flag_canc='" . time() . "',
					chi_modifica='$current_user'
				WHERE id_pagellina IN (
						SELECT pagelline.id_pagellina
						FROM classi_studenti
							INNER JOIN pagelline ON pagelline.id_studente = classi_studenti.id_studente
						WHERE classi_studenti.id_classe='$id_classe'
							AND pagelline.periodo='$periodo'
							AND classi_studenti.flag_canc=0
							AND pagelline.flag_canc=0
							AND id_pagellina NOT IN (
								SELECT DISTINCT id_pagellina
                                FROM voti_pagelline
							)
					)";

    pgsql_query($query) or die("Invalid $query");
    //}}} </editor-fold>
}

function calcola_voto_ammissione_esame_qualifica($id_oggetto, $tipo = 'classe', $current_user) {
    //{{{ <editor-fold defaultstate="collapsed">
    $parametri_professionali = estrai_parametri_professionali();
    $voto_pagellina_totale = [];

    $arrotondamento_parallelo = estrai_parametri_singoli("ARROTONDAMENTO_PARALLELO");
    $arrotondamento_ammissione_calcolato = estrai_parametri_singoli("ARROTONDAMENTO_AMMISSIONE_CALCOLATO");

    $dati_colonne_ammissione = [];
    if ($tipo == 'classe') {
        $arrotondamento_materia = estrai_parametri_singoli("ARROTONDAMENTO_MATERIA", $id_oggetto, 'classe');

        $elenco_studenti_tmp = estrai_studenti_classe($id_oggetto);
        foreach ($elenco_studenti_tmp as $studente) {
            $elenco_studenti[$studente['id_studente']] = $studente;
        }
    } else {
        // studente
        $elenco_studenti[$id_oggetto] = estrai_dati_studente($id_oggetto);
        $dati_classe = estrai_classe_principale_studente($id_oggetto);

        $arrotondamento_materia = estrai_parametri_singoli("ARROTONDAMENTO_MATERIA", $dati_classe['id_classe'], 'classe');
    }

    //var_dump_cesco($elenco_studenti);
    //{{{ <editor-fold defaultstate="collapsed" desc="primo quadrimestre">
    if ($tipo == 'classe') {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "7", $current_user, 'classe');
    } else {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "7", $current_user, 'studente');
    }

    if (is_array($elenco_voti_pagelline)) {
        foreach ($elenco_voti_pagelline as $key_id_studente => $studente) {
            //{{{ <editor-fold defaultstate="collapsed">
            $tipo_visualizzazione_voti = identifica_periodo_tipo_voto("7", $elenco_studenti[$key_id_studente]['id_classe']);
            $cont_voti = 0;
            $totale_voti = 0;
            foreach ($studente as $key_id_materia => $voto_pagellina) {
                if ($voto_pagellina['in_media_pagelle'] == 'SI') {
                    $voto_pagellina_totale['7'][$key_id_studente][$key_id_materia] = $voto_pagellina;
                    $voto_parallelo = 0;
                    $cont_parallelo = 0;
                    if ($tipo_visualizzazione_voti != "voto_singolo") {
                        if (($voto_pagellina['voto_scritto_pagella'] != "") and ( floatval($voto_pagellina['voto_scritto_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_orale_pagella'] != "") and ( floatval($voto_pagellina['voto_orale_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_pratico_pagella'] != "") and ( floatval($voto_pagellina['voto_pratico_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_parallelo++;
                        }
                    } else {
                        if (($voto_pagellina['voto_pagellina'] != "") and ( floatval($voto_pagellina['voto_pagellina']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pagellina']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pagellina']);
                            $cont_parallelo = 1;
                        }
                    }
                    if ($cont_parallelo > 0) {
                        $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                    } else {
                        $media_parallelo = 0;
                    }
                    $voto_pagellina_totale['7'][$key_id_studente][$key_id_materia]['media_parallelo'] = $media_parallelo;
                }
            }
            if ($cont_voti > 0) {
                $media = round(($totale_voti / $cont_voti), 1);
            } else {
                $media = 0;
            }
            $valore_ammissione = $media * 10;
            $elenco_studenti[$key_id_studente]["media_voti_scrutinio_primo_quadrimestre"] = $valore_ammissione;
            //}}} </editor-fold>
        }
    }
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="secondo trimestre">
    if ($tipo == 'classe') {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "8", $current_user, 'classe');
    } else {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "8", $current_user, 'studente');
    }
    if (is_array($elenco_voti_pagelline)) {
        foreach ($elenco_voti_pagelline as $key_id_studente => $studente) {
            //{{{ <editor-fold defaultstate="collapsed">
            $tipo_visualizzazione_voti = identifica_periodo_tipo_voto("8", $elenco_studenti[$key_id_studente]['id_classe']);
            $cont_voti = 0;
            $totale_voti = 0;
            foreach ($studente as $key_id_materia => $voto_pagellina) {
                if ($voto_pagellina['in_media_pagelle'] == 'SI') {
                    $voto_pagellina_totale['8'][$key_id_studente][$key_id_materia] = $voto_pagellina;
                    $voto_parallelo = 0;
                    $cont_parallelo = 0;

                    if ($tipo_visualizzazione_voti != "voto_singolo") {
                        if (($voto_pagellina['voto_scritto_pagella'] != "") and ( floatval($voto_pagellina['voto_scritto_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_orale_pagella'] != "") and ( floatval($voto_pagellina['voto_orale_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_pratico_pagella'] != "") and ( floatval($voto_pagellina['voto_pratico_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_parallelo++;
                        }
                    } else {
                        if (($voto_pagellina['voto_pagellina'] != "") and ( floatval($voto_pagellina['voto_pagellina']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pagellina']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pagellina']);
                            $cont_parallelo = 1;
                        }
                    }
                    if ($cont_parallelo > 0) {
                        $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                    } else {
                        $media_parallelo = 0;
                    }
                    $voto_pagellina_totale['8'][$key_id_studente][$key_id_materia]['media_parallelo'] = $media_parallelo;
                }
            }
            if ($cont_voti > 0) {
                $media = round(($totale_voti / $cont_voti), 1);
            } else {
                $media = 0;
            }
            $valore_ammissione = $media * 10;
            $elenco_studenti[$key_id_studente]["media_voti_scrutinio_secondo_trimestre"] = $valore_ammissione;
            //}}} </editor-fold>
        }
    }
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="prove strutturate">
    if ($tipo == 'classe') {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "10", $current_user, 'classe');
    } else {
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "10", $current_user, 'studente');
    }

    if (is_array($elenco_voti_pagelline)) {
        foreach ($elenco_voti_pagelline as $key_id_studente => $studente) {
            //{{{ <editor-fold defaultstate="collapsed">
            $tipo_visualizzazione_voti = identifica_periodo_tipo_voto("10", $elenco_studenti[$key_id_studente]['id_classe']);
            $cont_voti = 0;
            $totale_voti = 0;
            foreach ($studente as $key_id_materia => $voto_pagellina) {
                if ($voto_pagellina['in_media_pagelle'] == 'SI') {
                    $voto_pagellina_totale['10'][$key_id_studente][$key_id_materia] = $voto_pagellina;
                    $voto_parallelo = 0;
                    $cont_parallelo = 0;

                    if ($tipo_visualizzazione_voti != "voto_singolo") {
                        if (($voto_pagellina['voto_scritto_pagella'] != "") and ( floatval($voto_pagellina['voto_scritto_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_orale_pagella'] != "") and ( floatval($voto_pagellina['voto_orale_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_pratico_pagella'] != "") and ( floatval($voto_pagellina['voto_pratico_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_parallelo++;
                        }
                    } else {
                        if (($voto_pagellina['voto_pagellina'] != "") and ( floatval($voto_pagellina['voto_pagellina']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pagellina']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pagellina']);
                            $cont_parallelo = 1;
                        }
                    }
                    if ($cont_parallelo > 0) {
                        $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                    } else {
                        $media_parallelo = 0;
                    }
                    $voto_pagellina_totale['10'][$key_id_studente][$key_id_materia]['media_parallelo'] = $media_parallelo;
                }
            }
            if ($cont_voti > 0) {
                $media = round(($totale_voti / $cont_voti), 1);
            } else {
                $media = 0;
            }
            $valore_ammissione = $media * 10;
            $elenco_studenti[$key_id_studente]["media_voti_scrutinio_prove_strutturate"] = $valore_ammissione;
            //}}} </editor-fold>
        }
    }
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="fine anno">
    $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale($id_oggetto, "9", $current_user, $tipo == 'classe' ? 'classe' : 'studente');

    if (is_array($elenco_voti_pagelline)) {
        foreach ($elenco_voti_pagelline as $key_id_studente => $studente) {
            //{{{ <editor-fold defaultstate="collapsed">
            $tipo_visualizzazione_voti = identifica_periodo_tipo_voto("9", $elenco_studenti[$key_id_studente]['id_classe']);
            $cont_voti = 0;
            $totale_voti = 0;
            foreach ($studente as $key_id_materia => $voto_pagellina) {
                if ($voto_pagellina['in_media_pagelle'] == 'SI') {
                    $voto_pagellina_totale['9'][$key_id_studente][$key_id_materia] = $voto_pagellina;
                    $voto_parallelo = 0;
                    $cont_parallelo = 0;

                    if ($tipo_visualizzazione_voti != "voto_singolo") {
                        if (($voto_pagellina['voto_scritto_pagella'] != "") and ( floatval($voto_pagellina['voto_scritto_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_scritto_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_orale_pagella'] != "") and ( floatval($voto_pagellina['voto_orale_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_orale_pagella']);
                            $cont_parallelo++;
                        }
                        if (($voto_pagellina['voto_pratico_pagella'] != "") and ( floatval($voto_pagellina['voto_pratico_pagella']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pratico_pagella']);
                            $cont_parallelo++;
                        }
                    } else {
                        if (($voto_pagellina['voto_pagellina'] != "") and ( floatval($voto_pagellina['voto_pagellina']) != 0)) {
                            $totale_voti += floatval($voto_pagellina['voto_pagellina']);
                            $cont_voti++;
                            $voto_parallelo += floatval($voto_pagellina['voto_pagellina']);
                            $cont_parallelo = 1;
                        }
                    }
                    if ($cont_parallelo > 0) {
                        $media_parallelo = round(($voto_parallelo / $cont_parallelo), $arrotondamento_parallelo);
                    } else {
                        $media_parallelo = 0;
                    }
                    $voto_pagellina_totale['9'][$key_id_studente][$key_id_materia]['media_parallelo'] = $media_parallelo;
                }
            }
            if ($cont_voti > 0) {
                $media = round(($totale_voti / $cont_voti), 1);
            } else {
                $media = 0;
            }
            $valore_ammissione = $media * 10;
            $elenco_studenti[$key_id_studente]["media_voti_scrutinio_fine_anno"] = $valore_ammissione;
            //}}} </editor-fold>
        }
    }
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="sezione di elaborazione colonne gruppi per ammissione">
    $cont_gruppi_media = 1;
    foreach ($parametri_professionali['MEDIA'] as $gruppo) {
        //{{{ <editor-fold defaultstate="collapsed" desc="ciclo per gruppi media">
        $cont_stato_gruppo = 0;
        if ($parametri_professionali['gruppi_media'] >= $cont_gruppi_media) {
            $intestazione[$gruppo['nome_gruppo']] = 'Gruppo Media ' . $gruppo['nome_gruppo'];
            if ($gruppo['periodo7_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>I Quadr.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo8_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>II Trim.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo9_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Fine Anno.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo10_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Prove Strutt.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['curriculum1_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum I';
                $cont_stato_gruppo++;
            }
            if ($gruppo['curriculum2_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum II';
                $cont_stato_gruppo++;
            }
            if ($gruppo['stage_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Stage';
                $cont_stato_gruppo++;
            }

            if ($cont_stato_gruppo >= 1) {
                foreach ($voto_pagellina_totale['9'] as $key_id_studente => $studente) {
                    $tot_materie = count($studente);
                    if ($tot_materie == 0) {
                        $tot_materie = 1;
                    }
                    foreach ($studente as $key_id_materia => $valore) {
                        $valore7 = round((
                                (
                                $voto_pagellina_totale['7'][$key_id_studente][$key_id_materia]['media_parallelo'] *
                                $gruppo['periodo7_perc1']
                                ) /
                                $gruppo['periodo7_perc2']
                                ), $arrotondamento_parallelo);

                        $valore8 = round((
                                (
                                $voto_pagellina_totale['8'][$key_id_studente][$key_id_materia]['media_parallelo'] *
                                $gruppo['periodo8_perc1']
                                ) /
                                $gruppo['periodo8_perc2']
                                ), $arrotondamento_parallelo);


                        $valore9 = round((
                                (
                                $voto_pagellina_totale['9'][$key_id_studente][$key_id_materia]['media_parallelo'] *
                                $gruppo['periodo9_perc1']
                                ) /
                                $gruppo['periodo9_perc2']
                                ), $arrotondamento_parallelo);


                        $valore10 = round((
                                (
                                $voto_pagellina_totale['10'][$key_id_studente][$key_id_materia]['media_parallelo'] *
                                $gruppo['periodo10_perc1']
                                ) /
                                $gruppo['periodo10_perc2']
                                ), $arrotondamento_parallelo);

                        // calcolo della media delle singole materie

                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] = 0;

                        if ($gruppo['periodo7_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore7;
                        }

                        if ($gruppo['periodo8_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore8;
                        }

                        if ($gruppo['periodo9_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore9;
                        }

                        if ($gruppo['periodo10_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore10;
                        }


                        $valore_curriculum1 = round((
                                (
                                (
                                $elenco_studenti[$key_id_studente]["curriculum_prima"] *
                                $gruppo['curriculum1_perc1']
                                ) /
                                $gruppo['curriculum1_perc2']
                                ) /
                                $tot_materie
                                ), $arrotondamento_parallelo);


                        $valore_curriculum2 = round((
                                (
                                (
                                $elenco_studenti[$key_id_studente]["curriculum_seconda"] *
                                $gruppo['curriculum2_perc1']
                                ) /
                                $gruppo['curriculum2_perc2']
                                ) /
                                $tot_materie
                                ), $arrotondamento_parallelo);


                        $valore_stage = round((
                                (
                                (
                                $elenco_studenti[$key_id_studente]["stage_professionali"] *
                                $gruppo['stage_perc1']
                                ) /
                                $gruppo['stage_perc2']
                                ) /
                                $tot_materie
                                ), $arrotondamento_parallelo);


                        if ($gruppo['curriculum1_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_curriculum1;
                        }

                        if ($gruppo['curriculum2_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_curriculum2;
                        }

                        if ($gruppo['stage_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] += $valore_stage;
                        }
                        //echo 'valore9=' . $valore9 . '--valore10=' . $valore10 . '--valore_curr1=' . $valore_curriculum1 . '<br>';
                    }
                }
            } else {
                foreach ($voto_pagellina_totale['9'] as $key_id_studente => $studente) {
                    foreach ($studente as $key_id_materia => $studente) {
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['media'] = '0';
                    }
                }
            }

            // calcolo della media totale
            foreach ($dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']] as $key_id_studente => $studente) {
                $conteggio_materie = 0;
                $conteggio_finale_media = 0;

                foreach ($studente as $key_mat => $materia) {
                    if ($materia['media'] > 0) {
                        $finale[$key_id_studente][$key_mat]['contributo'] = round($materia['media'], $arrotondamento_materia);
                        $conteggio_finale_media += round($materia['media'], $arrotondamento_materia);
                        $conteggio_materie++;
                        //echo '$conteggio_finale_media=' . $conteggio_finale_media . '--$conteggio_materie=' . $conteggio_materie . '<br>';
                    }
                }
                if ($conteggio_materie > 0) {
                    $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                    $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['media'] = round(($conteggio_finale_media / $conteggio_materie), $arrotondamento_parallelo);
                } else {
                    $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                    $dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['media'] = 0;
                }
            }
        }
        $cont_gruppi_media++;
        //}}} </editor-fold>
    }

    $cont_gruppi_somma = 1;
    foreach ($parametri_professionali['SOMMA'] as $gruppo) {
        //{{{ <editor-fold defaultstate="collapsed" desc="ciclo per gruppi somma">
        $cont_stato_gruppo = 0;
        if ($parametri_professionali['gruppi_somma'] >= $cont_gruppi_somma) {
            $intestazione[$gruppo['nome_gruppo']] = 'Gruppo Somma ' . $gruppo['nome_gruppo'];
            if ($gruppo['periodo7_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>I Quadr.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo8_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>II Trim.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo9_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Fine Anno.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['periodo10_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Prove Strutt.';
                $cont_stato_gruppo++;
            }
            if ($gruppo['curriculum1_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum I';
                $cont_stato_gruppo++;
            }
            if ($gruppo['curriculum2_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Curriculum II';
                $cont_stato_gruppo++;
            }
            if ($gruppo['stage_stato'] == 'SI') {
                $intestazione[$gruppo['nome_gruppo']] .= '<br>Stage';
                $cont_stato_gruppo++;
            }

            if ($cont_stato_gruppo >= 1) {
                foreach ($voto_pagellina_totale['9'] as $key_id_studente => $studente) {
                    foreach ($studente as $key_id_materia => $valore) {
                        $valore7 = $voto_pagellina_totale['7'][$key_id_studente][$key_id_materia]['somma_parallelo'];
                        $valore8 = $voto_pagellina_totale['8'][$key_id_studente][$key_id_materia]['somma_parallelo'];
                        $valore9 = $voto_pagellina_totale['9'][$key_id_studente][$key_id_materia]['somma_parallelo'];
                        $valore10 = $voto_pagellina_totale['10'][$key_id_studente][$key_id_materia]['somma_parallelo'];
                        // calcolo della somma totale
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] = 0;

                        if ($gruppo['periodo7_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore7;
                        }

                        if ($gruppo['periodo8_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore8;
                        }

                        if ($gruppo['periodo9_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore9;
                        }

                        if ($gruppo['periodo10_stato'] == 'SI') {
                            $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] += $valore10;
                        }
                    }
                    $valore_curriculum1 = $elenco_studenti[$key_id_studente]["curriculum_prima"];
                    $valore_curriculum2 = $elenco_studenti[$key_id_studente]["curriculum_seconda"];
                    $valore_stage = $elenco_studenti[$key_id_studente]["stage_professionali"];


                    if ($gruppo['curriculum1_stato'] == 'SI') {
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['curriculum1']['somma'] += $valore_curriculum1;
                    }

                    if ($gruppo['curriculum2_stato'] == 'SI') {
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['curriculum2']['somma'] += $valore_curriculum2;
                    }

                    if ($gruppo['stage_stato'] == 'SI') {
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente]['stage']['somma'] += $valore_stage;
                    }
                }
            } else {
                foreach ($voto_pagellina_totale['9'] as $key_id_studente => $studente) {
                    foreach ($studente as $key_id_materia => $studente) {
                        $dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']][$key_id_studente][$key_id_materia]['somma'] = '0';
                    }
                }
            }

            // calcolo della somma totale
            foreach ($dati_colonne_ammissione['colonne'][$gruppo['nome_gruppo']] as $key_id_studente => $studente) {
                $conteggio_finale_somma = 0;

                foreach ($studente as $materia) {
                    if ($materia['somma'] > 0) {
                        $conteggio_finale_somma += $materia['somma'];
                    }
                }
                $dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['intestazione'] = $intestazione[$gruppo['nome_gruppo']];
                $dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['valori'][$key_id_studente]['somma'] = $conteggio_finale_somma;
            }
        }
        $cont_gruppi_somma++;
        //}}} </editor-fold>
    }

    //elaborazioni finali di MEDIA e SOMMA
    $valori_finali_ammissioni_studenti = [];

    $cont_gruppi_media = 1;
    foreach ($parametri_professionali['MEDIA'] as $gruppo) {
        $cont_stato_gruppo = 0;

        if ($parametri_professionali['gruppi_media'] >= $cont_gruppi_media) {
            foreach ($dati_colonne_ammissione['risultati']['gruppi_media'][$gruppo['nome_gruppo']]['valori'] as $key_id_studente => $studente) {
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['valore'] = $studente['media'];
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['perc1'] = $gruppo['gruppo_perc1'];
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_media'][$gruppo['nome_gruppo']]['perc2'] = $gruppo['gruppo_perc2'];
            }
        }
        $cont_gruppi_media++;
    }

    $cont_gruppi_somma = 1;
    foreach ($parametri_professionali['SOMMA'] as $gruppo) {
        $cont_stato_gruppo = 0;

        if ($parametri_professionali['gruppi_somma'] >= $cont_gruppi_somma) {
            foreach ($dati_colonne_ammissione['risultati']['gruppi_somma'][$gruppo['nome_gruppo']]['valori'] as $key_id_studente => $studente) {
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['valore'] = $studente['somma'];
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['perc1'] = $gruppo['gruppo_perc1'];
                $valori_finali_ammissioni_studenti[$key_id_studente]['gruppi_somma'][$gruppo['nome_gruppo']]['perc2'] = $gruppo['gruppo_perc2'];
            }
        }
        $cont_gruppi_somma++;
    }

    foreach ($valori_finali_ammissioni_studenti as $key_id_studente => $studente) {
        $valore_ammissione_da_gruppi = 0;
        if (is_array($studente['gruppi_media'])) {
            foreach ($studente['gruppi_media'] as $valore_gruppo) {
                $valore_ammissione_da_gruppi += (($valore_gruppo['valore'] * $valore_gruppo['perc1']) / $valore_gruppo['perc2']);
            }
        }
        if (is_array($studente['gruppi_somma'])) {
            foreach ($studente['gruppi_somma'] as $valore_gruppo) {
                $valore_ammissione_da_gruppi += (($valore_gruppo['valore'] * $valore_gruppo['perc1']) / $valore_gruppo['perc2']);
            }
        }
        //#### parte modificata il 21/6/2010 per voto ammisisme massimo 100
        $voto_ammissione_calcolato = round($valore_ammissione_da_gruppi, $arrotondamento_ammissione_calcolato);
        if ($voto_ammissione_calcolato > 100) {
            $voto_ammissione_calcolato = 100;
        }
        $elenco_studenti[$key_id_studente]["voto_ammissione_calcolato"] = $voto_ammissione_calcolato;
        //#########

        if ($elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] == "--") {
            if ($elenco_studenti[$key_id_studente]["voto_ammissione_calcolato"] >= 60) {
                $elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] = "SI";
            } else {
                $elenco_studenti[$key_id_studente]["ammesso_esame_qualifica"] = "NO";
            }
        }
    }

    $risultato = [
        "dati_colonne_ammissione_media" => $dati_colonne_ammissione['risultati']['gruppi_media'],
        "dati_colonne_ammissione_somma" => $dati_colonne_ammissione['risultati']['gruppi_somma'],
        "elenco_studenti" => $elenco_studenti
    ];
    //}}} </editor-fold>
    return $risultato;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_studente_materia($id_materia, $periodo, $id_studente, $voto_singolo_o_triplo = "voto_singolo") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella">
    $query_pag = "SELECT
						voti_pagelline.*,
						materie.scritto,
						materie.orale,
						materie.pratico,
						materie.codice,
						materie.descrizione,
						materie.descrizione_materia_straniera
					FROM
						voti_pagelline
						INNER JOIN pagelline ON voti_pagelline.id_pagellina=pagelline.id_pagellina
						INNER JOIN materie ON materie.id_materia=voti_pagelline.id_materia
					WHERE
						pagelline.id_studente='$id_studente'
						AND
						pagelline.periodo='$periodo'
						AND
						voti_pagelline.id_materia='$id_materia'
						AND
						pagelline.flag_canc=0
						AND
						materie.flag_canc=0
						AND
						voti_pagelline.flag_canc=0";
    $result_pag = pgsql_query($query_pag) or die("Invalid $query_pag");
    $numero_pag = pg_num_rows($result_pag);

    if ($numero_pag > 0) {
        $voti_pagelline[0] = trim(pg_fetch_result($result_pag, 0, "id_materia"));
        $voti_pagelline[1] = trim(pg_fetch_result($result_pag, 0, "codice"));
        $voti_pagelline[2] = trim(pg_fetch_result($result_pag, 0, "descrizione"));
        $voti_pagelline['descrizione_materia_straniera'] = trim(pg_fetch_result($result_pag, 0, "descrizione_materia_straniera"));
        $voti_pagelline["scritto"] = trim(pg_fetch_result($result_pag, 0, "scritto"));
        $voti_pagelline["orale"] = trim(pg_fetch_result($result_pag, 0, "orale"));
        $voti_pagelline["pratico"] = trim(pg_fetch_result($result_pag, 0, "pratico"));
        $voti_pagelline["id_voto_pagellina"] = trim(pg_fetch_result($result_pag, 0, "id_voto_pagellina"));

        if ($voto_singolo_o_triplo == "voto_singolo") {
            $voti_pagelline[6] = trim(pg_fetch_result($result_pag, 0, "voto_pagellina"));
            $voti_pagelline["voto_singolo"] = trim(pg_fetch_result($result_pag, 0, "voto_pagellina"));
        } else {
            $voti_pagelline[50] = trim(pg_fetch_result($result_pag, 0, "voto_scritto_pagella"));
            $voti_pagelline[51] = trim(pg_fetch_result($result_pag, 0, "voto_orale_pagella"));
            $voti_pagelline[52] = trim(pg_fetch_result($result_pag, 0, "voto_pratico_pagella"));
            $voti_pagelline["voto_scritto"] = trim(pg_fetch_result($result_pag, 0, "voto_scritto_pagella"));
            $voti_pagelline["voto_orale"] = trim(pg_fetch_result($result_pag, 0, "voto_orale_pagella"));
            $voti_pagelline["voto_pratico"] = trim(pg_fetch_result($result_pag, 0, "voto_pratico_pagella"));
        }

        $voti_pagelline[12] = trim(pg_fetch_result($result_pag, 0, "ore_assenza"));
        $voti_pagelline["ore_assenza"] = trim(pg_fetch_result($result_pag, 0, "ore_assenza"));
    }

    return $voti_pagelline;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_studente_vecchio($id_classe, $periodo, $id_studente, $voto_singolo_o_triplo = "voto_singolo") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella">
    $query_pag = "SELECT
						pagelline.id_pagellina
					FROM
						pagelline
					WHERE
						pagelline.id_studente='$id_studente'
						AND
						pagelline.periodo='$periodo'
						AND
						pagelline.flag_canc=0";
    $result_pag = pgsql_query($query_pag) or die("Invalid $query_pag");
    $numero_pag = pg_num_rows($result_pag);

    if ($numero_pag > 0) {
        $voti_pagelline[0][20] = trim(pg_fetch_result($result_pag, 0, "id_pagellina"));
        $id_pagellina = trim(pg_fetch_result($result_pag, 0, "id_pagellina"));
    } else {
        $voti_pagelline[0][20] = "INESISTENTE";
    }

    //Attenzione: questa query deve essere mantenuta sincronizzata con la query di estrazione dati per campi liberi
    $query_materia = "SELECT DISTINCT
							classi_prof_materie.id_materia,
							classi_prof_materie.ordinamento AS ordinamento_principale,
							classi.classe,
							classi.sezione,
							materie.scritto,
							materie.orale,
							materie.pratico,
							materie.codice,
							materie.descrizione,
							materie.descrizione_materia_straniera,
							materie.ordinamento,
							materie.in_media_pagelle,
							materie.descrizione_scuola_media,
							materie.tipo_materia,
							materie.tipologia_aggregamento,
							indirizzi.codice AS indirizzo,
                            classi_prof_materie.itp
						FROM
							(
								(
									classi_prof_materie INNER JOIN classi ON classi.id_classe=classi_prof_materie.id_classe
								)
								INNER JOIN materie ON classi_prof_materie.id_materia= materie.id_materia
							)
							INNER JOIN indirizzi ON indirizzi.id_indirizzo=classi.id_indirizzo
							INNER JOIN utenti ON utenti.id_utente=classi_prof_materie.id_professore
						WHERE
							classi_prof_materie.id_classe=" . $id_classe . "
							AND
							classi_prof_materie.flag_canc=0
							AND
							classi.flag_canc=0
							AND
							materie.flag_canc=0
							AND
							materie.in_media_pagelle <> 'NV'
							AND
							indirizzi.flag_canc=0
							AND
							utenti.flag_canc=0
						ORDER BY
							classi_prof_materie.ordinamento,
							materie.ordinamento,
							materie.descrizione";
    $result_materia = pgsql_query($query_materia) or die("Invalid $query_materia");
    $numero_materia = pg_num_rows($result_materia);
    if ($numero_materia > 0) {
        for ($cont_materia = 0; $cont_materia < $numero_materia; $cont_materia++) {
            $voti_pagelline[$cont_materia][0] = trim(pg_fetch_result($result_materia, $cont_materia, "id_materia"));
            $voti_pagelline[$cont_materia][1] = trim(pg_fetch_result($result_materia, $cont_materia, "codice"));
            $voti_pagelline[$cont_materia][2] = trim(pg_fetch_result($result_materia, $cont_materia, "descrizione"));
            $voti_pagelline[$cont_materia]["descrizione"] = trim(pg_fetch_result($result_materia, $cont_materia, "descrizione"));
            $voti_pagelline[$cont_materia]['descrizione_materia_straniera'] = trim(decode(pg_fetch_result($result_materia, $cont_materia, "descrizione_materia_straniera")));
            $voti_pagelline[$cont_materia]["in_media_pagelle"] = trim(pg_fetch_result($result_materia, $cont_materia, "in_media_pagelle"));
            $voti_pagelline[$cont_materia]["tipo_materia"] = trim(pg_fetch_result($result_materia, $cont_materia, "tipo_materia"));
            $voti_pagelline[$cont_materia]["scritto"] = trim(pg_fetch_result($result_materia, $cont_materia, "scritto"));
            $voti_pagelline[$cont_materia]["orale"] = trim(pg_fetch_result($result_materia, $cont_materia, "orale"));
            $voti_pagelline[$cont_materia]["pratico"] = trim(pg_fetch_result($result_materia, $cont_materia, "pratico"));
            $voti_pagelline[$cont_materia]["descrizione_scuola_media"] = trim(pg_fetch_result($result_materia, $cont_materia, "descrizione_scuola_media"));
            $voti_pagelline[$cont_materia]["tipologia_aggregamento"] = trim(pg_fetch_result($result_materia, $cont_materia, "tipologia_aggregamento"));
            $voti_pagelline[$cont_materia][13] = $id_classe;
            $voti_pagelline[$cont_materia][14] = $periodo;
            $voti_pagelline[$cont_materia][15] = trim(pg_fetch_result($result_materia, $cont_materia, "indirizzo"));
            $voti_pagelline[$cont_materia][16] = trim(pg_fetch_result($result_materia, $cont_materia, "classe")) . trim(pg_fetch_result($result_materia, $cont_materia, "sezione"));
            $voti_pagelline[$cont_materia]["itp"] = trim(pg_fetch_result($result_materia, $cont_materia, "itp"));

            //estraggo la descrizione della materia che sto guardando
            $dati_materia = estrai_dati_materia($voti_pagelline[$cont_materia][0]);

            $significati_voto = estrai_significati_voti_pagelle($dati_materia[13], "solo_abilitati", $periodo);
            for ($cont_significati = 0; $cont_significati < count($significati_voto); $cont_significati++) {
                $voti_pagelline[$cont_materia][77][$cont_significati][0] = $significati_voto[$cont_significati][1];
                $voti_pagelline[$cont_materia][77][$cont_significati][1] = $significati_voto[$cont_significati][2];
                $voti_pagelline[$cont_materia][77][$cont_significati][2] = $significati_voto[$cont_significati][5];

                $voto_tmp = (string) $significati_voto[$cont_significati][1];
                $significati_materia[$voto_tmp]["codice"] = $significati_voto[$cont_significati][5];
                $significati_materia[$voto_tmp]["valore"] = $significati_voto[$cont_significati][2];
                $significati_materia[$voto_tmp]["valore_pagella"] = $significati_voto[$cont_significati][6];
                $significati_materia[$voto_tmp]["valore_pagella_lingua"] = $significati_voto[$cont_significati]['valore_pagella_lingua'];
            }

            if ($voti_pagelline[0][20] != "INESISTENTE") {
                $query_voto = "SELECT
									*
								FROM
									voti_pagelline
								WHERE
									voti_pagelline.id_pagellina='$id_pagellina'
									AND
									voti_pagelline.id_materia='" . $voti_pagelline[$cont_materia][0] . "'
									AND
									voti_pagelline.flag_canc=0";
                $result_voto = pgsql_query($query_voto) or die("Invalid $query_voto");
                $numero_voto = pg_num_rows($result_voto);
                if ($numero_voto > 0) {
                    //$voti_pagelline[$cont_materia] = pg_fetch_assoc($result_voto,0);
                    if ($voto_singolo_o_triplo == 'voto_singolo') {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo voto unico">
                        $voti_pagelline[$cont_materia][6] = trim(pg_fetch_result($result_voto, 0, "voto_pagellina"));
                        $voto_tmp0 = (string) $voti_pagelline[$cont_materia][6];
                        if (is_array($significati_materia[$voto_tmp0])) {
                            $voti_pagelline[$cont_materia][10] = $significati_materia[$voto_tmp0]["codice"];
                            $voti_pagelline[$cont_materia][1000] = $significati_materia[$voto_tmp0]["valore"];
                            $voti_pagelline[$cont_materia][110] = $significati_materia[$voto_tmp0]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_unico'] = $significati_materia[$voto_tmp0]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][10] = $voti_pagelline[$cont_materia][6];
                            $voti_pagelline[$cont_materia][110] = $voti_pagelline[$cont_materia][6];
                        }
                        //}}} </editor-fold>
                    } elseif ($voto_singolo_o_triplo == 'personalizzato') {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo voto unico E ANCHE scritto, orale, pratico">
                        $voti_pagelline[$cont_materia][6] = trim(pg_fetch_result($result_voto, 0, "voto_pagellina"));
                        $voto_tmp0 = (string) $voti_pagelline[$cont_materia][6];
                        if (is_array($significati_materia[$voto_tmp0])) {
                            $voti_pagelline[$cont_materia][10] = $significati_materia[$voto_tmp0]["codice"];
                            $voti_pagelline[$cont_materia][1000] = $significati_materia[$voto_tmp0]["valore"];
                            $voti_pagelline[$cont_materia][110] = $significati_materia[$voto_tmp0]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_unico'] = $significati_materia[$voto_tmp0]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][10] = $voti_pagelline[$cont_materia][6];
                            $voti_pagelline[$cont_materia][110] = $voti_pagelline[$cont_materia][6];
                        }

                        $voti_pagelline[$cont_materia][50] = trim(pg_fetch_result($result_voto, 0, "voto_scritto_pagella"));
                        $voto_tmp1 = (string) $voti_pagelline[$cont_materia][50];
                        if (is_array($significati_materia[$voto_tmp1])) {
                            $voti_pagelline[$cont_materia][60] = $significati_materia[$voto_tmp1]["codice"];
                            $voti_pagelline[$cont_materia][6000] = $significati_materia[$voto_tmp1]["valore"];
                            $voti_pagelline[$cont_materia][160] = $significati_materia[$voto_tmp1]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_scritto'] = $significati_materia[$voto_tmp1]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][60] = $voti_pagelline[$cont_materia][50];
                            $voti_pagelline[$cont_materia][160] = $voti_pagelline[$cont_materia][50];
                        }

                        $voti_pagelline[$cont_materia][51] = trim(pg_fetch_result($result_voto, 0, "voto_orale_pagella"));
                        $voto_tmp2 = (string) $voti_pagelline[$cont_materia][51];
                        if (is_array($significati_materia[$voto_tmp2])) {
                            $voti_pagelline[$cont_materia][61] = $significati_materia[$voto_tmp2]["codice"];
                            $voti_pagelline[$cont_materia][6100] = $significati_materia[$voto_tmp2]["valore"];
                            $voti_pagelline[$cont_materia][161] = $significati_materia[$voto_tmp2]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_orale'] = $significati_materia[$voto_tmp2]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][61] = $voti_pagelline[$cont_materia][51];
                            $voti_pagelline[$cont_materia][161] = $voti_pagelline[$cont_materia][51];
                        }

                        $voti_pagelline[$cont_materia][52] = trim(pg_fetch_result($result_voto, 0, "voto_pratico_pagella"));
                        $voto_tmp3 = (string) $voti_pagelline[$cont_materia][52];
                        if (is_array($significati_materia[$voto_tmp3])) {
                            $voti_pagelline[$cont_materia][62] = $significati_materia[$voto_tmp3]["codice"];
                            $voti_pagelline[$cont_materia][6200] = $significati_materia[$voto_tmp3]["valore"];
                            $voti_pagelline[$cont_materia][162] = $significati_materia[$voto_tmp3]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_pratico'] = $significati_materia[$voto_tmp3]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][62] = $voti_pagelline[$cont_materia][52];
                            $voti_pagelline[$cont_materia][162] = $voti_pagelline[$cont_materia][52];
                        }
                        //}}} </editor-fold>
                    } else {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo scritto, orale, pratico">
                        $voti_pagelline[$cont_materia][50] = trim(pg_fetch_result($result_voto, 0, "voto_scritto_pagella"));
                        $voto_tmp1 = (string) $voti_pagelline[$cont_materia][50];
                        if (is_array($significati_materia[$voto_tmp1])) {
                            $voti_pagelline[$cont_materia][60] = $significati_materia[$voto_tmp1]["codice"];
                            $voti_pagelline[$cont_materia][6000] = $significati_materia[$voto_tmp1]["valore"];
                            $voti_pagelline[$cont_materia][160] = $significati_materia[$voto_tmp1]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_scritto'] = $significati_materia[$voto_tmp1]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][60] = $voti_pagelline[$cont_materia][50];
                            $voti_pagelline[$cont_materia][160] = $voti_pagelline[$cont_materia][50];
                        }

                        $voti_pagelline[$cont_materia][51] = trim(pg_fetch_result($result_voto, 0, "voto_orale_pagella"));
                        $voto_tmp2 = (string) $voti_pagelline[$cont_materia][51];
                        if (is_array($significati_materia[$voto_tmp2])) {
                            $voti_pagelline[$cont_materia][61] = $significati_materia[$voto_tmp2]["codice"];
                            $voti_pagelline[$cont_materia][6100] = $significati_materia[$voto_tmp2]["valore"];
                            $voti_pagelline[$cont_materia][161] = $significati_materia[$voto_tmp2]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_orale'] = $significati_materia[$voto_tmp2]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][61] = $voti_pagelline[$cont_materia][51];
                            $voti_pagelline[$cont_materia][161] = $voti_pagelline[$cont_materia][51];
                        }

                        $voti_pagelline[$cont_materia][52] = trim(pg_fetch_result($result_voto, 0, "voto_pratico_pagella"));
                        $voto_tmp3 = (string) $voti_pagelline[$cont_materia][52];
                        if (is_array($significati_materia[$voto_tmp3])) {
                            $voti_pagelline[$cont_materia][62] = $significati_materia[$voto_tmp3]["codice"];
                            $voti_pagelline[$cont_materia][6200] = $significati_materia[$voto_tmp3]["valore"];
                            $voti_pagelline[$cont_materia][162] = $significati_materia[$voto_tmp3]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_pratico'] = $significati_materia[$voto_tmp3]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][62] = $voti_pagelline[$cont_materia][52];
                            $voti_pagelline[$cont_materia][162] = $voti_pagelline[$cont_materia][52];
                        }
                        //}}} </editor-fold>
                    }
                    $voti_pagelline[$cont_materia][5] = trim(pg_fetch_result($result_voto, 0, "id_voto_pagellina"));
                    $voti_pagelline[$cont_materia][7] = trim(pg_fetch_result($result_voto, 0, "debito"));
                    $voti_pagelline[$cont_materia][8] = trim(pg_fetch_result($result_voto, 0, "id_materia"));
                    $voti_pagelline[$cont_materia][11] = trim(pg_fetch_result($result_voto, 0, "data"));
                    $voti_pagelline[$cont_materia][12] = trim(pg_fetch_result($result_voto, 0, "ore_assenza"));
                    $voti_pagelline[$cont_materia]['ore_assenza'] = trim(pg_fetch_result($result_voto, 0, "ore_assenza"));
                    $voti_pagelline[$cont_materia]['monteore_totale'] = trim(pg_fetch_result($result_voto, 0, "monteore_totale"));
                    $voti_pagelline[$cont_materia]['esito_recupero'] = trim(pg_fetch_result($result_voto, 0, "esito_recupero"));
                    if ($voti_pagelline[$cont_materia]['esito_recupero'] == 'SI') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Positivo';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'NO') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Negativo';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'ASSENTE') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Assente';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'NI') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Parziale';
                    }
                    $voti_pagelline[$cont_materia]['giudizio_analitico'] = trim(pg_fetch_result($result_voto, 0, "giudizio_analitico"));
                    $voti_pagelline[$cont_materia]['tipo_recupero'] = trim(pg_fetch_result($result_voto, 0, "tipo_recupero"));
                    $voti_pagelline[$cont_materia]['tipo_recupero_tradotto'] = estrai_tipo_recupero_singolo($voti_pagelline[$cont_materia]['tipo_recupero']);
                } else {
                    $voti_pagelline[$cont_materia][5] = "INESISTENTE";
                }
            } else {
                $voti_pagelline[$cont_materia][5] = "INESISTENTE";
            }

            foreach ($voti_pagelline[$cont_materia] as $key => $value) {
                if (!is_array($value)) {
                    $voti_pagelline[$cont_materia][$key] = decode($value);
                }
            }
        }
    }
    return $voti_pagelline;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_studente_vecchio_multi_classe($id_classe, $periodo, $id_studente, $voto_singolo_o_triplo = "voto_singolo", $tipo = 'classe') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella">
    $elenco_materie_nuovo = [];

    if ($tipo == 'studente') {
        $elenco_materie = estrai_materie_multi_classe_studente($id_studente);
    } else {
        $elenco_materie = estrai_materie_multi_classe($id_classe);
    }

    if (is_array($elenco_materie)) {
        foreach ($elenco_materie as $materia) {
            if ($materia["in_media_pagelle"] != 'NV') {
                $elenco_materie_nuovo[] = $materia;
            }
        }
    }

    $elenco_materie = $elenco_materie_nuovo;
    $dati_classe = estrai_classe($id_classe);

    $query_pag = "SELECT id_pagellina
					FROM pagelline
					WHERE id_studente = {$id_studente}
						AND periodo = '{$periodo}'
						AND flag_canc = 0";
    $result_pag = pgsql_query($query_pag) or die("Invalid $query_pag");
    $numero_pag = pg_num_rows($result_pag);

    if ($numero_pag > 0) {
        $voti_pagelline[0][20] = trim(pg_fetch_result($result_pag, 0, "id_pagellina"));
        $id_pagellina = trim(pg_fetch_result($result_pag, 0, "id_pagellina"));
    } else {
        $voti_pagelline[0][20] = "INESISTENTE";
    }

    if (count($elenco_materie) > 0) {
        for ($cont_materia = 0; $cont_materia < count($elenco_materie); $cont_materia++) {
            //{{{ <editor-fold defaultstate="collapsed">
            $voti_pagelline[$cont_materia][0] = $elenco_materie[$cont_materia]["id_materia"];
            $voti_pagelline[$cont_materia][1] = $elenco_materie[$cont_materia]["codice"];
            $voti_pagelline[$cont_materia][2] = $elenco_materie[$cont_materia]["descrizione"];
            $voti_pagelline[$cont_materia]['descrizione'] = $elenco_materie[$cont_materia]["descrizione"];
            $voti_pagelline[$cont_materia]['descrizione_materia_straniera'] = $elenco_materie[$cont_materia]["descrizione_materia_straniera"];
            $voti_pagelline[$cont_materia]["in_media_pagelle"] = $elenco_materie[$cont_materia]["in_media_pagelle"];
            $voti_pagelline[$cont_materia]["tipologia_aggregamento"] = $elenco_materie[$cont_materia]["tipologia_aggregamento"];
            $voti_pagelline[$cont_materia]["tipo_materia"] = $elenco_materie[$cont_materia]["tipo_materia"];
            $voti_pagelline[$cont_materia]["scritto"] = $elenco_materie[$cont_materia]["scritto"];
            $voti_pagelline[$cont_materia]["orale"] = $elenco_materie[$cont_materia]["orale"];
            $voti_pagelline[$cont_materia]["pratico"] = $elenco_materie[$cont_materia]["pratico"];
            $voti_pagelline[$cont_materia]["itp"] = $elenco_materie[$cont_materia]["itp"];
            $voti_pagelline[$cont_materia]["descrizione_scuola_media"] = $elenco_materie[$cont_materia]["descrizione_scuola_media"];
            $voti_pagelline[$cont_materia][13] = $id_classe;
            $voti_pagelline[$cont_materia][14] = $periodo;
            $voti_pagelline[$cont_materia][15] = $dati_classe["descrizione"];
            $voti_pagelline[$cont_materia][16] = $dati_classe["classe"] . $dati_classe["sezione"];

            $significati_voto = estrai_significati_voti_pagelle($elenco_materie[$cont_materia]["tipo_valutazione"], "solo_abilitati", $periodo);

            for ($cont_significati = 0; $cont_significati < count($significati_voto); $cont_significati++) {
                $voti_pagelline[$cont_materia][77][$cont_significati][0] = $significati_voto[$cont_significati][1];
                $voti_pagelline[$cont_materia][77][$cont_significati][1] = $significati_voto[$cont_significati][2];
                $voti_pagelline[$cont_materia][77][$cont_significati][2] = $significati_voto[$cont_significati][5];
                $voto_tmp = (string) $significati_voto[$cont_significati][1];
                $significati_materia[$voto_tmp]["codice"] = $significati_voto[$cont_significati][5];
                $significati_materia[$voto_tmp]["valore"] = $significati_voto[$cont_significati][2];
                $significati_materia[$voto_tmp]["valore_pagella"] = $significati_voto[$cont_significati][6];
                $significati_materia[$voto_tmp]["valore_pagella_lingua"] = $significati_voto[$cont_significati]['valore_pagella_lingua'];
            }

            if ($voti_pagelline[0][20] != "INESISTENTE") {
                $query_voto = "SELECT *
								FROM voti_pagelline
								WHERE id_pagellina = {$id_pagellina}
									AND id_materia = {$voti_pagelline[$cont_materia][0]}
									AND flag_canc = 0";
                $result_voto = pgsql_query($query_voto) or die("Invalid $query_voto");
                $numero_voto = pg_num_rows($result_voto);

                if ($numero_voto > 0) {
                    if ($voto_singolo_o_triplo == "voto_singolo") {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo voto unico">
                        $voti_pagelline[$cont_materia][6] = trim(pg_fetch_result($result_voto, 0, "voto_pagellina"));
                        $voto_tmp0 = (string) $voti_pagelline[$cont_materia][6];

                        if (is_array($significati_materia[$voto_tmp0])) {
                            $voti_pagelline[$cont_materia][10] = $significati_materia[$voto_tmp0]["codice"];
                            $voti_pagelline[$cont_materia][1000] = $significati_materia[$voto_tmp0]["valore"];
                            $voti_pagelline[$cont_materia][110] = $significati_materia[$voto_tmp0]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_unico'] = $significati_materia[$voto_tmp0]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][10] = $voti_pagelline[$cont_materia][6];
                            $voti_pagelline[$cont_materia][110] = $voti_pagelline[$cont_materia][6];
                        }
                        //}}} </editor-fold>
                    } elseif ($voto_singolo_o_triplo == 'personalizzato') {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo voto unico e anche scritto, orale, pratico">
                        $voti_pagelline[$cont_materia][6] = trim(pg_fetch_result($result_voto, 0, "voto_pagellina"));
                        $voto_tmp0 = (string) $voti_pagelline[$cont_materia][6];

                        if (is_array($significati_materia[$voto_tmp0])) {
                            $voti_pagelline[$cont_materia][10] = $significati_materia[$voto_tmp0]["codice"];
                            $voti_pagelline[$cont_materia][1000] = $significati_materia[$voto_tmp0]["valore"];
                            $voti_pagelline[$cont_materia][110] = $significati_materia[$voto_tmp0]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_unico'] = $significati_materia[$voto_tmp0]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][10] = $voti_pagelline[$cont_materia][6];
                            $voti_pagelline[$cont_materia][110] = $voti_pagelline[$cont_materia][6];
                        }

                        $voti_pagelline[$cont_materia][50] = trim(pg_fetch_result($result_voto, 0, "voto_scritto_pagella"));
                        $voto_tmp1 = (string) $voti_pagelline[$cont_materia][50];

                        if (is_array($significati_materia[$voto_tmp1])) {
                            $voti_pagelline[$cont_materia][60] = $significati_materia[$voto_tmp1]["codice"];
                            $voti_pagelline[$cont_materia][6000] = $significati_materia[$voto_tmp1]["valore"];
                            $voti_pagelline[$cont_materia][160] = $significati_materia[$voto_tmp1]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_scritto'] = $significati_materia[$voto_tmp1]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][60] = $voti_pagelline[$cont_materia][50];
                            $voti_pagelline[$cont_materia][160] = $voti_pagelline[$cont_materia][50];
                        }

                        $voti_pagelline[$cont_materia][51] = trim(pg_fetch_result($result_voto, 0, "voto_orale_pagella"));
                        $voto_tmp2 = (string) $voti_pagelline[$cont_materia][51];

                        if (is_array($significati_materia[$voto_tmp2])) {
                            $voti_pagelline[$cont_materia][61] = $significati_materia[$voto_tmp2]["codice"];
                            $voti_pagelline[$cont_materia][6100] = $significati_materia[$voto_tmp2]["valore"];
                            $voti_pagelline[$cont_materia][161] = $significati_materia[$voto_tmp2]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_orale'] = $significati_materia[$voto_tmp2]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][61] = $voti_pagelline[$cont_materia][51];
                            $voti_pagelline[$cont_materia][161] = $voti_pagelline[$cont_materia][51];
                        }

                        $voti_pagelline[$cont_materia][52] = trim(pg_fetch_result($result_voto, 0, "voto_pratico_pagella"));
                        $voto_tmp3 = (string) $voti_pagelline[$cont_materia][52];

                        if (is_array($significati_materia[$voto_tmp3])) {
                            $voti_pagelline[$cont_materia][62] = $significati_materia[$voto_tmp3]["codice"];
                            $voti_pagelline[$cont_materia][6200] = $significati_materia[$voto_tmp3]["valore"];
                            $voti_pagelline[$cont_materia][162] = $significati_materia[$voto_tmp3]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_pratico'] = $significati_materia[$voto_tmp3]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][62] = $voti_pagelline[$cont_materia][52];
                            $voti_pagelline[$cont_materia][162] = $voti_pagelline[$cont_materia][52];
                        }
                        //}}} </editor-fold>
                    } else {
                        //{{{ <editor-fold defaultstate="collapsed" desc="Valorizzo scritto, orale, pratico">
                        $voti_pagelline[$cont_materia][50] = trim(pg_fetch_result($result_voto, 0, "voto_scritto_pagella"));
                        $voto_tmp1 = (string) $voti_pagelline[$cont_materia][50];

                        if (is_array($significati_materia[$voto_tmp1])) {
                            $voti_pagelline[$cont_materia][60] = $significati_materia[$voto_tmp1]["codice"];
                            $voti_pagelline[$cont_materia][6000] = $significati_materia[$voto_tmp1]["valore"];
                            $voti_pagelline[$cont_materia][160] = $significati_materia[$voto_tmp1]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_scritto'] = $significati_materia[$voto_tmp1]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][60] = $voti_pagelline[$cont_materia][50];
                            $voti_pagelline[$cont_materia][160] = $voti_pagelline[$cont_materia][50];
                        }

                        $voti_pagelline[$cont_materia][51] = trim(pg_fetch_result($result_voto, 0, "voto_orale_pagella"));
                        $voto_tmp2 = (string) $voti_pagelline[$cont_materia][51];

                        if (is_array($significati_materia[$voto_tmp2])) {
                            $voti_pagelline[$cont_materia][61] = $significati_materia[$voto_tmp2]["codice"];
                            $voti_pagelline[$cont_materia][6100] = $significati_materia[$voto_tmp2]["valore"];
                            $voti_pagelline[$cont_materia][161] = $significati_materia[$voto_tmp2]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_orale'] = $significati_materia[$voto_tmp2]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][61] = $voti_pagelline[$cont_materia][51];
                            $voti_pagelline[$cont_materia][161] = $voti_pagelline[$cont_materia][51];
                        }

                        $voti_pagelline[$cont_materia][52] = trim(pg_fetch_result($result_voto, 0, "voto_pratico_pagella"));
                        $voto_tmp3 = (string) $voti_pagelline[$cont_materia][52];

                        if (is_array($significati_materia[$voto_tmp3])) {
                            $voti_pagelline[$cont_materia][62] = $significati_materia[$voto_tmp3]["codice"];
                            $voti_pagelline[$cont_materia][6200] = $significati_materia[$voto_tmp3]["valore"];
                            $voti_pagelline[$cont_materia][162] = $significati_materia[$voto_tmp3]["valore_pagella"];
                            $voti_pagelline[$cont_materia]['valore_pagella_lingua_voto_pratico'] = $significati_materia[$voto_tmp3]["valore_pagella_lingua"];
                        } else {
                            $voti_pagelline[$cont_materia][62] = $voti_pagelline[$cont_materia][52];
                            $voti_pagelline[$cont_materia][162] = $voti_pagelline[$cont_materia][52];
                        }
                        //}}} </editor-fold>
                    }

                    $voti_pagelline[$cont_materia][5] = trim(pg_fetch_result($result_voto, 0, "id_voto_pagellina"));
                    $voti_pagelline[$cont_materia][7] = trim(pg_fetch_result($result_voto, 0, "debito"));
                    $voti_pagelline[$cont_materia][8] = trim(pg_fetch_result($result_voto, 0, "id_materia"));
                    $voti_pagelline[$cont_materia][11] = trim(pg_fetch_result($result_voto, 0, "data"));
                    $voti_pagelline[$cont_materia][12] = trim(pg_fetch_result($result_voto, 0, "ore_assenza"));
                    $voti_pagelline[$cont_materia]['ore_assenza'] = trim(pg_fetch_result($result_voto, 0, "ore_assenza"));
                    $voti_pagelline[$cont_materia]['monteore_totale'] = trim(pg_fetch_result($result_voto, 0, "monteore_totale"));
                    $voti_pagelline[$cont_materia]['esito_recupero'] = trim(pg_fetch_result($result_voto, 0, "esito_recupero"));

                    if ($voti_pagelline[$cont_materia]['esito_recupero'] == 'SI') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Positivo';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'NO') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Negativo';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'ASSENTE') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Assente';
                    } elseif ($voti_pagelline[$cont_materia]['esito_recupero'] == 'NI') {
                        $voti_pagelline[$cont_materia]['esito_recupero_tradotto'] = 'Parziale';
                    }

                    $voti_pagelline[$cont_materia]['giudizio_analitico'] = trim(pg_fetch_result($result_voto, 0, "giudizio_analitico"));
                    $voti_pagelline[$cont_materia]['tipo_recupero'] = trim(pg_fetch_result($result_voto, 0, "tipo_recupero"));
                    $voti_pagelline[$cont_materia]['tipo_recupero_tradotto'] = estrai_tipo_recupero_singolo($voti_pagelline[$cont_materia]['tipo_recupero']);
                } else {
                    $voti_pagelline[$cont_materia][5] = "INESISTENTE";
                }
            } else {
                $voti_pagelline[$cont_materia][5] = "INESISTENTE";
            }

            foreach ($voti_pagelline[$cont_materia] as $key => $value) {
                if (!is_array($value)) {
                    $voti_pagelline[$cont_materia][$key] = decode($value);
                }
            }
            //}}} </editor-fold>
        }
    }

    return $voti_pagelline;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_studente($id_classe, $periodo, $id_studente, $voto_singolo_o_triplo = "voto_singolo", $id_professore = "non_prof") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella">
    $campi_liberi = estrai_codici_campi_liberi_con_valori($periodo);

    if ($id_professore == "non_prof") {
        $materie = estrai_materie_classe($id_classe);
    } else {
        $coordinatore = verifica_coordinatore_classe($id_classe, $id_professore);

        if ($coordinatore == "SI") {
            $materie = estrai_materie_classe($id_classe);
        } else {
            $materie = estrai_materie_classe_del_professore($id_classe, $id_professore);
        }
    }

    if (is_array($materie)) {
        foreach ($materie as $indice => $materia) {
            $dati_totali[$materia["id_materia"]] = $materie[$indice];
            $significati_voto = estrai_significati_voti_pagelle($materia["tipo_valutazione"], "solo_abilitati", $periodo);
            $dati_totali[$materia["id_materia"]]["significati_voto"] = $significati_voto;
        }
    }
    $query_voto = "SELECT
						voti_pagelline.*,
						materie.scritto,
						materie.orale,
						materie.pratico,
						materie.descrizione,
						materie.descrizione_materia_straniera,
						materie.codice,
						materie.tipo_materia,
						materie.descrizione_scuola_media,
						materie.tipologia_aggregamento,
						materie.in_media_pagelle,
						materie.tipo_voto_personalizzato,
						materie.nome_materia_sito,
						materie.nome_materia_breve
					FROM
						pagelline
						INNER JOIN voti_pagelline ON pagelline.id_pagellina=voti_pagelline.id_pagellina
						INNER JOIN materie ON voti_pagelline.id_materia=materie.id_materia
					WHERE
						pagelline.periodo='$periodo'
						AND
						pagelline.id_studente='$id_studente'
						AND
						pagelline.flag_canc=0
						AND
						voti_pagelline.flag_canc=0
						AND
						materie.flag_canc=0
					ORDER BY
						coalesce(cast(nullif(materie.ordinamento,'') as integer),0)";
    $result_voto = pgsql_query($query_voto) or die("Invalid $query_voto");
    $numero_voto = pg_num_rows($result_voto);
    if ($numero_voto > 0) {
        for ($cont = 0; $cont < $numero_voto; $cont++) {
            $id_materia_temp = trim(pg_fetch_result($result_voto, $cont, "id_materia"));
            if ($dati_totali[$id_materia_temp] != "") {
                $id_voto_pagellina = pg_fetch_result($result_voto, $cont, "id_voto_pagellina");
                $dati_totali[$id_materia_temp]["id_voto_pagellina"] = pg_fetch_result($result_voto, $cont, "id_voto_pagellina");
                $dati_totali[$id_materia_temp]["id_pagellina"] = pg_fetch_result($result_voto, $cont, "id_pagellina");
                $dati_totali[$id_materia_temp]["id_materia"] = pg_fetch_result($result_voto, $cont, "id_materia");
                $dati_totali[$id_materia_temp]["tipologia_aggregamento"] = pg_fetch_result($result_voto, $cont, "tipologia_aggregamento");
                $dati_totali[$id_materia_temp]["voto_pagellina"] = pg_fetch_result($result_voto, $cont, "voto_pagellina");
                $dati_totali[$id_materia_temp]["voto_scritto_pagella"] = pg_fetch_result($result_voto, $cont, "voto_scritto_pagella");
                $dati_totali[$id_materia_temp]["voto_orale_pagella"] = pg_fetch_result($result_voto, $cont, "voto_orale_pagella");
                $dati_totali[$id_materia_temp]["voto_pratico_pagella"] = pg_fetch_result($result_voto, $cont, "voto_pratico_pagella");
                $dati_totali[$id_materia_temp]["ore_assenza"] = pg_fetch_result($result_voto, $cont, "ore_assenza");
                $dati_totali[$id_materia_temp]["scritto"] = pg_fetch_result($result_voto, $cont, "scritto");
                $dati_totali[$id_materia_temp]["orale"] = pg_fetch_result($result_voto, $cont, "orale");
                $dati_totali[$id_materia_temp]["pratico"] = pg_fetch_result($result_voto, $cont, "pratico");
                $dati_totali[$id_materia_temp]["descrizione_materia"] = decode(pg_fetch_result($result_voto, $cont, "descrizione"));
                $dati_totali[$id_materia_temp]["descrizione_materia_straniera"] = decode(pg_fetch_result($result_voto, $cont, "descrizione_materia_straniera"));
                $dati_totali[$id_materia_temp]['in_media_pagelle'] = pg_fetch_result($result_voto, $cont, "in_media_pagelle");
                $dati_totali[$id_materia_temp]["codice_materia"] = pg_fetch_result($result_voto, $cont, "codice");
                $dati_totali[$id_materia_temp]["tipo_materia"] = pg_fetch_result($result_voto, $cont, "tipo_materia");
                $dati_totali[$id_materia_temp]["giudizio_analitico"] = decode(pg_fetch_result($result_voto, $cont, "giudizio_analitico"));
                $dati_totali[$id_materia_temp]["tipo_recupero"] = pg_fetch_result($result_voto, $cont, "tipo_recupero");
                $dati_totali[$id_materia_temp]["esito_recupero"] = pg_fetch_result($result_voto, $cont, "esito_recupero");
                $dati_totali[$id_materia_temp]["tipo_voto_personalizzato"] = pg_fetch_result($result_voto, $cont, "tipo_voto_personalizzato");
                $dati_totali[$id_materia_temp]["nome_materia_sito"] = decode(pg_fetch_result($result_voto, $cont, "nome_materia_sito"));
                $dati_totali[$id_materia_temp]["nome_materia_breve"] = decode(pg_fetch_result($result_voto, $cont, "nome_materia_breve"));

                if ($dati_totali[$id_materia_temp]['esito_recupero'] == 'SI') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Positivo';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'NO') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Negativo';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'ASSENTE') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Assente';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'NI') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Parziale';
                }
                $dati_totali[$id_materia_temp]["descrizione_scuola_media"] = pg_fetch_result($result_voto, $cont, "descrizione_scuola_media");
                $dati_totali[$id_materia_temp]['tipo_recupero_tradotto'] = estrai_tipo_recupero_singolo($dati_totali[$id_materia_temp]['tipo_recupero']);

                $elenco_valori = estrai_valori_campi_liberi_materia_studente_per_stampa($id_voto_pagellina);
                $campi_liberi_temp = $campi_liberi;
                if (is_array($elenco_valori)) {
                    foreach ($elenco_valori as $valore) {
                        if ($valore["id_valore_precomp"] != "-1") {
                            if (strlen($valore["id_valore_precomp"]) > 0) {
                                $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"][$valore["id_valore_precomp"]]["selezionato"] = "SI";
                            }
                        } else {
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["id_valore_precomp"] = "-1";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["selezionato"] = "SI";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["valore_testuale"] = $valore["valore_testuale"];
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["id_valore_precomp"] = "meno_uno";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["valore_testuale"] = $valore["valore_testuale"];
                        }
                        $campi_liberi_temp[$valore["id_campo_libero"]]['id_valore_campo_libero'] = $valore['id_valore_campo_libero'];
                    }
                }
                $dati_totali[$id_materia_temp]["campi_liberi"] = $campi_liberi_temp;
            }
        }
    }
    return $dati_totali;
    //}}} </editor-fold>
}

function estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo, $id_studente, $voto_singolo_o_triplo = "voto_singolo", $id_professore = "non_prof") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella">
    $campi_liberi = estrai_codici_campi_liberi_con_valori($periodo);

    if ($id_professore == "non_prof") {
        $materie = estrai_materie_multi_classe_studente($id_studente);
    } else {
        $coordinatore = verifica_coordinatore_classe($id_classe, $id_professore);

        if ($coordinatore == "SI") {
            $materie = estrai_materie_multi_classe_studente($id_studente);
        } else {
            $materie = estrai_materie_multi_classe_studente_del_professore($id_studente, $id_professore);
        }
    }

    if (is_array($materie)) {
        foreach ($materie as $indice => $materia) {
            $dati_totali[$materia["id_materia"]] = $materie[$indice];
            $significati_voto = estrai_significati_voti_pagelle($materia["tipo_valutazione"], "solo_abilitati", $periodo);
            $dati_totali[$materia["id_materia"]]["significati_voto"] = $significati_voto;
        }
    }

    $query_voto = "SELECT
						voti_pagelline.*,
						materie.scritto,
						materie.orale,
						materie.pratico,
						materie.descrizione,
						materie.descrizione_materia_straniera,
						materie.codice,
						materie.tipo_materia,
						materie.in_media_pagelle,
						materie.descrizione_scuola_media,
						materie.tipologia_aggregamento,
						materie.tipo_voto_personalizzato,
						materie.nome_materia_sito,
						materie.nome_materia_breve
					FROM
						pagelline
						INNER JOIN voti_pagelline ON pagelline.id_pagellina=voti_pagelline.id_pagellina
						INNER JOIN materie ON voti_pagelline.id_materia=materie.id_materia
					WHERE
						pagelline.periodo = '$periodo'
						AND
						pagelline.id_studente = {$id_studente}
						AND
						pagelline.flag_canc = 0
						AND
						voti_pagelline.flag_canc = 0
						AND
						materie.flag_canc = 0
					ORDER BY COALESCE(cast(nullif(materie.ordinamento,'') as integer),0)";

    $result_voto = pgsql_query($query_voto) or die("Invalid $query_voto");
    $numero_voto = pg_num_rows($result_voto);

    if ($numero_voto > 0) {
        for ($cont = 0; $cont < $numero_voto; $cont++) {
            $id_materia_temp = trim(pg_fetch_result($result_voto, $cont, "id_materia"));
            if ($dati_totali[$id_materia_temp] != "") {
                $id_voto_pagellina = trim(pg_fetch_result($result_voto, $cont, "id_voto_pagellina"));
                $dati_totali[$id_materia_temp]["id_voto_pagellina"] = pg_fetch_result($result_voto, $cont, "id_voto_pagellina");
                $dati_totali[$id_materia_temp]["id_pagellina"] = pg_fetch_result($result_voto, $cont, "id_pagellina");
                $dati_totali[$id_materia_temp]["id_materia"] = pg_fetch_result($result_voto, $cont, "id_materia");
                $dati_totali[$id_materia_temp]["tipologia_aggregamento"] = pg_fetch_result($result_voto, $cont, "tipologia_aggregamento");
                $dati_totali[$id_materia_temp]["voto_pagellina"] = pg_fetch_result($result_voto, $cont, "voto_pagellina");
                $dati_totali[$id_materia_temp]["voto_scritto_pagella"] = pg_fetch_result($result_voto, $cont, "voto_scritto_pagella");
                $dati_totali[$id_materia_temp]["voto_orale_pagella"] = pg_fetch_result($result_voto, $cont, "voto_orale_pagella");
                $dati_totali[$id_materia_temp]["voto_pratico_pagella"] = pg_fetch_result($result_voto, $cont, "voto_pratico_pagella");
                $dati_totali[$id_materia_temp]["proposta_voto_pagellina"] = pg_fetch_result($result_voto, $cont, "proposta_voto_pagellina");
                $dati_totali[$id_materia_temp]["proposta_voto_scritto_pagella"] = pg_fetch_result($result_voto, $cont, "proposta_voto_scritto_pagella");
                $dati_totali[$id_materia_temp]["proposta_voto_orale_pagella"] = pg_fetch_result($result_voto, $cont, "proposta_voto_orale_pagella");
                $dati_totali[$id_materia_temp]["proposta_voto_pratico_pagella"] = pg_fetch_result($result_voto, $cont, "proposta_voto_pratico_pagella");
                $dati_totali[$id_materia_temp]["ore_assenza"] = pg_fetch_result($result_voto, $cont, "ore_assenza");
                $dati_totali[$id_materia_temp]["monteore_totale"] = pg_fetch_result($result_voto, $cont, "monteore_totale");
                $dati_totali[$id_materia_temp]["scritto"] = pg_fetch_result($result_voto, $cont, "scritto");
                $dati_totali[$id_materia_temp]["orale"] = pg_fetch_result($result_voto, $cont, "orale");
                $dati_totali[$id_materia_temp]["pratico"] = pg_fetch_result($result_voto, $cont, "pratico");
                $dati_totali[$id_materia_temp]["descrizione_materia"] = decode(pg_fetch_result($result_voto, $cont, "descrizione"));
                $dati_totali[$id_materia_temp]["descrizione_materia_straniera"] = decode(pg_fetch_result($result_voto, $cont, "descrizione_materia_straniera"));
                $dati_totali[$id_materia_temp]["codice_materia"] = pg_fetch_result($result_voto, $cont, "codice");
                $dati_totali[$id_materia_temp]["tipo_materia"] = pg_fetch_result($result_voto, $cont, "tipo_materia");
                $dati_totali[$id_materia_temp]["in_media_pagelle"] = pg_fetch_result($result_voto, $cont, "in_media_pagelle");
                $dati_totali[$id_materia_temp]["giudizio_analitico"] = decode(pg_fetch_result($result_voto, $cont, "giudizio_analitico"));
                $dati_totali[$id_materia_temp]["tipo_recupero"] = pg_fetch_result($result_voto, $cont, "tipo_recupero");
                $dati_totali[$id_materia_temp]["esito_recupero"] = pg_fetch_result($result_voto, $cont, "esito_recupero");
                $dati_totali[$id_materia_temp]["tipo_voto_personalizzato"] = pg_fetch_result($result_voto, $cont, "tipo_voto_personalizzato");
                $dati_totali[$id_materia_temp]["nome_materia_sito"] = decode(pg_fetch_result($result_voto, $cont, "nome_materia_sito"));
                $dati_totali[$id_materia_temp]["nome_materia_breve"] = decode(pg_fetch_result($result_voto, $cont, "nome_materia_breve"));

                if ($dati_totali[$id_materia_temp]['esito_recupero'] == 'SI') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Positivo';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'NO') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Negativo';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'ASSENTE') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Assente';
                } elseif ($dati_totali[$id_materia_temp]['esito_recupero'] == 'NI') {
                    $dati_totali[$id_materia_temp]['esito_recupero_tradotto'] = 'Parziale';
                }

                $dati_totali[$id_materia_temp]["descrizione_scuola_media"] = pg_fetch_result($result_voto, $cont, "descrizione_scuola_media");
                $dati_totali[$id_materia_temp]['tipo_recupero_tradotto'] = estrai_tipo_recupero_singolo($dati_totali[$id_materia_temp]['tipo_recupero']);

                $elenco_valori = estrai_valori_campi_liberi_materia_studente_per_stampa($id_voto_pagellina);
                $campi_liberi_temp = $campi_liberi;

                if (is_array($elenco_valori)) {
                    foreach ($elenco_valori as $valore) {
                        if ($valore["id_valore_precomp"] != "-1") {
                            if (strlen($valore["id_valore_precomp"]) > 0) {
                                $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"][$valore["id_valore_precomp"]]["selezionato"] = "SI";
                                $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["peso"] = $valore["peso"];
                            }
                        } else {
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["id_valore_precomp"] = "-1";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["selezionato"] = "SI";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["-1"]["valore_testuale"] = $valore["valore_testuale"];
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["id_valore_precomp"] = "meno_uno";
                            $campi_liberi_temp[$valore["id_campo_libero"]]["valori_precomp"]["meno_uno"]["valore_testuale"] = $valore["valore_testuale"];
                        }
                    }
                }
                $dati_totali[$id_materia_temp]["campi_liberi"] = $campi_liberi_temp;
            }
        }
    }

    return $dati_totali;
    //}}} </editor-fold>
}

function estrai_voto_pagellina($id_voto_pagellina) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre un record della tabella voti_pagelline">
    $query = "SELECT
					*
				FROM
					voti_pagelline
				WHERE
					id_voto_pagellina='$id_voto_pagellina'
					AND
					flag_canc=0";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $voto_pagellina = pg_fetch_assoc($result, 0);
        $voto_pagellina[0] = trim(pg_fetch_result($result, 0, "id_voto_pagellina"));
        $voto_pagellina[1] = trim(pg_fetch_result($result, 0, "voto_pagellina"));
        $voto_pagellina[3] = trim(pg_fetch_result($result, 0, "debito"));
        $voto_pagellina[4] = trim(pg_fetch_result($result, 0, "id_materia"));
        $voto_pagellina[5] = trim(pg_fetch_result($result, 0, "id_pagellina"));
        $voto_pagellina[6] = trim(pg_fetch_result($result, 0, "ore_assenza"));
        $voto_pagellina[7] = trim(pg_fetch_result($result, 0, "data"));
        $voto_pagellina[8] = trim(pg_fetch_result($result, 0, "voto_scritto_pagella"));
        $voto_pagellina[9] = trim(pg_fetch_result($result, 0, "voto_orale_pagella"));
        $voto_pagellina[10] = trim(pg_fetch_result($result, 0, "voto_pratico_pagella"));
    }
    return $voto_pagellina;
    //}}} </editor-fold>
}

function estrai_id_pagellina($periodo, $id_studente) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre l'id di una pagellina">
    $id_pagellina = "";
    $query = "SELECT
					id_pagellina
				FROM
					pagelline
				WHERE
					id_studente='$id_studente'
					AND
					periodo='$periodo'
					AND
					flag_canc=0
				ORDER BY
					id_pagellina";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $id_pagellina = trim(pg_fetch_result($result, 0, "id_pagellina"));
    }
    return $id_pagellina;
    //}}} </editor-fold>
}

function estrai_id_voto_pagellina($id_materia, $id_pagellina) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre l'id di un voto pagellina">
    $id_voto_pagellina = "";
    $query = "SELECT
					id_voto_pagellina
				FROM
					voti_pagelline
				WHERE
					id_materia='$id_materia'
					AND
					id_pagellina='$id_pagellina'
					AND
					flag_canc=0";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $id_voto_pagellina = trim(pg_fetch_result($result, 0, "id_voto_pagellina"));
    }
    return $id_voto_pagellina;
    //}}} </editor-fold>
}

function identifica_periodo_tipo_voto($periodo, $id_ricerca = -1, $tipo_ricerca = 'classe') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per identificare il tipo di votazione singola doppia o tripla definito per quel tipo di pagelle">
    if ($tipo_ricerca == 'studente') {
        $query = "SELECT * FROM studenti_completi
                    WHERE id_studente = {$id_ricerca} and ordinamento != 'CORSO'";
    } else {
        $query = "SELECT * FROM classi_complete
                    WHERE id_classe = {$id_ricerca}";
    }

    $result = pgsql_query($query);
    $numero = pg_num_rows($result);
    $stato_visualizza_pagelle = "";
    $id_classe = pg_fetch_result($result, 0, "id_classe");

    switch ($periodo) {
        case 7:
        case 8:
        case 27:
        case 28:
            $stato_visualizza_pagelle = estrai_parametri_singoli("VOTAZIONE_PAGELLA_FINE_QUADRIMESTRE", $id_classe, 'classe');
            break;
        case 9:
        case 29:
            $stato_visualizza_pagelle = estrai_parametri_singoli("VOTAZIONE_PAGELLA_FINE_ANNO", $id_classe, 'classe');
            break;
        case 10:
            $stato_visualizza_pagelle = estrai_parametri_singoli("VOTO_PROVE_STRUTTURATE");
            break;
        case 11:
            $stato_visualizza_pagelle = estrai_parametri_singoli("VOTAZIONE_PAGELLA_ESAME_MAESTRO_ARTE", $id_classe, 'classe');
            break;
        default:
            $stato_visualizza_pagelle = estrai_parametri_singoli("VOTAZIONE_PAGELLA_INFRAQUADRIMESTRALE", $id_classe, 'classe');
            break;
    }

    switch (intval($stato_visualizza_pagelle)) {
        case 1:
            $tipo_visualizzazione_voti = "voto_singolo";
            break;
        case 2:
            $tipo_visualizzazione_voti = "scritto_orale";
            break;
        case 3:
            $tipo_visualizzazione_voti = "scritto_orale_pratico";
            break;
        case 4:
            $tipo_visualizzazione_voti = "personalizzato";
            break;
    }

    return $tipo_visualizzazione_voti;
    //}}} </editor-fold>
}

function calcola_crediti_ammessi($media_voti_pagella, $classe, $debiti, $studente) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per calcolare la banda dei crediti di uno studente in base alla media voti e alla classe di appartenenza">
    // TODO: ATTENZIONE!!!! NON CONSIDERA LA PRESENZA DI DEBITI E I CREDITI DEGLI STUDENTI PRIVATISTI. NON SO SE SIA VOLUTO, NEL CASO ADEGUARE.
    $sede_default = estrai_sedi_con_comune_e_provincia();
    global $db_key;
    $anno_scolastico = explode("_", $db_key);

    $trentino_abilitato = 'NO';
    foreach ($sede_default as $singola_sede) {
        if ($singola_sede['provincia'] === 'BZ' || $singola_sede['provincia'] === 'TN') {
            $trentino_abilitato = 'SI';
        }
    }

    if ((int) $anno_scolastico[1] < 2018) {
        switch ($classe) {
            case 3:
            case 4:
                //{{{ <editor-fold>
                if (($media_voti_pagella >= 5) && ($media_voti_pagella < 6) && $trentino_abilitato == 'SI') {
                    $fascia_crediti[0] = 3;
                    $fascia_crediti[1] = 4;
                }

                if ($media_voti_pagella < 6) {
                    $fascia_crediti[0] = 0;
                }

                if ($media_voti_pagella == 6) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 3;
                        $fascia_crediti[1] = 4;
                    } else {
                        $fascia_crediti[0] = 3;
                    }
                }

                if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 4;
                        $fascia_crediti[1] = 5;
                    } else {
                        $fascia_crediti[0] = 4;
                    }
                }

                if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 5;
                        $fascia_crediti[1] = 6;
                    } else {
                        $fascia_crediti[0] = 5;
                    }
                }

                if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 6;
                        $fascia_crediti[1] = 7;
                    } else {
                        $fascia_crediti[0] = 6;
                    }
                }

                if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[1] = 7;
                        $fascia_crediti[2] = 8;
                    } else {
                        $fascia_crediti[0] = 6;
                    }
                }
                //}}} </editor-fold>
                break;
            case 5:
                //{{{ <editor-fold>

                if (($media_voti_pagella >= 5) && ($media_voti_pagella < 6) && $trentino_abilitato == 'SI') {
                    $fascia_crediti[0] = 4;
                    $fascia_crediti[1] = 5;
                }

                if ($media_voti_pagella == 6) {
                    $fascia_crediti[0] = 4;
                    $fascia_crediti[1] = 5;
                }

                if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                    $fascia_crediti[0] = 5;
                    $fascia_crediti[1] = 6;
                }

                if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                    $fascia_crediti[0] = 6;
                    $fascia_crediti[1] = 7;
                }

                if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                    $fascia_crediti[0] = 7;
                    $fascia_crediti[1] = 8;
                }
                if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                    $fascia_crediti[0] = 8;
                    $fascia_crediti[1] = 9;
                }
                //}}} </editor-fold>
                break;
        }

        if ($trentino_abilitato == 'SI' && ($studente['pei'] == 'SI' || $studente['bes'] == 'SI')) {
            $fascia_crediti[0] = 3;
            $fascia_crediti[1] = 4;
            $fascia_crediti[2] = 5;
            $fascia_crediti[3] = 6;
            $fascia_crediti[4] = 7;
            $fascia_crediti[5] = 8;
            $fascia_crediti[6] = 9;
        }
    } else {
        switch ($classe) {
            case 3:
                //{{{ <editor-fold>
//                if(($media_voti_pagella >= 5) && ($media_voti_pagella < 6) && $trentino_abilitato == 'SI')
//                {
//                    $fascia_crediti[0] = 7;
//                    $fascia_crediti[1] = 8;
//                }

                if ($media_voti_pagella < 6) {
                    if ((int) $anno_scolastico[1] == 2019) {
                        $fascia_crediti[0] = 6;
                    } else {
                        $fascia_crediti[0] = 0;
                    }
                }

                if ($media_voti_pagella == 6) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 7;
                        $fascia_crediti[1] = 8;
                    } else {
                        $fascia_crediti[0] = 7;
                    }
                }

                if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 8;
                        $fascia_crediti[1] = 9;
                    } else {
                        $fascia_crediti[0] = 8;
                    }
                }

                if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 9;
                        $fascia_crediti[1] = 10;
                    } else {
                        $fascia_crediti[0] = 9;
                    }
                }

                if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 10;
                        $fascia_crediti[1] = 11;
                    } else {
                        $fascia_crediti[0] = 10;
                    }
                }

                if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[1] = 11;
                        $fascia_crediti[2] = 12;
                    } else {
                        $fascia_crediti[0] = 11;
                    }
                }
                //}}} </editor-fold>
                break;
            case 4:
                //{{{ <editor-fold>
//                if(($media_voti_pagella >= 5) && ($media_voti_pagella < 6) && $trentino_abilitato == 'SI')
//                {
//                    $fascia_crediti[0] = 8;
//                    $fascia_crediti[1] = 9;
//                }

                if ($media_voti_pagella < 6) {
                    if ((int) $anno_scolastico[1] == 2019) {
                        $fascia_crediti[0] = 6;
                    } else {
                        $fascia_crediti[0] = 0;
                    }
                }

                if ($media_voti_pagella == 6) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 8;
                        $fascia_crediti[1] = 9;
                    } else {
                        $fascia_crediti[0] = 8;
                    }
                }

                if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 9;
                        $fascia_crediti[1] = 10;
                    } else {
                        $fascia_crediti[0] = 9;
                    }
                }

                if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 10;
                        $fascia_crediti[1] = 11;
                    } else {
                        $fascia_crediti[0] = 10;
                    }
                }

                if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[0] = 11;
                        $fascia_crediti[1] = 12;
                    } else {
                        $fascia_crediti[0] = 11;
                    }
                }

                if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                    if ($debiti == "NO") {
                        $fascia_crediti[1] = 12;
                        $fascia_crediti[2] = 13;
                    } else {
                        $fascia_crediti[0] = 12;
                    }
                }
                //}}} </editor-fold>
                break;
            case 5:
                //{{{ <editor-fold>
                //            if(($media_voti_pagella >= 5) && ($media_voti_pagella < 6) && $trentino_abilitato == 'SI')
                //          {
                //              $fascia_crediti[0] = 4;
                //              $fascia_crediti[1] = 5;
                //          }
                if ((int) $anno_scolastico[1] == 2019 or (int) $anno_scolastico[1] == 2020) {
                    if ($media_voti_pagella < 5) {
                        $fascia_crediti[0] = 9;
                        $fascia_crediti[1] = 10;
                    }

                    if (($media_voti_pagella > 5) && ($media_voti_pagella <= 6)) {
                        $fascia_crediti[0] = 11;
                        $fascia_crediti[1] = 12;
                    }

                    if ($media_voti_pagella == 6) {
                        $fascia_crediti[0] = 13;
                        $fascia_crediti[1] = 14;
                    }

                    if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                        $fascia_crediti[0] = 15;
                        $fascia_crediti[1] = 16;
                    }

                    if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                        $fascia_crediti[0] = 17;
                        $fascia_crediti[1] = 18;
                    }

                    if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                        $fascia_crediti[0] = 19;
                        $fascia_crediti[1] = 20;
                    }
                    if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                        $fascia_crediti[0] = 21;
                        $fascia_crediti[1] = 22;
                    }
                } else {
                    if ($media_voti_pagella < 6) {
                        $fascia_crediti[0] = 7;
                        $fascia_crediti[1] = 8;
                    }

                    if ($media_voti_pagella == 6) {
                        $fascia_crediti[0] = 9;
                        $fascia_crediti[1] = 10;
                    }

                    if (($media_voti_pagella > 6) && ($media_voti_pagella <= 7)) {
                        $fascia_crediti[0] = 10;
                        $fascia_crediti[1] = 11;
                    }

                    if (($media_voti_pagella > 7) && ($media_voti_pagella <= 8)) {
                        $fascia_crediti[0] = 11;
                        $fascia_crediti[1] = 12;
                    }

                    if (($media_voti_pagella > 8) && ($media_voti_pagella <= 9)) {
                        $fascia_crediti[0] = 13;
                        $fascia_crediti[1] = 14;
                    }
                    if (($media_voti_pagella > 9) && ($media_voti_pagella <= 10)) {
                        $fascia_crediti[0] = 14;
                        $fascia_crediti[1] = 15;
                    }
                }

                //}}} </editor-fold>
                break;
        }

        if ($trentino_abilitato == 'SI' && ($studente['pei'] == 'SI' || $studente['bes'] == 'SI')) {
            if ((int) $anno_scolastico[1] == 2019 or (int) $anno_scolastico[1] == 2020) {
                $fascia_crediti[0] = 9;
                $fascia_crediti[1] = 10;
                $fascia_crediti[2] = 11;
                $fascia_crediti[3] = 12;
                $fascia_crediti[4] = 13;
                $fascia_crediti[5] = 14;
                $fascia_crediti[6] = 15;
                $fascia_crediti[7] = 16;
                $fascia_crediti[8] = 17;
                $fascia_crediti[9] = 18;
                $fascia_crediti[10] = 19;
                $fascia_crediti[11] = 20;
                $fascia_crediti[12] = 21;
                $fascia_crediti[13] = 22;
            } else {
                $fascia_crediti[0] = 7;
                $fascia_crediti[1] = 8;
                $fascia_crediti[2] = 9;
                $fascia_crediti[3] = 10;
                $fascia_crediti[4] = 11;
                $fascia_crediti[5] = 12;
                $fascia_crediti[6] = 13;
                $fascia_crediti[7] = 14;
                $fascia_crediti[8] = 15;
            }
        }
    }

    return $fascia_crediti;
    //}}} </editor-fold>
}

/**
 * Funzione per inserire un nuovo voto in una pagellina
 *
 * @param type $voto_pagellina
 * @param type $debito
 * @param type $id_materia
 * @param type $id_pagellina
 * @param type $ore_assenza
 * @param type $data
 * @param type $voto_scritto_pagella
 * @param type $voto_orale_pagella
 * @param type $voto_pratico_pagella
 * @param type $current_user
 * @param type $tipo_recupero
 * @param type $esito_recupero
 * @param type $giudizio_analitico
 * @param type $modalita
 * @param type $monteore_totale
 * @param type $sovrascrivi_monteore
 * @param type $sovrascrivi_voto
 * @return type
 */
function inserisci_voto_pagellina($voto_pagellina, $debito, $id_materia, $id_pagellina, $ore_assenza, $data, $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $current_user, $tipo_recupero = '', $esito_recupero = '', $giudizio_analitico = '', $modalita = 'professore', $monteore_totale = 0, $sovrascrivi_monteore = "NO", $sovrascrivi_voto = "SI") {
    /* {{{ */
    $query = "SELECT
					id_voto_pagellina,
					modificato_da_amministratore
				FROM voti_pagelline
                WHERE id_pagellina = '{$id_pagellina}'
                    AND id_materia = '{$id_materia}'
					AND flag_canc = 0";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        $id_voto_pagellina = trim(pg_fetch_result($result, 0, "id_voto_pagellina"));
        $modificato_da_amministratore = trim(pg_fetch_result($result, 0, "modificato_da_amministratore"));
        modifica_voto_pagellina($id_voto_pagellina, $voto_pagellina, $debito, $ore_assenza, $data, $sovrascrivi_voto, 'SI', 'SI', $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $current_user, $tipo_recupero, $esito_recupero, $giudizio_analitico, $modificato_da_amministratore, $modalita, $monteore_totale, $sovrascrivi_monteore);
    } else {
        if ($modalita == 'amministratore') {
            $query = "INSERT INTO voti_pagelline (
							voto_pagellina,
							debito,
							id_materia,
							id_pagellina,
							ore_assenza,
							monteore_totale,
							data,
							voto_scritto_pagella,
							voto_orale_pagella,
							voto_pratico_pagella,
							tipo_recupero,
							esito_recupero,
							giudizio_analitico,
							modificato_da_amministratore
						) VALUES (
							'" . $voto_pagellina . "',
							'" . $debito . "',
							'" . $id_materia . "',
							'" . $id_pagellina . "',
							'" . intval($ore_assenza) . "',
							'" . intval($monteore_totale) . "',
							'" . $data . "',
							'" . $voto_scritto_pagella . "',
							'" . $voto_orale_pagella . "',
							'" . $voto_pratico_pagella . "',
							'" . $tipo_recupero . "',
							'" . $esito_recupero . "',
							'" . encode($giudizio_analitico) . "',
							'SI'
						)";

            $result = pgsql_query($query) or die("Invalid $query");
        } else {
            $query = "SELECT
							studenti_completi.id_studente,
							studenti_completi.id_classe,
							pagelline.periodo
						FROM pagelline, studenti_completi
						WHERE
                            pagelline.id_pagellina = {$id_pagellina}
							AND pagelline.id_studente = studenti_completi.id_studente
							AND pagelline.flag_canc = 0
						ORDER BY studenti_completi.ordinamento";

            $result = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($result);

            if ($numero > 0) {
                $periodo = trim(pg_fetch_result($result, 0, "periodo"));
                $id_studente = trim(pg_fetch_result($result, 0, "id_studente"));
                $id_classe = trim(pg_fetch_result($result, 0, "id_classe"));
            }

            $voti_calcolati = applica_formula_proposta_voto($periodo, $id_classe, $id_studente, $id_materia, $voto_pagellina, $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $current_user);
            $query = "INSERT INTO voti_pagelline (
							voto_pagellina,
							proposta_voto_pagellina,
							debito,
							id_materia,
							id_pagellina,
							ore_assenza,
							monteore_totale,
							data,
							voto_scritto_pagella,
							voto_orale_pagella,
							voto_pratico_pagella,
							proposta_voto_scritto_pagella,
							proposta_voto_orale_pagella,
							proposta_voto_pratico_pagella,
							tipo_recupero,
							esito_recupero,
							giudizio_analitico,
							modificato_da_amministratore
						) VALUES (
							'" . $voti_calcolati['unico'] . "',
							'" . $voto_pagellina . "',
							'" . $debito . "',
							'" . $id_materia . "',
							'" . $id_pagellina . "',
							'" . intval($ore_assenza) . "',
							'" . intval($monteore_totale) . "',
							'" . $data . "',
							'" . $voti_calcolati['scritto'] . "',
							'" . $voti_calcolati['orale'] . "',
							'" . $voti_calcolati['pratico'] . "',
							'" . $voto_scritto_pagella . "',
							'" . $voto_orale_pagella . "',
							'" . $voto_pratico_pagella . "',
							'" . $tipo_recupero . "',
							'" . $esito_recupero . "',
							'" . encode($giudizio_analitico) . "',
							'NO'
						)";

            pgsql_query($query) or die("Invalid $query");
        }

        $query = "SELECT id_voto_pagellina FROM voti_pagelline
                    WHERE id_materia = '{$id_materia}'
						AND id_pagellina = '{$id_pagellina}'
						AND data = '{$data}'
						AND flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $id_voto_pagellina = trim(pg_fetch_result($result, 0, "id_voto_pagellina"));
        }

        inserisci_log(["id_voto_pagellina" => $id_voto_pagellina], "voti_pagelline", $current_user, "INTERFACCIA", "INSERIMENTO");
    }

    return $id_voto_pagellina;
    /* }}} */
}

/**
 * Funzione per modificare il voto di una pagellina
 *
 * @param type $id_voto_pagellina
 * @param string $voto_pagellina
 * @param type $debito
 * @param type $ore_assenza
 * @param type $data
 * @param type $sovrascrivi_voto
 * @param type $sovrascrivi_assenze
 * @param type $sovrascrivi_debito
 * @param type $voto_scritto_pagella
 * @param type $voto_orale_pagella
 * @param type $voto_pratico_pagella
 * @param type $current_user
 * @param type $tipo_recupero
 * @param type $esito_recupero
 * @param type $giudizio_analitico
 * @param type $modificato_da_amministratore
 * @param type $modalita
 * @param type $monteore_totale
 * @param type $sovrascrivi_monteore
 * @return type
 */
function modifica_voto_pagellina($id_voto_pagellina, $voto_pagellina, $debito, $ore_assenza, $data, $sovrascrivi_voto, $sovrascrivi_assenze, $sovrascrivi_debito, $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $current_user, $tipo_recupero = '', $esito_recupero = '', $giudizio_analitico = '', $modificato_da_amministratore = 'NO', $modalita = 'professore', $monteore_totale = 0, $sovrascrivi_monteore = "NO") {
    $ore_assenza = (int) $ore_assenza;
    $monteore_totale = (int) $monteore_totale;
    /* {{{ */
    if ($voto_pagellina == "-1") {
        $voto_pagellina = "";
    }

    if ($modalita == 'amministratore') {
        $query = "UPDATE voti_pagelline SET ";

        if ($sovrascrivi_voto == "SI") {
            $query .= " voto_pagellina = '{$voto_pagellina}',
                        voto_scritto_pagella = '{$voto_scritto_pagella}',
                        voto_orale_pagella = '{$voto_orale_pagella}',
                        voto_pratico_pagella = '{$voto_pratico_pagella}',
                        giudizio_analitico = '" . encode($giudizio_analitico) . "',
                        tipo_recupero = '{$tipo_recupero}',
                        esito_recupero = '{$esito_recupero}',
                        modificato_da_amministratore = 'SI', ";
        }

        if ($sovrascrivi_assenze == "SI") {
            $query .= "	ore_assenza	= '{$ore_assenza}', ";
        }

        if ($sovrascrivi_monteore == "SI") {
            $query .= "	monteore_totale	= '{$monteore_totale}', ";
        }

        if ($sovrascrivi_debito == "SI") {
            $query .= "	debito = '{$debito}', ";
        }

        $query .= "	data = '{$data}'
                    WHERE id_voto_pagellina = {$id_voto_pagellina}";

        pgsql_query($query) or die("Invalid $query");
    } else {
        $query = "SELECT
						studenti_completi.id_studente,
						studenti_completi.id_classe,
						pagelline.periodo,
						voti_pagelline.id_materia
					FROM
						voti_pagelline,
						pagelline,
						studenti_completi
                    WHERE voti_pagelline.id_voto_pagellina = {$id_voto_pagellina}
						AND pagelline.id_pagellina = voti_pagelline.id_pagellina
						AND pagelline.id_studente = studenti_completi.id_studente
						AND pagelline.flag_canc = 0
					ORDER BY studenti_completi.ordinamento";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $periodo = trim(pg_fetch_result($result, 0, "periodo"));
            $id_studente = trim(pg_fetch_result($result, 0, "id_studente"));
            $id_classe = trim(pg_fetch_result($result, 0, "id_classe"));
            $id_materia = trim(pg_fetch_result($result, 0, "id_materia"));
        }

        $voti_calcolati = applica_formula_proposta_voto($periodo, $id_classe, $id_studente, $id_materia, $voto_pagellina, $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $current_user);

        $query = "UPDATE voti_pagelline SET ";

        if ($sovrascrivi_voto == "SI") {
            $voto_corrente = estrai_voto_pagellina($id_voto_pagellina);
            if ($voto_corrente['proposta_voto_pagellina'] == $voto_pagellina && $voto_corrente['proposta_voto_scritto_pagella'] == $voto_scritto_pagella && $voto_corrente['proposta_voto_orale_pagella'] == $voto_orale_pagella && $voto_corrente['proposta_voto_pratico_pagella'] == $voto_pratico_pagella) {
                $query .= "
                            giudizio_analitico			  = '" . encode($giudizio_analitico) . "',
                            tipo_recupero				  = '{$tipo_recupero}',
                            esito_recupero				  = '{$esito_recupero}', ";
            } else {
                $query .= " proposta_voto_pagellina		  = '{$voto_pagellina}',
                        proposta_voto_scritto_pagella = '{$voto_scritto_pagella}',
                        proposta_voto_orale_pagella	  = '{$voto_orale_pagella}',
                        proposta_voto_pratico_pagella = '{$voto_pratico_pagella}',
                        giudizio_analitico			  = '" . encode($giudizio_analitico) . "',
                        tipo_recupero				  = '{$tipo_recupero}',
                        esito_recupero				  = '{$esito_recupero}',
                        modificato_da_amministratore  = '{$modificato_da_amministratore}',
                        voto_pagellina				  = '{$voti_calcolati['unico']}',
                        voto_scritto_pagella		  = '{$voti_calcolati['scritto']}',
                        voto_orale_pagella			  = '{$voti_calcolati['orale']}',
                        voto_pratico_pagella		  = '{$voti_calcolati['pratico']}', ";
            }
        }

        if ($sovrascrivi_assenze == "SI") {
            $query .= "	ore_assenza	= {$ore_assenza }, ";
        }

        if ($sovrascrivi_monteore == "SI") {
            $query .= "	monteore_totale = {$monteore_totale}, ";
        }

        if ($sovrascrivi_debito == "SI") {
            $query .= "	debito = '{$debito}', ";
        }

        $query .= "	data = {$data}
                    WHERE id_voto_pagellina = {$id_voto_pagellina}";

        pgsql_query($query) or die("Invalid {$query}");
    }

    inserisci_log(["id_voto_pagellina" => $id_voto_pagellina], "voti_pagelline", $current_user, "INTERFACCIA", "MODIFICA");

    return $id_voto_pagellina;
    /* }}} */
}

function modifica_esito_tipo_recupero($id_voto_pagellina, $esito_recupero = '', $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare l'esito di un tipo di recupero">
    $query = "UPDATE voti_pagelline
				SET
					esito_recupero = '" . $esito_recupero . "'
				WHERE
					id_voto_pagellina = " . $id_voto_pagellina;

    pgsql_query($query) or die("Invalid $query");

    inserisci_log(["id_pagellina" => $id_voto_pagellina], "voti_pagelline", $current_user, "INTERFACCIA", "MODIFICA");

    return $id_voto_pagellina;
    //}}} </editor-fold>
}

function inserisci_pagellina($periodo, $id_studente, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire una nuova pagellina">
    $query = "SELECT id_pagellina FROM pagelline
				WHERE periodo = '$periodo'
					AND id_studente = {$id_studente}
					AND flag_canc = 0
				ORDER BY id_pagellina DESC";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $id_pagellina = "INESISTENTE";

    if ($numero > 0) {
        $id_pagellina = pg_fetch_result($result, 0, "id_pagellina");
    } else {
        $query = "INSERT INTO pagelline (
						periodo,
						id_studente
					) VALUES (
						'$periodo',
						'$id_studente'
					)
                    RETURNING id_pagellina";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        $id_pagellina = "INESISTENTE";

        if ($numero > 0) {
            $id_pagellina = pg_fetch_result($result, 0, "id_pagellina");
        }

        inserisci_log(["id_pagellina" => $id_pagellina], "pagelline", $current_user, "INTERFACCIA", "INSERIMENTO");
    }

    return $id_pagellina;
    //}}} </editor-fold>
}

function inserisci_proposte_voti_assenze_pagellina_classe($id_classe, $periodo, $data_inizio_voti, $data_fine_voti, $inserisci_voto, $sovrascrivi_voto, $data_inizio_assenze, $data_fine_assenze, $inserisci_assenze, $sovrascrivi_assenze, $arrotondamento, $tipo_inserimento, $id_professore, $funzione_abilita_proposte_totali, $current_user, $consiglio_classe_attivo = 'NO', $forza_inserimento_singola_materia = 'NO', $pagellina_da_usare_per_pre_inserimento = null, $sovrascrivi_monteore = 'NO', $corsi_abbinati = "NO", $calcola_monteore_template = "NO", $numero_settimane_template = "0", $durata_periodo_scolastico = '0', $inserisci_assenze_DAD = "SI") {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i voti di una classe per ogni pagella di tutte le materie">
    if ($id_professore != -1) {
        $coordinatore = verifica_coordinatore_classe($id_classe, $id_professore);
    }

    if ($tipo_inserimento == 'amministratore' or ( $coordinatore == 'SI' && $consiglio_classe_attivo == 'SI')) {
        $modalita = 'amministratore';
    } else {
        $modalita = 'professore';
    }

    if ($pagellina_da_usare_per_pre_inserimento > 0) {
        $vecchia_pagellina = estrai_voti_tabellone_pagellina_classe_finale($id_classe, $pagellina_da_usare_per_pre_inserimento, $current_user, 'classe');
    }

    if (($tipo_inserimento == "amministratore" || ($coordinatore == "SI" && $funzione_abilita_proposte_totali == '1')) and $forza_inserimento_singola_materia != 'SI') {
        $elenco_materie = estrai_materie_multi_classe($id_classe);
    } else {
        $elenco_materie = estrai_materie_multi_classe_del_professore($id_classe, $id_professore);
    }

    $peso_voti = estrai_parametri_singoli("PESO_VOTI", $id_classe, 'classe');
    if ($peso_voti == "SI") {
        $peso_voti = 'PONDERATA';
    } else {
        $peso_voti = 'ARITMETICA';
    }


    // Se le aree disciplinari sono attive e la classe ha le aree disciplinari calcolo anche le materie NV
    // Per le materie NV non aggregate controllo al momento del salvataggio della pagellina
    $aree_disciplinari = estrai_parametri_singoli("ABILITA_AREE_DISCIPLINARI");
    $aree_disciplinari_classe = estrai_aree_disciplinari($id_classe, 'matrice');

    $materie_aggregate = [];
    foreach ($aree_disciplinari_classe as $area) {
        $materie_aggregate[$area['id_materia_aggregata']] = $area['id_materia_aggregata'];
    }

    //$filtra_materie_nv = $aree_disciplinari == 'SI' && count($aree_disciplinari_classe) > 0 ? "X": "NV";

    if (($aree_disciplinari == 'SI' && count($aree_disciplinari_classe) > 0) or ( $corsi_abbinati == "SI")) {
        $filtra_materie_nv = "X";
    } else {
        $filtra_materie_nv = "NV";
    }

    $elenco_studenti = estrai_studenti_classe($id_classe);
    $sovrascrivi_voto_vecchio = $sovrascrivi_voto;
    $sovrascrivi_assenze_vecchie = $sovrascrivi_assenze;
    $inserisci_monteore = $inserisci_assenze;

    for ($cont_stud = 0; $cont_stud < count($elenco_studenti); $cont_stud++) {
        $id_studente = $elenco_studenti[$cont_stud][0];

        if ($inserisci_assenze == 'SI') {
            $ore_assenza_totali = estrai_ore_assenza_studente_periodo_nuovo($id_studente, $data_inizio_assenze, $data_fine_assenze, $filtra_materie_nv, 'SI', 'SI', 0, $inserisci_assenze_DAD);

            if ($calcola_monteore_template == 'TEMPLATE') {
                $ore_monteore_template = calcola_monteore_template_studente($id_studente, $numero_settimane_template, $durata_periodo_scolastico);
                foreach ($ore_assenza_totali as $id_materia => $ore_assenza) {
                    $ore_assenza_totali[$id_materia]["monteore_totale"] = $ore_monteore_template[$id_materia]["monteore_totale"];
                }
            }

            if (is_array($vecchia_pagellina[$id_studente]) && is_array($ore_assenza_totali)) {
                foreach ($ore_assenza_totali as $id_materia => $ore_assenza) {
                    $ore_assenza_totali[$id_materia]["ore_assenza"] = intval($ore_assenza_totali[$id_materia]["ore_assenza"]) +
                            intval($vecchia_pagellina[$id_studente][$id_materia]["ore_assenza"]);

                    $ore_assenza_totali[$id_materia]["monteore_totale"] = intval($ore_assenza_totali[$id_materia]["monteore_totale"]) +
                            intval($vecchia_pagellina[$id_studente][$id_materia]["monteore_totale"]);
                }
            }

            //modifica per inserimento ore assenze prese anche da corsi collegati alla materia principale
            if ($corsi_abbinati == 'SI') {
                foreach ($ore_assenza_totali as $id_materia => $ore_assenza) {
                    if ($ore_assenza['id_materia_riferimento'] > 0) {
                        $ore_assenza_totali[$ore_assenza['id_materia_riferimento']]["ore_assenza"] += intval($ore_assenza_totali[$id_materia]["ore_assenza"]);
                        $ore_assenza_totali[$ore_assenza['id_materia_riferimento']]["monteore_totale"] += intval($ore_assenza_totali[$id_materia]["monteore_totale"]);
                    }
                }
            }
            //fine modifica
        }

        if ($inserisci_voto == 'SI' || $inserisci_assenze == 'SI' || $inserisci_monteore == 'SI') {
            for ($cont = 0; $cont < count($elenco_materie); $cont++) {
                //{{{ <editor-fold defaultstate="collapsed" desc="sezione inserimento voti">
                $sovrascrivi_voto = $sovrascrivi_voto_vecchio;
                $sovrascrivi_assenze = $sovrascrivi_assenze_vecchie;

                $query = "SELECT * FROM pagelline
							WHERE id_studente = {$id_studente}
								AND periodo = '$periodo'
								AND flag_canc=0
							ORDER BY id_pagellina desc";

                $result = pgsql_query($query) or die("Invalid $query");
                $numero = pg_num_rows($result);
                $voto_pagellina = "";
                $debito = "";
                $ore_assenza = 0;
                $data = time();
                $id_materia = $elenco_materie[$cont][0];

                $query_abbinamenti = "SELECT id_materia
										FROM classi_prof_materie
                                        INNER JOIN studenti_completi ON (studenti_completi.id_classe=classi_prof_materie.id_classe)
										WHERE
											id_studente = {$id_studente}
											AND id_materia = {$id_materia}";

                $result_abbinamenti = pgsql_query($query_abbinamenti) or die("Invalid $query_abbinamenti");
                $numero_abbinamenti = pg_num_rows($result_abbinamenti);

                if ($numero_abbinamenti > 0) {
                    //{{{ <editor-fold defaultstate="collapsed">
                    if ($inserisci_voto == 'SI') {
                        $media_voti = calcola_media_voti_studente_singola_materia($id_studente, $id_materia, $data_inizio_voti, $data_fine_voti, $arrotondamento, 2, $corsi_abbinati, '', $peso_voti);
                    } else {
                        $media_voti = 0;
                    }

                    if (is_array($media_voti)) {
                        if ($media_voti["totale"] == 0) {
                            $voto_pagellina = "";
                        } else {
                            $voto_pagellina = $media_voti["totale"];
                        }

                        if ($media_voti["scritto"] == 0) {
                            $voto_scritto_pagellina = "";
                        } else {
                            $voto_scritto_pagellina = $media_voti["scritto"];
                        }

                        if ($media_voti["orale"] == 0) {
                            $voto_orale_pagellina = "";
                        } else {
                            $voto_orale_pagellina = $media_voti["orale"];
                        }

                        if ($media_voti["pratico"] == 0) {
                            $voto_pratico_pagellina = "";
                        } else {
                            $voto_pratico_pagellina = $media_voti["pratico"];
                        }
                    } else {
                        $voto_pagellina = "";
                        $voto_scritto_pagellina = "";
                        $voto_orale_pagellina = "";
                        $voto_pratico_pagellina = "";
                    }

                    if ($numero > 0) {
                        //{{{ <editor-fold defaultstate="collapsed" desc="caso in cui la pagellina esiste">
                        $id_pagellina = trim(pg_fetch_result($result, 0, "id_pagellina"));

                        $query_int = "SELECT *
										FROM voti_pagelline
										WHERE id_pagellina = {$id_pagellina}
                                            AND id_materia = {$id_materia}
											AND flag_canc = 0";

                        $result_int = pgsql_query($query_int) or die("Invalid $query_int");
                        $numero_int = pg_num_rows($result_int);

                        if ($numero_int > 0) {
                            $voto_pagellina_estratto = pg_fetch_assoc($result_int, 0);

                            if ($inserisci_voto == "NO") {
                                $sovrascrivi_voto = "NO";
                            } else {
                                if (
                                        $sovrascrivi_voto == "NO" &&
                                        (strlen(
                                                $voto_pagellina_estratto["voto_pagellina"] .
                                                $voto_pagellina_estratto["voto_scritto_pagella"] .
                                                $voto_pagellina_estratto["voto_orale_pagella"] .
                                                $voto_pagellina_estratto["voto_pratico_pagella"]
                                        ) == 0)
                                ) {
                                    $sovrascrivi_voto = "SI";
                                }
                            }

                            if ($inserisci_assenze == "NO") {
                                $sovrascrivi_assenze = "NO";
                            } else {
                                if ($sovrascrivi_assenze == "NO" && intval($voto_pagellina_estratto["ore_assenza"]) == 0) {
                                    $sovrascrivi_assenze = "SI";
                                }
                            }

                            if ($inserisci_monteore == "NO") {
                                $sovrascrivi_monteore = "NO";
                            } else {
                                if ($inserisci_monteore == "NO" && intval($voto_pagellina_estratto["monteore_totale"]) == 0) {
                                    $sovrascrivi_monteore = "SI";
                                }
                            }
                            // Gestisco le materie NV non aggregate in classi con aree disciplinari
                            // Imposto monteore e assenze a 0 a prescindere
                            if ($aree_disciplinari == 'SI' && count($aree_disciplinari_classe) > 0 && !in_array($id_materia, $materie_aggregate)
                            ) {
                                $voto_pagellina_estratto["ore_assenza"] = 0;
                                $voto_pagellina_estratto["monteore_totale"] = 0;
                            }

                            $id_voto_pagellina = trim(pg_fetch_result($result_int, 0, "id_voto_pagellina"));
                            $sovrascrivi_debito = "NO";
                            $id_voto = modifica_voto_pagellina($id_voto_pagellina, $voto_pagellina, $debito, $ore_assenza_totali[$id_materia]["ore_assenza"], $data, $sovrascrivi_voto, $sovrascrivi_assenze, $sovrascrivi_debito, $voto_scritto_pagellina, $voto_orale_pagellina, $voto_pratico_pagellina, $current_user, '', '', '', $voto_pagellina_estratto["modificato_da_amministratore"], $modalita, $ore_assenza_totali[$id_materia]["monteore_totale"], $sovrascrivi_monteore);
                        } else {
                            if ($inserisci_voto == "NO") {
                                $voto_pagellina = "";
                                $voto_scritto_pagellina = "";
                                $voto_orale_pagellina = "";
                                $voto_pratico_pagellina = "";
                            }

                            if ($inserisci_assenze == "NO") {
                                $ore_assenza_totali[$id_materia]["ore_assenza"] = 0;
                            }

                            if ($inserisci_monteore == "NO") {
                                    $ore_assenza_totali[$id_materia]["monteore_totale"] = 0;
                                }

                            // Gestisco le materie NV non aggregate in classi con aree disciplinari
                            // Imposto monteore e assenze a 0 a prescindere
                            if ($aree_disciplinari == 'SI' && count($aree_disciplinari_classe) > 0 && !in_array($id_materia, $materie_aggregate)
                            ) {
                                $ore_assenza_totali[$id_materia]["ore_assenza"] = 0;
                                $ore_assenza_totali[$id_materia]["monteore_totale"] = 0;
                            }

                            $id_voto = inserisci_voto_pagellina($voto_pagellina, $debito, $id_materia, $id_pagellina, $ore_assenza_totali[$id_materia]["ore_assenza"], $data, $voto_scritto_pagellina, $voto_orale_pagellina, $voto_pratico_pagellina, $current_user, '', '', '', $modalita, $ore_assenza_totali[$id_materia]["monteore_totale"], $sovrascrivi_monteore);
                        }
                        //}}} </editor-fold>
                    } else {
                        //{{{ <editor-fold defaultstate="collapsed" desc="caso inserisce nuova pagellina">
                        if ($inserisci_voto == "NO") {
                            $voto_pagellina = "";
                            $voto_scritto_pagellina = "";
                            $voto_orale_pagellina = "";
                            $voto_pratico_pagellina = "";
                        }

                        if ($inserisci_assenze == "NO") {
                            $ore_assenza_totali[$id_materia]["ore_assenza"] = 0;
                        }

                        if ($inserisci_monteore == "NO") {
                                $ore_assenza_totali[$id_materia]["monteore_totale"] = 0;
                            }

                        // Gestisco le materie NV non aggregate in classi con aree disciplinari
                        // Imposto monteore e assenze a 0 a prescindere
                        if ($aree_disciplinari == 'SI' && count($aree_disciplinari_classe) > 0 && !in_array($id_materia, $materie_aggregate)
                        ) {
                            $ore_assenza_totali[$id_materia]["ore_assenza"] = 0;
                            $ore_assenza_totali[$id_materia]["monteore_totale"] = 0;
                        }

                        $id_pagellina = inserisci_pagellina($periodo, $id_studente, $current_user);
                        $id_voto = inserisci_voto_pagellina($voto_pagellina, $debito, $id_materia, $id_pagellina, $ore_assenza_totali[$id_materia]["ore_assenza"], $data, $voto_scritto_pagellina, $voto_orale_pagellina, $voto_pratico_pagellina, $current_user, '', '', '', $modalita, $ore_assenza_totali[$id_materia]["monteore_totale"], $sovrascrivi_monteore);
                        //}}} </editor-fold>
                    }
                    //}}} </editor-fold>
                }
                //}}} </editor-fold>
            }
        }
    }

    return $voto_pagellina;
    //}}} </editor-fold>
}

function elimina_pagellina($id_pagellina) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare i dati di una commissione">
    $query = "DELETE FROM pagelline
				WHERE
					id_pagellina='$id_pagellina'";
    $result = pgsql_query($query) or die("Invalid $query");

    return $result;
    //}}} </editor-fold>
}

function elimina_voto_pagellina($id_voto_pagellina) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare i dati di una commissione">
    $query = "DELETE FROM voti_pagelline
				WHERE
					id_voto_pagellina='$id_voto_pagellina'";
    $result = pgsql_query($query) or die("Invalid $query");

    return $result;
    //}}} </editor-fold>
}

function modifica_pagella_per_correzione($id_voto_pagellina, $voto_scritto_pagella, $voto_orale_pagella, $voto_pratico_pagella, $voto, $ore_assenza) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare il voto triplo di una pagella">
    if ($voto == "-1") {
        $voto = "";
    }
    if ($voto_scritto_pagella == "-1") {
        $voto_scritto_pagella = "";
    }
    if ($voto_orale_pagella == "-1") {
        $voto_orale_pagella = "";
    }
    if ($voto_pratico_pagella == "-1") {
        $voto_pratico_pagella = "";
    }

    $query = "UPDATE voti_pagelline
				SET
					voto_pagellina='$voto',
					voto_scritto_pagella='$voto_scritto_pagella',
					voto_orale_pagella='$voto_orale_pagella',
					voto_pratico_pagella='$voto_pratico_pagella',
					ore_assenza='$ore_assenza'
				WHERE
					id_voto_pagellina='$id_voto_pagellina'";
    $result = pgsql_query($query) or die("Invalid $query");

    return $result;
    //}}} </editor-fold>
}

function traduci_voto_esame_qualifica($tabella_traduzione, $valore) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare il voto triplo di una pagella">
    $query = "SELECT
					*
				FROM
					tab_traduzione_qualifiche
				WHERE
					tipo_tabella='" . $tabella_traduzione . "'";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    $tabella_traduzione = [];
    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $tabella_traduzione[] = pg_fetch_assoc($result, $cont);
        }
    }

    foreach ($tabella_traduzione as $riga) {
        if (($valore >= $riga['minimo']) and ( $valore < $riga['massimo'])) {
            $valore_tradotto = $riga['valore_tradotto'];
        }
    }

    return $valore_tradotto;
    //}}} </editor-fold>
}

function cambia_stato_consiglio_di_classe($id_classe, $azione, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare lo stato di attivazione di un consiglio di classe">
    if ($azione == 'apri') {
        $valore = 'SI';
    } elseif ($azione == 'chiudi') {
        $valore = 'POST';
        $stud_classe = estrai_studenti_classe($id_classe);
        foreach ($stud_classe as $studente) {
            calcola_esito_finale_studente($studente['id_studente'], $current_user, true);
        }
    } else {
        $valore = 'NO';
    }
    $query = "UPDATE classi
				SET consiglio_classe_attivo = '" . $valore . "'
				WHERE id_classe = " . $id_classe;

    pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_classe"] = $id_classe;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "MODIFICA";
    inserisci_log($mat_oggetti, "classi", $current_user, $tipo_sorgente, $operazione);
    //}}} </editor-fold>
}

function calcola_media_voti_pagellina_studente_periodo_anno($id_studente, $periodo, $anno, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="funzione che calcola la media dei voti di una pagellina di uno studente per un periodo in un'anno scolastico">
    global $dbname, $db_key;

    $db_key_temp = $db_key;

    $trentino_abilitato = verifica_trentino_abilitato();
    $risultato_media = 0;
    if (array_key_exists($anno, $dbname)) {
        $db_key = $anno;

        $mat_voti = estrai_voti_tabellone_pagellina_classe_finale($id_studente, $periodo, $current_user, 'studente');
        $mat_materie = estrai_materie_multi_classe_studente($id_studente);
        $somma_valori = 0;
        $cont_media = 0;

        if (is_array($mat_materie)) {
            foreach ($mat_materie as $materia_per_media) {

                // Se scuola del trentino verifico la presenza di carenze nella tabella specifica
                $debito_presente = ($trentino_abilitato == 'NO') ? false : verifica_presenza_debito($id_studente, $materia_per_media['id_materia'], estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE"));


                if ($materia_per_media['in_media_pagelle'] == 'SI' && floatval($mat_voti[$id_studente][$materia_per_media['id_materia']]['voto_pagellina']) > 0) {
                    if ($debito_presente && floatval($mat_voti[$id_studente][$materia_per_media['id_materia']]['voto_pagellina']) < 6) {
                        $voto_temp = 6;
                    } else {
                        $voto_temp = floatval($mat_voti[$id_studente][$materia_per_media['id_materia']]['voto_pagellina']);
                    }

                    $somma_valori += $voto_temp;
                    $cont_media++;
                }
            }
        }

        $risultato_media = $cont_media > 0 ? round($somma_valori / $cont_media, 2) : '';
    }

    $db_key = $db_key_temp;

    return $risultato_media;
    //}}} </editor-fold>
}

function estrai_pagelline_studente_periodo_anno($id_studente, $codice_fiscale, $array_db_anno, $current_user, $raggruppamento = 'id_materia') {
    //{{{ <editor-fold defaultstate="collapsed" desc="funzione che calcola la media dei voti di una pagellina di uno studente per un periodo in un'anno scolastico">
    global $dbname;
    global $db_key;

    $db_key_temp = $db_key;
    $array_finale_materie = [];

    foreach ($array_db_anno as $singolo_anno) {
        $anno = $singolo_anno['anno'];
        $periodo = $singolo_anno['periodo'];

        if (array_key_exists($anno, $dbname)) {
            $db_key = $anno;
            //{{{ <editor-fold defaultstate="collapsed">
            $query = "SELECT id_classe,
                            classe,
                            codice_fiscale,
                            id_studente
                        FROM studenti_completi
                        WHERE ordinamento='0'
                            AND
                            (
                                id_studente = {$id_studente}
                                OR 
                                codice_fiscale = '{$codice_fiscale}'
                            )
                        ";

            $result = pgsql_query($query) or die("Invalid $query");
            $numero = pg_num_rows($result);

            if ($numero > 0) {
                $classe = pg_fetch_result($result, 0, 'classe');
                $codice_fiscale_da_verificare = pg_fetch_result($result, 0, 'codice_fiscale');
                $id_studente_storico = pg_fetch_result($result, 0, 'id_studente');
            }

            $somma_valori = 0;
            $cont_media = 0;

            if ($codice_fiscale == $codice_fiscale_da_verificare) {
                $mat_voti = estrai_voti_tabellone_pagellina_classe_finale($id_studente_storico, $periodo, $current_user, 'studente');
                $mat_materie = estrai_materie_multi_classe_studente($id_studente_storico);

                if (is_array($mat_materie)) {
                    foreach ($mat_materie as $materia_per_media) {
                        if ($materia_per_media['in_media_pagelle'] == 'SI' && floatval($mat_voti[$id_studente_storico][$materia_per_media['id_materia']]['voto_pagellina']) > 0) {
                            $array_finale_materie[$materia_per_media[$raggruppamento]]['descrizione'] = $materia_per_media['descrizione'];
                            $array_finale_materie[$materia_per_media[$raggruppamento]][$classe] = floatval($mat_voti[$id_studente_storico][$materia_per_media['id_materia']]['voto_pagellina']);
                            $somma_valori += floatval($mat_voti[$id_studente_storico][$materia_per_media['id_materia']]['voto_pagellina']);
                            $cont_media++;
                        }
                    }
                }

                if ($cont_media > 0) {
                    $array_finale_materie['media_voti']['descrizione'] = 'Media voti';
                    $array_finale_materie['media_voti'][$classe] = round($somma_valori / $cont_media, 2);
                } else {
                    $array_finale_materie['media_voti']['descrizione'] = 'Media voti';
                    $array_finale_materie['media_voti'][$classe] = '';
                }
            }
            //}}} </editor-fold>
        }
    }

    $db_key = $db_key_temp;

    return $array_finale_materie;
    //}}} </editor-fold>
}

function estrai_formule_proposte_voto() {
    //{{{ <editor-fold defaultstate="collapsed">
    $query = "SELECT * FROM
					formule_proposte_voto,
					classi_complete
				WHERE
					formule_proposte_voto.id_classe = classi_complete.id_classe
					AND formule_proposte_voto.flag_canc = 0
				ORDER BY
					formule_proposte_voto.periodo,
					formule_proposte_voto.data_modifica";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $mat_formule[$cont] = pg_fetch_assoc($result, $cont);
            $mat_formule[$cont]['descrizione_periodo'] = traduci_periodo($mat_formule[$cont]['periodo']);
            $mat_formule[$cont]['parametri'] = unserialize($mat_formule[$cont]['parametri']);
            $descrizione_finale = '';
            $arrotondamento = '';

            foreach ($mat_formule[$cont]['parametri'] as $periodo => $valori) {
                if (is_numeric($periodo)) {
                    if ($valori['numeratore'] != 0 and $valori['denominatore'] != 0) {
                        $descrizione_periodo = traduci_periodo($periodo);
                        $descrizione_finale .= "(" . $descrizione_periodo . " x " . $valori['numeratore'] . "/" . $valori['denominatore'] . ") + ";
                    }
                } elseif ($periodo == 'costante_funzione') {
                    if ($valori != 0) {
                        $descrizione_finale .= "(" . $valori . ") + ";
                    }
                } elseif ($periodo == 'arrotondamento_formula') {
                    if ($valori != 0) {
                        $arrotondamento = " arrotondato a " . $valori;
                    }
                }
            }

            if (strlen($descrizione_finale) > 0) {
                $descrizione_finale = substr($descrizione_finale, 0, -3) . $arrotondamento;
            }
            $mat_formule[$cont]['descrizione_formula'] = $descrizione_finale;
        }
        return $mat_formule;
    }
    //}}} </editor-fold>
}

function estrai_formula_proposta_voto($id_formula_proposta_voto) {
    //{{{ <editor-fold defaultstate="collapsed">
    $query = "SELECT
					*
				FROM
					formule_proposte_voto,
					classi_complete
				WHERE
					formule_proposte_voto.id_formula_proposta_voto=$id_formula_proposta_voto
					AND
					formule_proposte_voto.id_classe = classi_complete.id_classe
					AND
					formule_proposte_voto.flag_canc=0
				ORDER BY
					formule_proposte_voto.periodo,
					formule_proposte_voto.data_modifica";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $mat_formula = pg_fetch_assoc($result, 0);
        $mat_formula['descrizione_periodo'] = traduci_periodo($mat_formula['periodo']);
        $mat_formula['parametri'] = unserialize($mat_formula['parametri']);
        $descrizione_finale = '';
        $arrotondamento = '';
        foreach ($mat_formula['parametri'] as $periodo => $valori) {
            if (is_numeric($periodo)) {
                if ($valori['numeratore'] != 0 and $valori['denominatore'] != 0) {
                    $descrizione_periodo = traduci_periodo($periodo);
                    $descrizione_finale .= "(" . $descrizione_periodo . " x " . $valori['numeratore'] . "/" . $valori['denominatore'] . ") + ";
                }
            } elseif ($periodo == 'costante_funzione') {
                if ($valori != 0) {
                    $descrizione_finale .= "(" . $valori . ") + ";
                }
            } elseif ($periodo == 'arrotondamento_formula') {
                if ($valori != 0) {
                    $arrotondamento = " arrotondato a " . $valori;
                }
            }
        }

        if (strlen($descrizione_finale) > 0) {
            $descrizione_finale = substr($descrizione_finale, 0, -3) . $arrotondamento;
        }
        $mat_formula['descrizione_formula'] = $descrizione_finale;
        return $mat_formula;
    }
    //}}} </editor-fold>
}

function inserisci_formula_proposta_voto($periodo, $id_classe, $parametri, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed">
    $query = "SELECT
					id_formula_proposta_voto
				FROM
					formule_proposte_voto
				WHERE
					periodo='$periodo'
					AND
					id_classe='$id_classe'
					AND
					flag_canc=0
				ORDER BY
					id_formula_proposta_voto desc";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    $id_formula_proposta_voto = "INESISTENTE";
    if ($numero > 0) {
        $id_formula_proposta_voto = trim(pg_fetch_result($result, 0, "id_formula_proposta_voto"));
        $query = "UPDATE formule_proposte_voto
					SET
						periodo = '$periodo',
						id_classe = '$id_classe',
						parametri = '" . serialize($parametri) . "'
					WHERE
						id_formula_proposta_voto = " . $id_formula_proposta_voto;
        $result = pgsql_query($query) or die("Invalid $query");

        $mat_oggetti["id_formula_proposta_voto"] = $id_formula_proposta_voto;
        $tipo_sorgente = "INTERFACCIA";
        $operazione = "MODIFICA";
        inserisci_log($mat_oggetti, "formule_proposte_voto", $current_user, $tipo_sorgente, $operazione);
    } else {
        $query = "INSERT INTO formule_proposte_voto (
						parametri,
						periodo,
						id_classe
					) VALUES (
						'" . serialize($parametri) . "',
						'$periodo',
						'$id_classe'
					)";
        $result = pgsql_query($query) or die("Invalid $query");

        $query = "SELECT
						id_formula_proposta_voto
					FROM
						formule_proposte_voto
					WHERE
						periodo='$periodo'
						AND
						id_classe='$id_classe'
						AND
						flag_canc=0
					ORDER BY
						id_formula_proposta_voto desc";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        $id_formula_proposta_voto = "INESISTENTE";

        if ($numero > 0) {
            $id_formula_proposta_voto = trim(pg_fetch_result($result, 0, "id_formula_proposta_voto"));
        }

        $mat_oggetti["id_formula_proposta_voto"] = $id_formula_proposta_voto;
        $tipo_sorgente = "INTERFACCIA";
        $operazione = "INSERIMENTO";
        inserisci_log($mat_oggetti, "formule_proposte_voto", $current_user, $tipo_sorgente, $operazione);
    }

    return $id_formula_proposta_voto;
    //}}} </editor-fold>
}

function elimina_formula_proposta_voto($id_formula_proposta_voto, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed">
    $query = "UPDATE formule_proposte_voto
				SET flag_canc=" . time() . "
				WHERE id_formula_proposta_voto='$id_formula_proposta_voto'";

    $result = pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_formula_proposta_voto"] = $id_formula_proposta_voto;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "ELIMINAZIONE";
    inserisci_log($mat_oggetti, "formule_proposte_voto", $current_user, $tipo_sorgente, $operazione);

    return $result;
    //}}} </editor-fold>
}

function applica_formula_proposta_voto($periodo, $id_classe, $id_studente, $id_materia, $proposta_voto_unico, $proposta_voto_scritto, $proposta_voto_orale, $proposta_voto_pratico, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed">
    $query = "SELECT parametri FROM formule_proposte_voto
				WHERE periodo = '$periodo'
					AND id_classe = '$id_classe'
					AND flag_canc = 0
				ORDER BY id_formula_proposta_voto DESC";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    $id_formula_proposta_voto = "INESISTENTE";

    if ($numero > 0) {
        $parametri_proposta_voto = unserialize(pg_fetch_result($result, 0, "parametri"));
        $annulla_tutto = true;
        if (is_array($parametri_proposta_voto)) {
            $annulla_tutto = false;
            foreach ($parametri_proposta_voto as $periodo_parametro => $valori_parametro) {

                if ($periodo_parametro == 'arrotondamento_formula') {
                    $arrotondamento = $valori_parametro;
                } elseif ($periodo_parametro == 'costante_funzione') {
                    $voto_finale['unico'] += $valori_parametro;
                    $voto_finale['scritto'] += $valori_parametro;
                    $voto_finale['orale'] += $valori_parametro;
                    $voto_finale['pratico'] += $valori_parametro;
                } elseif ($valori_parametro['denominatore'] != 0 and $valori_parametro['numeratore'] != 0) {
                    if ($periodo_parametro != $periodo) {
                        //{{{ <editor-fold defaultstate="collapsed">
                        $voti_studente = estrai_voti_tabellone_pagellina_classe_finale($id_studente, $periodo_parametro, $current_user, 'studente');
                        //var_dump($voti_studente);
                        $voti_materia = $voti_studente[$id_studente][$id_materia];

                        //echo "---$id_materia---\n";
                        //var_dump($voti_materia);

                        $tipo_voto = identifica_periodo_tipo_voto($periodo_parametro, $id_studente, 'studente');
                        //var_dump($tipo_voto);

                        switch ($tipo_voto) {
                            case 'voto_singolo':
                                $voto_finale['unico'] += floatval($voti_materia['voto_pagellina']) *
                                        ($valori_parametro['numeratore'] /
                                        $valori_parametro['denominatore']);
                                $voto_finale['scritto'] += floatval($voti_materia['voto_pagellina']) *
                                        ($valori_parametro['numeratore'] /
                                        $valori_parametro['denominatore']);
                                $voto_finale['orale'] += floatval($voti_materia['voto_pagellina']) *
                                        ($valori_parametro['numeratore'] /
                                        $valori_parametro['denominatore']);
                                $voto_finale['pratico'] += floatval($voti_materia['voto_pagellina']) *
                                        ($valori_parametro['numeratore'] /
                                        $valori_parametro['denominatore']);

                                if (floatval($voti_materia['voto_pagellina']) == 0) {
                                    $annulla_tutto = true;
                                }
                                break;
                            case 'scritto_orale':
                                $voto_globale = 0;
                                $cont_globale = 0;
                                if ($voti_materia['scritto'] == '1') {
                                    $voto_finale['scritto'] += floatval($voti_materia['voto_scritto_pagella']) *
                                            ($valori_parametro['numeratore'] /
                                            $valori_parametro['denominatore']);
                                    $voto_globale += floatval($voti_materia['voto_scritto_pagella']);
                                    $cont_globale++;
                                    if (floatval($voti_materia['voto_scritto_pagella']) == 0) {
                                        $annulla_tutto = true;
                                    }
                                }
                                if ($voti_materia['orale'] == '1') {
                                    $voto_finale['orale'] += floatval($voti_materia['voto_orale_pagella']) *
                                            ($valori_parametro['numeratore'] /
                                            $valori_parametro['denominatore']);
                                    $voto_globale += floatval($voti_materia['voto_orale_pagella']);
                                    $cont_globale++;
                                    if (floatval($voti_materia['voto_orale_pagella']) == 0) {
                                        $annulla_tutto = true;
                                    }
                                }
                                $voto_finale['unico'] += floatval(($voto_globale) / $cont_globale) *
                                        ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);

                                break;
                            case 'scritto_orale_pratico':
                                $voto_globale = 0;
                                $cont_globale = 0;
                                if ($voti_materia['scritto'] == '1') {
                                    $voto_finale['scritto'] += floatval($voti_materia['voto_scritto_pagella']) *
                                            ($valori_parametro['numeratore'] /
                                            $valori_parametro['denominatore']);
                                    $voto_globale += floatval($voti_materia['voto_scritto_pagella']);
                                    $cont_globale++;
                                    if (floatval($voti_materia['voto_scritto_pagella']) == 0) {
                                        $annulla_tutto = true;
                                    }
                                }
                                if ($voti_materia['orale'] == '1') {
                                    $voto_finale['orale'] += floatval($voti_materia['voto_orale_pagella']) *
                                            ($valori_parametro['numeratore'] /
                                            $valori_parametro['denominatore']);
                                    $voto_globale += floatval($voti_materia['voto_orale_pagella']);
                                    $cont_globale++;
                                    if (floatval($voti_materia['voto_orale_pagella']) == 0) {
                                        $annulla_tutto = true;
                                    }
                                }
                                if ($voti_materia['pratico'] == '1') {
                                    $voto_finale['pratico'] += floatval($voti_materia['voto_pratico_pagella']) *
                                            ($valori_parametro['numeratore'] /
                                            $valori_parametro['denominatore']);
                                    $voto_globale += floatval($voti_materia['voto_pratico_pagella']);
                                    $cont_globale++;
                                    if (floatval($voti_materia['voto_pratico_pagella']) == 0) {
                                        $annulla_tutto = true;
                                    }
                                }
                                $voto_finale['unico'] += floatval(($voto_globale) / $cont_globale) *
                                        ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);

                                break;
                        }
                        //var_dump($voto_finale);
                        //}}} </editor-fold>
                    } else {
                        //{{{ <editor-fold defaultstate="collapsed">
                        $voto_finale['unico'] += floatval($proposta_voto_unico) * ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);
                        $voto_finale['scritto'] += floatval($proposta_voto_scritto) * ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);
                        $voto_finale['orale'] += floatval($proposta_voto_orale) * ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);
                        $voto_finale['pratico'] += floatval($proposta_voto_pratico) * ($valori_parametro['numeratore'] / $valori_parametro['denominatore']);
                        //}}} </editor-fold>
                    }
                }
            }
        }

        if ($annulla_tutto) {
            $voto_finale = [
                'unico' => $proposta_voto_unico,
                'scritto' => $proposta_voto_scritto,
                'orale' => $proposta_voto_orale,
                'pratico' => $proposta_voto_pratico
            ];
        }
    } else {
        $voto_finale = [
            'unico' => $proposta_voto_unico,
            'scritto' => $proposta_voto_scritto,
            'orale' => $proposta_voto_orale,
            'pratico' => $proposta_voto_pratico
        ];
    }
    $query = "SELECT
					tipo_valutazione
				FROM
					materie
				WHERE
					id_materia='$id_materia'";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $tipo_valutazione = pg_fetch_result($result, 0, "tipo_valutazione");
        $significati_voto = estrai_significati_voti_pagelle($tipo_valutazione, "solo_abilitati", $periodo);
        $voto_finale = [
            'unico' => arrotonda_a_schema_voti($significati_voto, round_to($voto_finale['unico'], $arrotondamento)),
            'scritto' => arrotonda_a_schema_voti($significati_voto, round_to($voto_finale['scritto'], $arrotondamento)),
            'orale' => arrotonda_a_schema_voti($significati_voto, round_to($voto_finale['orale'], $arrotondamento)),
            'pratico' => arrotonda_a_schema_voti($significati_voto, round_to($voto_finale['pratico'], $arrotondamento))
        ];
    }
    return $voto_finale;
    //}}} </editor-fold>
}

function arrotonda_a_schema_voti($schema_voti, $voto) {
    //{{{ <editor-fold defaultstate="collapsed">
    $voto_min = 0;
    $voto_max = 1000;
    if (is_numeric($voto)) {
        foreach ($schema_voti as $voto_corrente) {
            if (is_numeric($voto_corrente['voto'])) {
                if ($voto_corrente['voto'] >= $voto_min and $voto_corrente['voto'] <= $voto) {
                    $voto_min = $voto_corrente['voto'];
                }
                if ($voto_corrente['voto'] <= $voto_max and $voto_corrente['voto'] >= $voto) {
                    $voto_max = $voto_corrente['voto'];
                }
            }
        }
        $diff_positiva = $voto_max - $voto;
        $diff_negativa = $voto - $voto_min;

        //echo "voto $voto voto_min $voto_min voto_max $voto_max diff_positiva $diff_positiva diff_negativa $diff_negativa<br>";
        if ($diff_positiva <= $diff_negativa) {
            return $voto_max;
        } else {
            return $voto_min;
        }
    } else {
        return $voto;
    }
    //}}} </editor-fold>
}

function traduci_periodo($periodo) {
    //{{{ <editor-fold defaultstate="collapsed">
    switch ($periodo) {
        case '1':
            $nome_pagella = '1a pagellina infraquadrimestrale';
            break;
        case '2':
            $nome_pagella = '2a pagellina infraquadrimestrale';
            break;
        case '3':
            $nome_pagella = '3a pagellina infraquadrimestrale';
            break;
        case '4':
            $nome_pagella = '4a pagellina infraquadrimestrale';
            break;
        case '5':
            $nome_pagella = '5a pagellina infraquadrimestrale';
            break;
        case '6':
            $nome_pagella = '6a pagellina infraquadrimestrale';
            break;
        case '7':
            $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
            break;
        case '8':
            $nome_pagella = 'Pagella fine 2o trimestre';
            break;
        case '9':
            $nome_pagella = 'Pagella fine anno';
            break;
        case '10':
            $nome_pagella = 'Prove strutturate';
            break;
        case '11':
            $nome_pagella = 'Esami di licenza maestro d\'arte';
            break;
        case '21':
            $nome_pagella = '1a pagellina infraquadrimestrale';
            break;
        case '22':
            $nome_pagella = '2a pagellina infraquadrimestrale';
            break;
        case '23':
            $nome_pagella = '3a pagellina infraquadrimestrale';
            break;
        case '24':
            $nome_pagella = '4a pagellina infraquadrimestrale';
            break;
        case '25':
            $nome_pagella = '5a pagellina infraquadrimestrale';
            break;
        case '26':
            $nome_pagella = '6a pagellina infraquadrimestrale';
            break;
        case '27':
            $nome_pagella = 'Pagella fine 1o quadrimestre/trimestre';
            break;
        case '28':
            $nome_pagella = 'Pagella fine 2o trimestre';
            break;
        case '29':
            $nome_pagella = 'Pagella fine anno';
            break;
    }
    return $nome_pagella;
    //}}} </editor-fold>
}

function imposta_monteore_e_ore_assenza_studente_periodo_pagellina($id_studente, $inizio, $fine, $periodo, $importa_assenze = 'SI', $importa_monteore = 'SI') {
    // {{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre le ore di assenza per materia di uno studente in un periodo">

    if ($importa_assenze == 'SI' and $importa_monteore == 'SI') {
        $query = "select
						*
					from
						pagelline
					where
						id_studente='$id_studente'
						and
						periodo='$periodo'
						and
						flag_canc=0
					order by
						id_pagellina desc";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $id_pagellina = trim(pg_fetch_result($result, 0, "id_pagellina"));

            $ore_tot = estrai_ore_assenza_studente_periodo_nuovo($id_studente, $inizio, $fine);

            if (is_array($ore_tot)) {
                foreach ($ore_tot as $id_materia => $ore_mat) {
                    $query_int = " update
											voti_pagelline
										set";

                    if ($importa_assenze == 'SI') {
                        $query_int .= " ore_assenza = '" . intval($ore_mat['ore_assenza']) . "',";
                    }
                    if ($importa_monteore == 'SI') {
                        $query_int .= " monteore_totale = '" . intval($ore_mat['monteore_totale']) . "',";
                    }
                    $query_int = substr($query_int, 0, -1);
                    $query_int .= "
										where
											id_pagellina='$id_pagellina'
											and
											id_materia='$id_materia'
											and
											flag_canc=0";
                    $result_int = pgsql_query($query_int) or die("Invalid $query_int");
                }
            }
        }
    }

// }}} </editor-fold>
}

function estrai_parametri_stampe_pagelle() {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre i parametri di stampa delle pagelle">
    $query = "SELECT *
				FROM
					parametri_pagella";
    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);
    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $parametri_stampa_temp = pg_fetch_assoc($result, $cont);
            $parametri_stampa[$parametri_stampa_temp["codice"]] = $parametri_stampa_temp;
        }
    }
    return $parametri_stampa;
    //}}} </editor-fold>
}

function modifica_parametri_stampe_pagelle($parametri_stampa, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per modificare i parametri di stampa delle pagelle">
    foreach ($parametri_stampa as $codice => $riga) {
        $query = "SELECT codice FROM parametri_pagella
					WHERE codice='" . $codice . "'";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $query = "UPDATE parametri_pagella
						SET valore_offset = '" . intval($riga["valore_offset"]) . "'
						WHERE codice = '" . $codice . "'";

            pgsql_query($query) or die("Invalid $query");

            $mat_oggetti["codice"] = $codice;
            $tipo_sorgente = "INTERFACCIA";
            $operazione = "MODIFICA";
            inserisci_log($mat_oggetti, "parametri_pagella", $current_user, $tipo_sorgente, $operazione);
        } else {
            $query = "INSERT INTO parametri_pagella (
							codice,
							valore_offset
						) VALUES (
							'" . $codice . "',
							'" . intval($riga["valore_offset"]) . "'
						)";

            pgsql_query($query) or die("Invalid $query");

            $mat_oggetti["codice"] = $codice;
            $tipo_sorgente = "INTERFACCIA";
            $operazione = "INSERIMENTO";
            inserisci_log($mat_oggetti, "parametri_pagella", $current_user, $tipo_sorgente, $operazione);
        }
    }

    return $result;
    //}}} </editor-fold>
}

function estrai_materie_curricolo($id_classe, $periodo = 'anno_corr') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre tutti i parenti inseriti di uno studente">
    if ($id_classe > 0) {
        if ($periodo == 'anno_succ') {
            $suffisso = '_succ';
        }
        $query = "SELECT
						classi_materie_curricolo$suffisso.id_classe,
						classi_materie_curricolo$suffisso.id_materia,
						classi_materie_curricolo$suffisso.ore_totali,
						classi_materie_curricolo$suffisso.anni_corso,
						classi_materie_curricolo$suffisso.tipo_materia,
						materie.descrizione,
						materie.descrizione_materia_straniera,
						classi_materie_curricolo$suffisso.ordinamento
					FROM classi_materie_curricolo$suffisso
						INNER JOIN materie ON materie.id_materia=classi_materie_curricolo$suffisso.id_materia
					WHERE classi_materie_curricolo$suffisso.id_classe='$id_classe'
						AND materie.flag_canc=0
					ORDER BY
						classi_materie_curricolo$suffisso.ordinamento,
						materie.ordinamento,
						materie.descrizione";

        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);
        if ($numero > 0) {
            for ($cont = 0; $cont < $numero; $cont++) {
                $materie_curricolo[$cont]["id_classe"] = trim(pg_fetch_result($result, $cont, "id_classe"));
                $materie_curricolo[$cont]["id_materia"] = trim(pg_fetch_result($result, $cont, "id_materia"));
                $materie_curricolo[$cont]["ore_totali"] = trim(pg_fetch_result($result, $cont, "ore_totali"));
                $materie_curricolo[$cont]["descrizione"] = trim(pg_fetch_result($result, $cont, "descrizione"));
                $materie_curricolo[$cont]["descrizione_materia_straniera"] = trim(pg_fetch_result($result, $cont, "descrizione_materia_straniera"));
                $materie_curricolo[$cont]["anni_corso"] = trim(pg_fetch_result($result, $cont, "anni_corso"));
                $materie_curricolo[$cont]["ordinamento"] = trim(pg_fetch_result($result, $cont, "ordinamento"));
                $materie_curricolo[$cont]["tipo_materia"] = trim(pg_fetch_result($result, $cont, "tipo_materia"));
            }
        }
    }
    return $materie_curricolo;
    //}}} </editor-fold>
}

function estrai_materie_curricolo_studente($id_studente) {
    //q{{{ <editor-fold defaultstate="collapsed" desc="Funzione per estrarre tutti i parenti inseriti di uno studente">
    $query = "SELECT
					classi_materie_curricolo.id_classe,
					classi_materie_curricolo.id_materia,
					classi_materie_curricolo.ore_totali,
					classi_materie_curricolo.anni_corso,
					materie.descrizione,
					materie.descrizione_materia_straniera,
					classi_materie_curricolo.ordinamento,
					classi_materie_curricolo.tipo_materia
				FROM classi_materie_curricolo
                INNER JOIN materie ON materie.id_materia = classi_materie_curricolo.id_materia
				WHERE classi_materie_curricolo.id_classe IN (
						SELECT DISTINCT id_classe
                        FROM studenti_completi
                        WHERE id_studente = '" . $id_studente . "'
					)
					AND materie.flag_canc = 0
				ORDER BY
					classi_materie_curricolo.ordinamento,
					materie.ordinamento,
					materie.descrizione";

    $result = pgsql_query($query) or die("Invalid $query");
    $numero = pg_num_rows($result);

    if ($numero > 0) {
        for ($cont = 0; $cont < $numero; $cont++) {
            $materie_curricolo[$cont]["id_classe"] = trim(pg_fetch_result($result, $cont, "id_classe"));
            $materie_curricolo[$cont]["id_materia"] = trim(pg_fetch_result($result, $cont, "id_materia"));
            $materie_curricolo[$cont]["ore_totali"] = trim(pg_fetch_result($result, $cont, "ore_totali"));
            $materie_curricolo[$cont]["descrizione"] = trim(decode(pg_fetch_result($result, $cont, "descrizione")));
            $materie_curricolo[$cont]["descrizione_materia_straniera"] = trim(decode(pg_fetch_result($result, $cont, "descrizione_materia_straniera")));
            $materie_curricolo[$cont]["anni_corso"] = trim(pg_fetch_result($result, $cont, "anni_corso"));
            $materie_curricolo[$cont]["ordinamento"] = trim(pg_fetch_result($result, $cont, "ordinamento"));
            $materie_curricolo[$cont]["tipo_materia"] = trim(pg_fetch_result($result, $cont, "tipo_materia"));
        }
    }

    return $materie_curricolo;
    //}}} </editor-fold>
}

function traduci_crediti_totali($crediti) {
    //{{{ <editor-fold defaultstate="collapsed" desc="traduco i crediti in lettere">
    return traduci_numero_in_lettere($crediti);
    //}}} </editor-fold>
}

function inserisci_materie_curricolo($array_dati, $current_user, $periodo = 'anno_corr') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per inserire i nuovi abbinamenti del curricolo delle materie di una classe">
    if ($periodo == 'anno_succ') {
        $suffisso = '_succ';
    }

    for ($cont = 0; $cont < count($array_dati); $cont++) {
        if (
                ($array_dati[$cont]["id_classe"] > 0)
                and ( $array_dati[$cont]["id_materia"] > 0)
        ) {
            $query_select = "SELECT * FROM classi_materie_curricolo$suffisso
								WHERE id_classe='" . $array_dati[$cont]["id_classe"] . "'
									AND id_materia='" . $array_dati[$cont]["id_materia"] . "'";

            $result_select = pgsql_query($query_select) or die("Invalid $query_select");
            $numero_select = pg_num_rows($result_select);

            if ($numero_select == 0) {
                $query = "INSERT INTO classi_materie_curricolo$suffisso
							(
								id_classe,
								id_materia,
								ore_totali,
								anni_corso,
								ordinamento,
								tipo_materia
							)
							VALUES
							(
								'" . $array_dati[$cont]["id_classe"] . "',
								'" . $array_dati[$cont]["id_materia"] . "',
								'" . $array_dati[$cont]["ore_totali"] . "',
								'" . $array_dati[$cont]["anni_corso"] . "',
								'" . $array_dati[$cont]["ordinamento"] . "',
								'" . $array_dati[$cont]["tipo_materia"] . "'
							)";

                $result = pgsql_query($query);

                $mat_oggetti["id_classe"] = $array_dati[$cont]["id_classe"];
                $mat_oggetti["id_materia"] = $array_dati[$cont]["id_materia"];
                $mat_oggetti["ore_totali"] = $array_dati[$cont]["ore_totali"];
                $mat_oggetti["anni_corso"] = $array_dati[$cont]["anni_corso"];
                $mat_oggetti["tipo_materia"] = $array_dati[$cont]["tipo_materia"];
                $tipo_sorgente = "INTERFACCIA";
                $operazione = "INSERIMENTO";
                inserisci_log($mat_oggetti, "classi_materie_curricolo$suffisso", $current_user, $tipo_sorgente, $operazione);
            }
        }
    }

    return $result;
    //}}} </editor-fold>
}

function elimina_materie_curricolo($id_classe, $periodo = 'anno_corr') {
    //{{{ <editor-fold defaultstate="collapsed" desc="Funzione per eliminare le materie di un curriculo di una classe">
    if ($periodo == 'anno_succ') {
        $suffisso = '_succ';
    }

    $query = "DELETE FROM classi_materie_curricolo$suffisso
				WHERE id_classe = '$id_classe'";

    $result = pgsql_query($query) or die("Invalid $query");

    return $result;
    //}}} </editor-fold>
}

/**
 * Estrae il monteore totale e le assenze dal tabellone (se impostate)
 * @param int $id_studente
 * @param string $periodo
 * @return array $monteore_assenze
 */
function estrai_monteore_assenze_da_pagella($id_studente, $periodo = null) {
    //{{{ <editor-fold defaultstate="collapsed">
    $monteore_assenze = [];
    if (!$periodo) {
        $dati_classe = estrai_classe_principale_studente($id_studente);
        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO', (int) $dati_classe['id_classe'], 'classe');
    }

    $id_pagellina = estrai_id_pagellina($periodo, $id_studente);

    if ($id_pagellina > 0) {
        $query = "SELECT
                    SUM(monteore_totale) as tot_monteore,
                    SUM(ore_assenza) as tot_assenze
                  FROM voti_pagelline
                  WHERE id_pagellina = {$id_pagellina}
                    --AND voto_pagellina <> ''
                    AND monteore_totale <> 0
                    AND flag_canc = 0";

        $result = pgsql_query($query) or die("Invalid $query");

        $monteore_assenze = pg_fetch_assoc($result, 0);
    }
    return $monteore_assenze;
    //}}} </editor-fold>
}

/**
 * Inserisce le proposte di voto per le aree disciplinari partendo dalla media delle materie che contengono (sviluppo per il Trentino)
 *
 * @param type $periodo
 * @param type $id_classe
 * @param type $current_user
 * @return type
 */
function inserisci_proposte_voti_assenze_aree_disciplinari($periodo, $id_classe, $current_user, $arrotondamento, $sovrascrivi_voto = 'SI', $sovrascrivi_monteore = 'NO') {
    //{{{ <editor-fold defaultstate="collapsed">

    $abilita_aree_disciplinari = estrai_parametri_singoli('ABILITA_AREE_DISCIPLINARI');
    $risultato = $errori = $voto_aree_disc = [];

    if ($abilita_aree_disciplinari == 'SI') {

        $elenco_aree_disciplinari = estrai_aree_disciplinari($id_classe, 'matrice');
        $pagelle_classe = estrai_voti_tabellone_pagellina_classe_finale($id_classe, $periodo, $current_user);

        if (count($pagelle_classe) > 0) {
            // ciclo le pagelle della classe studente per studente
            foreach ($pagelle_classe as $id_studente => $pagella) {



                $stud = $voto_aree_disc = $errori = [];
                // ciclo le materie delle aree disciplinari
                foreach ($elenco_aree_disciplinari as $area_singola) {
                    $materia_principale = $area_singola['id_materia_principale'];
                    $materia_aggregata = $area_singola['id_materia_aggregata'];

                    $stud[$materia_principale]['descrizione'] = $area_singola['descrizione_principale'];
                    $stud[$materia_principale]['id_voto_pagellina'] = $pagella[$materia_principale]['id_voto_pagellina'];
                    $stud[$materia_principale]['tipo_materia'] = $area_singola['tipo_principale'];
                    $stud[$materia_principale]['id_pagellina'] = $pagella[$materia_aggregata]['id_pagellina'];

                    // Dati materia aggregata
                    $stud[$materia_principale]['materie'][$materia_aggregata]['id_materia'] = $pagella[$materia_aggregata]['id_materia'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['descrizione_materia'] = $pagella[$materia_aggregata]['descrizione_materia'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['tipo_materia'] = $pagella[$materia_aggregata]['tipo_materia'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['proposta_voto_pagellina'] = $pagella[$materia_aggregata]['proposta_voto_pagellina'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['voto_pagellina'] = $pagella[$materia_aggregata]['voto_pagellina'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['monteore_totale'] = $pagella[$materia_aggregata]['monteore_totale'];
                    $stud[$materia_principale]['materie'][$materia_aggregata]['ore_assenza'] = $pagella[$materia_aggregata]['ore_assenza'];

                    // Verifico se le aree disciplinari hanno già il voto per evitare la sovrascrittura
                    if ($sovrascrivi_voto == 'NO' && $pagella[$materia_principale]['proposta_voto_pagellina'] != '') {
                        $voto_aree_disc[] = $materia_principale;
                    }
                }

                // Calcolo medie ciclando i voti in caso siano presenti e inserisco la proposta di voto
                foreach ($stud as $key => $area_disciplinare) {
                    $materie = $area_disciplinare['materie'];
                    $stud[$key]['monteore_area'] = 0;
                    $stud[$key]['assenze_area'] = 0;
                    $stud[$key]['valido'] = true;
                    $num = 0;

                    foreach ($materie as $key2 => $materia_singola) {
                        // Se esiste la proposta voto pagellina di una materia aggregata
                        // aggiungo il voto ed il conteggio delle materie
                        if ($materia_singola['proposta_voto_pagellina'] > 0) {
                            $stud[$key]['somma_voti'] += $materia_singola['proposta_voto_pagellina'];
                            $stud[$key]['monteore_area'] += $materia_singola['monteore_totale'];
                            $stud[$key]['assenze_area'] += $materia_singola['ore_assenza'];
                            $num++;
                        } else {
                            // Se manca il voto segnalo negli errori
                            $stud[$key]['valido'] = false;

                            // Aggiungo l'errore al report solo se non è religione o non è un'area disciplinare
                            if (!array_key_exists($materia_singola['id_materia'], $elenco_aree_disciplinari) && $area_disciplinare['tipo_materia'] != "RELIGIONE" && $area_disciplinare['tipo_materia'] != "ALTERNATIVA") {
                                $errori[$materia_singola['id_materia']] = "Proposta voto mancante per la materia <b>" .
                                        $materia_singola['descrizione_materia'] . "</b>"
                                        . " aggregata all'area disciplinare <b>" . $pagella[$key]['descrizione_materia'] . "</b>";
                            }
                        }
                    }
                    $stud[$key]['numero_voti'] = $num;
                    $stud[$key]['media'] = arrotonda_media(($stud[$key]['somma_voti'] / $num), $arrotondamento);

                    // Se ci sono voti validi inserisco la proposta di voto
                    if ($num > 0 && $stud[$key]['somma_voti'] > 0 &&
                            (
                            $stud[$key]['valido'] == true || ($stud[$key]['tipo_materia'] == "RELIGIONE" && $stud[$key]['tipo_materia'] == "ALTERNATIVA")
                            )
                    ) {
                        // Inserimento della proposta di voto con i dati dell'area disciplinare
                        // (forzo come "professore" per inserire le proposte e non solo i voti)
                        // Controllo se il voto va sovrascritto
//                        if(!in_array($key, $voto_aree_disc)) {
                        inserisci_voto_pagellina(
                                $stud[$key]['media'], '', $key, $stud[$key]['id_pagellina'], $stud[$key]['assenze_area'], time(), '', '', '', $current_user, '', '', '', 'professore', $stud[$key]['monteore_area'], $sovrascrivi_monteore, $sovrascrivi_voto
                        );
                        // debug
                        $errori[$key] = "Le materie dell'area disciplinare " . $area_disciplinare['descrizione'] . " sono state inserite";
//                        } else {
                        // debug
//                            $errori[$key] = "Le materie dell'area disciplinare " . $area_disciplinare['descrizione'] . " hanno il voto e non sono state sovrascritte";
//                        }
                    } else {
                        // debug
                        $errori[$key] = "Le materie dell'area disciplinare " . $area_disciplinare['descrizione'] . " non presentano proposte di voto inserite";
                    }
                }
                $risultato['errore'] = '';
                $risultato['risultato'][$id_studente] = $errori;
            }
        } else {
            $risultato['errore'] = "Non sono presenti pagelle per il periodo selezionato.";
        }
    }

    return $risultato;
    //}}} </editor-fold>
}

/**
 * Estrae i valori disponibili per il consiglio orientativo del trentino
 * (se viene passato il parametro estrae il singolo valore)
 * @param integer $valore
 * @return array
 */
function estrai_valori_consiglio_orientativo_trentino($valore = '') {
    //{{{ <editor-fold defaultstate="collapsed">
    // Se il filtro è vuoto non estraggo il valore Nessuna scelta comunicata
    $filtro_valore = $valore != "" ? " AND id_consiglio_orientativo_trentino in ({$valore}) " : " AND id_consiglio_orientativo_trentino > 0";

    $query = "SELECT id_consiglio_orientativo_trentino,
                    descrizione,
                    ordinamento
                FROM consiglio_orientativo_trentino
                WHERE flag_canc = 0
                    {$filtro_valore}
                ORDER BY ordinamento";

    $result = pgsql_query($query) or die("Invalid $query");

    $elenco_valori = pg_fetch_all($result);

    return $elenco_valori;
    //}}} </editor-fold>
}

/**
 * Aggiorna il consiglio integrativo del trentino sullo studente
 *
 * @global integer $current_user
 * @param integer $id_studente
 * @param array $consiglio_orientativo_trentino
 */
function aggiorna_consiglio_orientativo_trentino($id_studente, $consiglio_orientativo_trentino) {
    //{{{ <editor-fold defaultstate="collapsed">
    global $current_user;

    $valore = !empty($consiglio_orientativo_trentino) ? implode(',', $consiglio_orientativo_trentino) : 0;

    $query = "UPDATE studenti
                SET consiglio_orientativo_trentino = '{$valore}'
                WHERE id_studente = {$id_studente}";

    pgsql_query($query) or die("Invalid $query");

    $mat_oggetti["id_studente"] = $id_studente;
    $tipo_sorgente = "INTERFACCIA";
    $operazione = "MODIFICA";

    inserisci_log($mat_oggetti, 'studenti', $current_user, $tipo_sorgente, $operazione);
    //}}} </editor-fold>
}

/**
 * Estrae la lista degli esiti disponibili per una determinata classe
 *
 * @param integer $id_classe
 * @return array
 */
function estrai_esiti_disponibili($id_classe = null) {
    //{{{ <editor-fold defaultstate="collapsed">
    $elenco_esiti_disponibili = [];
    //fatta modifica per covid
    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
    $anno = explode('/', $anno_scolastico);
    if ($anno[1] == 2020) {
        $escludi_sospesi = " AND elenco_esiti.valore != 'sospeso' ";
    }
    if ($anno[1] != 2020) {
        $escludi_om = " AND elenco_esiti.descrizione != 'Ammesso secondo O.M. 11/2020' ";
    }
// Se viene passata la classe
    if ($id_classe) {
        $controllo_classe = " AND (tipo_indirizzo, anno_classe) IN (
                                    SELECT tipo_indirizzo::int, classe::int
                                        FROM classi_complete
                                        WHERE id_classe = {$id_classe}
                                            AND tipo_indirizzo != 'CORSO'
                                        )
                                        ";
    }

    $query = "SELECT elenco_esiti.id_esito,
                    elenco_esiti.valore,
                    elenco_esiti.descrizione,
                    esiti_classi.anno_classe,
                    esiti_classi.tipo_indirizzo,
                    esiti_classi.ordinamento
                FROM elenco_esiti,
                    esiti_classi
                WHERE elenco_esiti.id_esito = esiti_classi.id_esito
                    {$controllo_classe}
                    {$escludi_sospesi}
                    {$escludi_om}
                ORDER BY esiti_classi.tipo_indirizzo,
                    esiti_classi.anno_classe,
                    esiti_classi.ordinamento
                ";

    $result = pgsql_query($query) or die("Invalid $query");

    $numero = pg_num_rows($result);
    if ($numero > 0) {
        $elenco_esiti = pg_fetch_all($result);

        foreach ($elenco_esiti as $esito) {
            $id_esito = $esito['id_esito'];

            // Se sono Infanzia, Primarie, Secondarie I Grado converto l'id con SI/NO
            if (in_array($esito['tipo_indirizzo'], [4, 6, 7])) {
                if ($esito['valore'] == 'positivo') {
                    $id_esito = 'SI';
                } elseif ($esito['valore'] == 'negativo') {
                    $id_esito = 'NO';
                }
            }

            // Popolo l'array finale
            $elenco_esiti_disponibili[$id_esito] = [
                'id_esito' => $id_esito,
                'valore' => $esito['valore'],
                'descrizione' => $esito['descrizione'],
                'anno_classe' => $esito['anno_classe'],
                'tipo_indirizzo' => $esito['tipo_indirizzo'],
                'ordinamento' => $esito['ordinamento'],
            ];
        }
    }

    return $elenco_esiti_disponibili;
//}}} </editor-fold>
}

/**
 * Estrai i dati relativi ad un singolo esito
 * la funzione NON deve essere utilizzata per gli esiti di:
 * - Infanzia
 * - Primaria
 * - Secondaria I Grado
 *
 * @param type $id_esito
 * @return array $esito
 */
function estrai_singolo_esito($id_esito) {
    //{{{ <editor-fold defaultstate="collapsed">
    $esito = [];

    if (is_numeric($id_esito)) {
        // se l'id_esito è numerico lo estraggo
        $query = "SELECT id_esito,
                        valore,
                        descrizione
                    FROM elenco_esiti
                    WHERE id_esito  = {$id_esito}
                    LIMIT 1
                ";
        $result = pgsql_query($query) or die("Invalid $query");
        $numero = pg_num_rows($result);

        if ($numero > 0) {
            $esito_estratto = pg_fetch_assoc($result, 0);
            // Se non trovo l'esito da convertire
            $esito = [
                'id_esito' => $esito_estratto['id_esito'],
                'valore' => $esito_estratto['valore'],
                'descrizione' => $esito_estratto['descrizione'],
            ];
        } else {
            // Se non trovo l'esito da convertire
            $esito = [
                'id_esito' => $id_esito,
                'valore' => 'errore',
                'descrizione' => 'Errore - Esito non censito',
            ];
        }
    } else {
        // Se l'id esito è vuoto
        $esito = [
            'id_esito' => $id_esito,
            'valore' => 'errore',
            'descrizione' => 'Errore - Id esito vuoto',
        ];
    }

    return $esito;
    //}}} </editor-fold>
}

/**
 * Salva l'esito finale di uno studente in caso di esito manuale
 * @param int $id_studente
 * @param int $esito_finale
 * @param int $anno_reale_classe
 * @param int $current_user
 * @return type
 */
function modifica_esito_finale_studente($id_studente, $esito_finale, $anno_reale_classe, $current_user, $sovrascrivi_modifiche = 'SI') {
    //{{{ <editor-fold defaultstate="collapsed">
    if (intval($id_studente) > 0) {
        $campo_da_modificare = '';
        switch ($anno_reale_classe) {
            case '1':
                $campo_da_modificare = 'prima';
                break;
            case '2':
                $campo_da_modificare = 'seconda';
                break;
            case '3':
                $campo_da_modificare = 'terza';
                break;
            case '4':
                $campo_da_modificare = 'quarta';
                break;
            case '5':
                $campo_da_modificare = 'quinta';
                break;
        }

        if ($campo_da_modificare != '') {
            // Se l'esito è vuoto lo imposto a null
            $esito = $esito_finale == 0 ? "null" : intval($esito_finale);
            $cond = '';
            if ($sovrascrivi_modifiche == 'NO') {
                // se è a no nel where verifico che non sia stato gia impostato
                // così lo imposto solo la prima volta
                $cond = " AND
                        (esito_" . $campo_da_modificare . "_superiore is null
                            OR
                        esito_" . $campo_da_modificare . "_superiore = 0)";
            }

            $query = "UPDATE studenti
						SET esito_" . $campo_da_modificare . "_superiore = {$esito}
						WHERE id_studente = " . intval($id_studente) . $cond;
            pgsql_query($query) or die("Invalid $query");

            inserisci_log(["id_studente" => intval($id_studente)], "studenti", $current_user, "INTERFACCIA", "MODIFICA");

            $esito_impostato = estrai_singolo_esito($esito_finale);

            $cosa = "Impostato esito manuale " . $esito_impostato['descrizione'] . " per lo studente {$id_studente}";
            inserisci_log_storico($current_user, 'GESTIONE_PAGELLE', $cosa);

            // Genero Carenze/Recuperi se l'esito è positivo
            if ($esito_impostato['valore'] == 'positivo') {
                genera_carenze_recuperi_da_esito_manuale($id_studente, $current_user);
            }

            return $id_studente;
        }
    }
    //}}} </editor-fold>
}

/**
 * Estrae la pagellina di uno studente per un dato periodo
 * @param int $id_studente
 * @param string $periodo
 * @return array
 */
function estrai_pagellina_studente($id_studente, $periodo) {
    //{{{ <editor-fold defaultstate="collapsed">
    $dati_pagellina = [];

    $query_pagellina = "SELECT * FROM pagelline
                            WHERE periodo = '{$periodo}'
                                AND id_studente = {$id_studente}
                                AND flag_canc = 0
                        ";
    $result_pagellina = pgsql_query($query_pagellina) or die("Invalid $query_pagellina");

    $numero = pg_num_rows($result_pagellina);

    // Deve esserci una sola pagellina
    if ($numero == 1) {
        $dati_pagellina = pg_fetch_assoc($result_pagellina, 0);
        $id_pagellina = $dati_pagellina['id_pagellina'];

        $query_voti = "SELECT *
                        FROM voti_pagelline
                        WHERE flag_canc = 0
                            AND id_pagellina = {$id_pagellina}
                        ";
        $result_voti = pgsql_query($query_voti) or die("Invalid $query_voti");

        $elenco_voti = pg_fetch_all($result_voti);

        foreach ($elenco_voti as $voto) {
            $dati_pagellina['voti'][$voto['id_materia']] = $voto;
        }
    }

    return $dati_pagellina;
    //}}} </editor-fold>
}

/**
 * Funzione per la generazione di carenze in caso di impostazione manuale dell'esito
 * @param int $id_studente
 */
function genera_carenze_recuperi_da_esito_manuale($id_studente, $current_user) {
    //{{{ <editor-fold defaultstate="collapsed">
    $trentino_abilitato = Verifica_trentino_abilitato();
    $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $ammessi_tutti = estrai_parametri_singoli('AMMESSI_TUTTI_SU_APRI_SCRUTINIO');

    if ($trentino_abilitato == 'SI') {
        // Scuole del Trentino
        if ($ammessi_tutti != 'SI') {
            $carenze_recuperi_inseriti = genera_carenze_trentino($id_studente, $current_user, $anno_scolastico);
        }
    } else {
        // Altre scuole
        // !!! DA FARE PER IL PROSSIMO ANNO !!!
    }


    // Log inserimento delle carenze/recuperi
    if (count($carenze_recuperi_inseriti) > 0) {
        $cosa = "Generate " . count($carenze_recuperi_inseriti) . " carenze/recuperi per lo studente {$id_studente}";
        inserisci_log_storico($current_user, 'GESTIONE_PAGELLE', $cosa, "", $carenze_recuperi_inseriti);
    }
    //}}} </editor-fold>
}

/**
 * Genera i debiti del Trentino in caso di ammissione manuale e voti insufficienti
 * @param int $id_studente
 * @param string $anno_scolastico
 */
function genera_carenze_trentino($id_studente, $current_user, $anno_scolastico = null) {
    //{{{ <editor-fold defaultstate="collapsed">
    $debiti_inseriti = [];

    if (!$anno_scolastico) {
        $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    }

    $scala_voti = estrai_parametri_singoli('SCALA_VOTI');

    // Estraggo i debiti e recupero le materie
    $debiti_studente = estrai_debiti_studente($id_studente, $anno_scolastico);

    $materie_debito = [];
    foreach ($debiti_studente as $debito) {
        $id_materia_debito = $debito['id_materia'];
        $materie_debito[$id_materia_debito] = $id_materia_debito;
    }

    // Estraggo la pagellina
    $date_pagellina = estrai_pagellina_studente($id_studente, '9');

    // Ciclo i voti
    foreach ($date_pagellina['voti'] as $voto_pagellina) {
        $voto = intval($voto_pagellina['voto_pagellina']);

        // Se il voto è in centinaia lo dividiamo
        if ($scala_voti == 'CENTINAIA') {
            $voto = intval($voto / 10);
        }

        $materia = estrai_dati_materia($voto_pagellina['id_materia']);

        if ($voto < 6 && $voto > 0 && !in_array($voto_pagellina['id_materia'], $materie_debito) && $materia['in_media_pagelle'] == 'SI' && !in_array($materia['tipo_materia'], ['RELIGIONE', 'ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO'])
        ) {
            //Voto insufficiente e senza debito - Genero il debito nuovo

            $tipo_carenza = intval($voto) == 5 ? "carenza" : "carenza_grave";

            inserisci_debito($voto_pagellina['id_materia'], encode($materia['descrizione']), $id_studente, 'CON VERIFICA', 'NO', $anno_scolastico, $current_user, $tipo_carenza
            );
            $debiti_inseriti[] = [
                'id_studente' => $id_studente,
                'id_materia' => $voto_pagellina['id_materia'],
                'anno_scolastico' => $anno_scolastico,
                'tipo_carenza' => $tipo_carenza,
                'current_user' => $current_user
            ];
        }
    }

    return $debiti_inseriti;
    //}}} </editor-fold>
}

/**
 * Genera l'output della stampa delle pagelle personalizzate in base a quello che si è selezionato (sito genitori, zip, segreteria digitale, inserimento massivo, pdf)
 * @param string $output_stampa (arriva dal tpl o viene forzano manualmente in caso di inserimento massivo)
 * @param string $periodo_pagella
 * @param array $elenco_studenti
 * @param array $parametri_stampa
 * @param string $tipo_stampa
 * @param string $formato
 * @param string $orientamento
 */
function output_stampa($output_stampa, $periodo_pagella, $elenco_studenti, $parametri_stampa, $tipo_stampa, $formato = "A4", $orientamento = 'P') {
    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
    $anno_inizio = explode('/', $anno_scolastico)[0];
    $anno_fine = explode('/', $anno_scolastico)[1];

    switch ($output_stampa) {
        case "SITO_GENITORI":
            // Upload sito genitori

            $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
            $pagelle_generate = [];

            foreach ($elenco_studenti as $studente) {
                $id_stud_per_stampa_sito = $studente['id_studente'];
                $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
                genera_stampa($pdf, $studente, $parametri_stampa);
                $file_name = $nome_pagella_per_file . '.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                $content = file_get_contents($pagella);

                // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                if ($file[0]['id']) {
                    messengerUpdateFile($file[0]['id'], $content);
                } else {
                    // Destinatari: Studente + genitori
                    $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                    messengerSaveFile([
                        'content' => $content,
                        'hidden' => false,
                        'mime' => 'application/pdf',
                        'name' => $file_name,
                        'owner' => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year' => "{$anno_inizio}/{$anno_fine}",
                            'period' => $periodo_pagella
                        ],
                        'recipients' => $recipients,
                        'tags' => ['PAGELLE']
                    ]);
                }

                if (file_exists($pagella)) {
                    $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                    unlink($pagella);
                }
            }

            if (empty($pagelle_generate)) {
                echo "Errore generazione pagelle (contattare l'Assistenza)";
            } else {
                echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
            }
            break;

        case "ZIP":
            // ZIP per firma digitale
            $rel_dir = 'tmp_pdf/';
            $base_dir = '/var/www-source/mastercom/' . $rel_dir;
            $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
            $dir = $temp_dir . '/';

            exec('mkdir ' . $temp_dir);

            //cancello tutte i file temporanei fatti da più di 1 ora
            exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
            foreach ($elenco_studenti as $studente) {
                $id_stud_per_stampa_sito = $studente['id_studente'];

                $cf_studente = $studente['codice_fiscale'];
                $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
                $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
                genera_stampa($pdf, $studente, $parametri_stampa);
                $pdf->Output($file, "F");
            }
            $nome = 'export' . date('YmdHi') . '.zip';
            exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
            exec('rm -fr ' . $dir);
            //Reindirizzamento JavaScript
            echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
            break;

        case "SEGRETERIA_DIGITALE":
            //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
            $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
            $pagelle_generate = [];
            $external_data = [];

            foreach ($elenco_studenti as $studente) {
                $id_stud_per_stampa_sito = $studente['id_studente'];
                $classe_studente = $studente['classe'] . $studente['sezione'] . ' - ' . $studente['descrizione_indirizzi'];
                $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
                genera_stampa($pdf, $studente, $parametri_stampa);
                $file_name = $nome_pagella_per_file . '_' . strtoupper($studente['cognome']) . '_' . strtoupper($studente['nome']) . '.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                $dati_classe = estrai_classe_principale_studente($studente['id_studente']);
                $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO', (int) $dati_classe['id_classe'], 'classe');

                if ($periodo == '29' || $periodo == '9') {
                    $descrizione_periodo = "finale";
                } else {
                    $descrizione_periodo = "intermedia";
                }

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
                $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file . '_*.pdf');

                foreach ($pagelle as $pagella) {
                    $external_data[basename($pagella)] = [
                        'hidden' => false,
                        'mime' => 'application/pdf',
                        'name' => basename($pagella),
                        'class' => $classe_studente,
                        'owner' => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year' => "{$anno_inizio}/{$anno_fine}",
                            'period' => $descrizione_periodo
                        ],
                        'recipients' => $recipients,
                        'tags' => ['PAGELLE']
                    ];
                }
            }

            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file . '_*.pdf');

            $url_send_file = get_mc2_url()."/mc2-api/archive/document";
            $data = [
                'origin_id' => 3,
                'dossier' => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
            ];

            $i = 0;
            foreach ($pagelle as $pagella) {
                $data['file[' . $i . ']'] = new CURLFile($pagella);
                $i++;
            }

            $data['external_data'] = json_encode(array_values($external_data));

            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url_send_file);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
            curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
            curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 100);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

            $result = curl_exec($ch);

            if ($result === FALSE) {
                echo "Errore durante l\'invio della pagella.";
                curl_close($ch);
            } else {
                echo 'Pagella inviata correttamente.';
                curl_close($ch);
            }
            //}}} </editor-fold>
            break;

        case "INSERIMENTO_MASSIVO":
            print "<pre>";
            print "ciao";
            print "</pre>";
            break;

        default:
            // Caso stampa classica - un PDF con tutte le pagelle
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            foreach ($elenco_studenti as $studente) {
                genera_stampa($pdf, $studente, $parametri_stampa);
            }
            $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
            break;
    }
}

/**
 * La funzione estra i dati del tabellone di una classe o uno studente relativo alle competenze
 * @param int $id_classe (id della classe del che si vuole estrarre o della classe della quale fa parte lo studente)
 * @param string $periodo
 * @param string $current_user
 * @param string $current_key
 * @param string $form_stato (amministratore o professore per filtrare o meno le materie)
 * @param int $id_studente_selezionato (id_studente specifico se necessario)
 */
function estrai_tabellone_classe_competenze($id_classe, $periodo, $current_user, $current_key, $form_stato = 'amministratore', $id_studente_selezionato = null, $estrai_campi_liberi_tutti_studenti = false, $estrai_competenze_scrutini_precedenti = false){
    $anno_scolastico = estrai_parametri_singoli('ANNO_SCOLASTICO_ATTUALE');
    $parametro_visualizza_materie_tabellone = estrai_parametri_singoli("VISUALIZZA_MATERIE_TABELLONE");
    $coordinatore = verifica_coordinatore_classe((int) $id_classe, (int) $current_user);
    $dati_classe = estrai_classe($id_classe);
    $elenco_studenti = estrai_studenti_classe_registro($id_classe, null, false, 'rapida');
    $elenco_materie = estrai_materie_multi_classe($id_classe);

    // elimino le materie nv
    foreach ($elenco_materie as $key => $mat){
        if ($mat['in_media_pagelle'] == 'NV') {
            unset($elenco_materie[$key]);
        }
    }

    $array_id_materie = array_column($elenco_materie, 'id_materia');
    $array_generale = [];

    if ($id_studente_selezionato > 0) {
        $estrai_campi_liberi_tutti_studenti = true;
        $elenco_studenti = array_filter($elenco_studenti, function ($studente) use ($id_studente_selezionato) {
            if ($studente['id_studente'] == $id_studente_selezionato) {
                return $studente;
            }
        });
        $elenco_studenti = array_values($elenco_studenti);
        $array_generale['id_studente_selezionato'] = $id_studente_selezionato;

        // SPOSTATO nel ciclo degli studenti sotto cosi' da non ripetere l'estrazione dei voti pagellina
        // $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $id_studente_selezionato, $periodo, (int) $current_user, 'studente');
        // $id_pagellina = $elenco_voti_pagelline[$id_studente_selezionato][array_keys($elenco_voti_pagelline[$id_studente_selezionato])[0]]['id_pagellina'];
        // $template->assign('id_pagellina', $id_pagellina);
    }

    $array_generale['elenco_studenti'] = $elenco_studenti;
    $array_generale['elenco_materie'] = $elenco_materie;

    foreach ($elenco_studenti as $key_studente => $studente) {
        $ore_assenza_totali_periodo_selezionato =0;
        $minuti_assenza_totali_periodo_selezionato = 0;
        $ore_monteore_totale_periodo_selezionato = 0;
        $minuti_monteore_totale_periodo_selezionato = 0;

        // estraggo le competenze
        $parametri = [
            "anno_scolastico" =>    $anno_scolastico,
            "estrai_valutazioni_figli"  =>  'NO'
        ];

        if ($id_studente_selezionato > 0) {
            $parametri['estrai_valutazioni_figli'] = 'SI';
        }

        $periodo_api = ($periodo > 20) ? $periodo - 20 : $periodo;

        // ATTENZIONE!! Se impostato per l'estrazione, occorre dividere/filtrare le competenze a valle in base al periodo nel quale devono comparire
        if ($estrai_competenze_scrutini_precedenti){
            switch ($periodo_api) {
                case 8:
                    $parametri['periodi_aggiuntivi'] = [7];
                    break;
                case 9:
                    $parametri['periodi_aggiuntivi'] = [7, 8];
                    break;
                default:
                    break;
            }
        }

        $competenze_scrutinio = nextapi_call('competenze_studente/dati_scrutinio/' . $periodo_api . '/' . $studente['id_studente'], 'GET', $parametri, $current_key);

        // per sbloccare le percentuali assenze nel tabellone generale scommentare le riga sotto
        $elenco_voti_pagelline = estrai_voti_tabellone_pagellina_classe_finale((int) $studente['id_studente'], $periodo, (int) $current_user, 'studente');

        if ($id_studente_selezionato > 0) {
            $id_pagellina = $elenco_voti_pagelline[$id_studente_selezionato][array_keys($elenco_voti_pagelline[$id_studente_selezionato])[0]]['id_pagellina'];
            $array_generale['id_pagellina'] = $id_pagellina;
            $elenco_id_competenze_scrutinio = array_column($competenze_scrutinio['elenco'], 'id');
        }

        if ($estrai_campi_liberi_tutti_studenti){
            //campi liberi
            $mat_campi_liberi_base = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente);
            $mat_campi_liberi_figli = estrai_elenco_campi_liberi((int) $id_classe, $periodo, (int) $id_studente, 'figli');

            $mat_campi_liberi = [];
            $mat_campi_liberi_parenti = [];

            foreach ($mat_campi_liberi_base as $campo_libero) {
                $trovato = false;
                $trovato_figlio = false;
                foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
                    if ($campo_libero_figlio['id_padre'] == $campo_libero['id_campo_libero']) {
                        $mat_campi_liberi_parenti[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
                        $trovato = true;
                    }
                    if ($campo_libero_figlio['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                        $trovato_figlio = true;
                    }
                }
                if (!$trovato && !$trovato_figlio) {
                    $mat_campi_liberi[$campo_libero['id_campo_libero'] . "_" . $campo_libero['id_materia']] = $campo_libero;
                }
            }

            foreach ($mat_campi_liberi_parenti as $key_parenti => $campo_libero_parente) {
                foreach ($mat_campi_liberi_figli as $campo_libero_figlio) {
                    if ($campo_libero_figlio['id_padre'] == $campo_libero_parente['id_campo_libero']) {
                        $mat_campi_liberi_parenti[$key_parenti]['figli'][] = $campo_libero_figlio;
                    }
                }
            }
        }

        foreach ($array_generale['elenco_materie'] as $key_materia => $materia) {
            // $array_generale['elenco_materie'][$materia['id_materia']]['dati_materia'] = $materia;
            $array_generale['elenco_materie'][$key_materia]['elenco_studenti'][$studente['id_studente']] = $studente['id_studente'];

            foreach ($competenze_scrutinio['elenco'] as $key_comp => $competenza) {

                // ------------ VERIFICA LIMITI SU SINGOLA MATERIA
                $competenza_singola_materia = false;

                // pulisco dai vincoli le materie che non centrano con la classe di modo da capire in modo piu' efficace se e' trasversale o "finta" perche' comprende materie di altre classi (tipo arte italiano e arte medie)
                foreach ($competenza['vincoli']['materie'] as $id_mat_vincolo => $vincolo) {
                    if (!in_array($id_mat_vincolo, $array_id_materie)) {
                        unset($competenza['vincoli']['materie'][$id_mat_vincolo]);
                    }
                }
                foreach ($competenza['vincoli_ereditati'] as $key => $vincolo_ereditato) {
                    foreach ($vincolo_ereditato['materie'] as $id_mat_vincolo => $vincolo) {
                        if (!in_array($id_mat_vincolo, $array_id_materie)) {
                            unset($competenza['vincoli_ereditati'][$key]['materie'][$id_mat_vincolo]);
                        }
                    }
                        
                    if (count($competenza['vincoli_ereditati'][$key]['materie']) == 0){
                        unset($competenza['vincoli_ereditati'][$key]);
                    }
                }

                if (!isset($competenza['vincoli_ereditati']) || count($competenza['vincoli_ereditati']) == 0) {
                    if (count($competenza['vincoli']['materie']) == 1 && isset($competenza['vincoli']['materie'][$materia['id_materia']])) {
                        $competenza_singola_materia = true;
                    }
                } else {
                    foreach ($competenza['vincoli_ereditati'] as $vincoli_padri) {
                        if (count($vincoli_padri['materie']) == 1 && isset($vincoli_padri['materie'][$materia['id_materia']])) {
                            // almeno un padre nella gerarchia con vincolo su singola materia
                            $competenza_singola_materia = true;
                            break;
                        }
                    }
                }

                if ($competenza_singola_materia) {
                    // competenza con vincolo unico sulla materia
                    $compt_tmp_dati_generali = $competenza;
                    unset($compt_tmp_dati_generali['valutazioni_figli']);
                    unset($compt_tmp_dati_generali['scrutinio']);
                    foreach ($compt_tmp_dati_generali['schema_valutazioni'] as $key_schema => $val) {
                        if ($val['tipo'] != 'scrutini') {
                            unset($compt_tmp_dati_generali['schema_valutazioni'][$key_schema]);
                        }
                    }
                    if (!isset($array_generale['elenco_materie'][$key_materia]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']])) {
                        $array_generale['elenco_materie'][$key_materia]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']] = $compt_tmp_dati_generali;
                    }

                    // unisco valutazioni della competenze con quelle dei figli raggruppandole per data
                    $tot_valutazioni = [];
                    $tot_valutazioni_tmp = $competenza['valutazioni_figli'];

                    //pulisco le valutazioni di figli presenti in scrutinio
                    if (count($tot_valutazioni_tmp > 0)) {
                        foreach ($tot_valutazioni_tmp as $data => $valutazioni) {
                            foreach ($valutazioni as $key => $valutazione) {
                                if (in_array($valutazione['dati_competenza']['id'], $elenco_id_competenze_scrutinio)) {
                                    unset($tot_valutazioni_tmp[$data][$key]);
                                    if (count($tot_valutazioni_tmp[$data]) == 0) {
                                        unset($tot_valutazioni_tmp[$data]);
                                    }
                                }
                            }
                        }
                    }

                    if (count($competenza['valutazioni']) > 0) {
                        foreach ($competenza['valutazioni'] as $valutazione) {
                            if (!isset($tot_valutazioni_tmp[$valutazione['data']][$competenza['id']])) {
                                $tot_valutazioni_tmp[$valutazione['data']][$competenza['id']]['dati_competenza']['figlia'] = 'NO';
                            }
                            $tot_valutazioni_tmp[$valutazione['data']][$competenza['id']]['valutazioni'][] = $valutazione;
                        }
                    }
                    ksort($tot_valutazioni_tmp);

                    // imposto le date nel formato italiano
                    foreach ($tot_valutazioni_tmp as $data_iso => $val) {
                        $tot_valutazioni[date("d/m/Y", strtotime($data_iso))] = $val;
                    }

                    //$array_generale['elenco_materie'][$key_materia]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']]['elenco_studenti'][$studente['id_studente']]['valutazioni_figli'] = $competenza['valutazioni_figli'];
                    $array_generale['elenco_materie'][$key_materia]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']]['elenco_studenti'][$studente['id_studente']]['valutazioni_totali'] = $tot_valutazioni;
                    $array_generale['elenco_materie'][$key_materia]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']]['elenco_studenti'][$studente['id_studente']]['scrutinio'] = $competenza['scrutinio'];
                    unset($competenze_scrutinio['elenco'][$key_comp]);
                }
            }

            if (isset($elenco_voti_pagelline[$studente['id_studente']][$materia['id_materia']])) {
                $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo] = $elenco_voti_pagelline[$studente['id_studente']][$materia['id_materia']];
                $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_ore'] = floor($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale'] / 60);
                $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'] = $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale'] % 60;
                $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_ore'] = floor($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza'] / 60);
                $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'] = $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza'] % 60;

                $ore_assenza_totali_periodo_selezionato += $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_ore'];
                $minuti_assenza_totali_periodo_selezionato += $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'];
                $ore_monteore_totale_periodo_selezionato += $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_ore'];
                $minuti_monteore_totale_periodo_selezionato += $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'];

                if ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale'] > 0) {
                    $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['percentuale_assenze'] = floor($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza'] * 100 / $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale']) . '%';
                }

                if ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'] < 9) {
                    $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'] = '0' . $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['monteore_totale_minuti'];
                }
                if ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'] < 9) {
                    $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'] = '0' . $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['ore_assenza_minuti'];
                }
            }


            if (
                (
                    (is_array($mat_campi_liberi_base)
                        and (!empty($mat_campi_liberi_base))
                    )
                    ||
                    (is_array($mat_campi_liberi_parenti)
                        and (!empty($mat_campi_liberi_parenti))
                    )
                )
                and ($dati_classe['livello_abilitazione_campi_liberi'] == 'totale'
                    or
                    ($dati_classe['livello_abilitazione_campi_liberi'] == 'condotta'
                        and
                        $materia['tipo_materia'] == 'CONDOTTA'
                    )
                )
            ) {
                foreach ($mat_campi_liberi as $key => $campo_libero) {
                    if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia']) {
                        if (count($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi']) > 0) {
                            foreach ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi'] as $campo_valutazione) {
                                if ($campo_valutazione['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                                    $campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] = $campo_valutazione['id_valore_campo_libero'];
                                    $campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] = $campo_valutazione['id_valore_precomp'];
                                    $campo_libero['valore'][$materia['id_materia']]['valore'] = $campo_valutazione['valore'];
                                    break;
                                }
                            }
                        }

                        $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['elenco_campi_liberi']['normali'][] = $campo_libero;
                    }
                }
                foreach ($mat_campi_liberi_parenti as $key => $campo_libero) {
                    if ($campo_libero['id_materia'] == 0 or $campo_libero['id_materia'] == $materia['id_materia']) {
                        if (count($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi']) > 0) {
                            foreach ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi'] as $campo_valutazione) {
                                if ($campo_valutazione['id_campo_libero'] == $campo_libero['id_campo_libero']) {
                                    $campo_libero['valore'][$materia['id_materia']]['id_valore_campo_libero'] = $campo_valutazione['id_valore_campo_libero'];
                                    $campo_libero['valore'][$materia['id_materia']]['id_valore_precomp'] = $campo_valutazione['id_valore_precomp'];
                                    $campo_libero['valore'][$materia['id_materia']]['valore'] = $campo_valutazione['valore'];
                                    break;
                                }
                            }
                        }

                        foreach ($campo_libero['figli'] as $key => $campo_libero_figlio) {
                            if (count($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi']) > 0) {
                                foreach ($array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['campi_liberi'] as $campo_valutazione) {
                                    if ($campo_valutazione['id_campo_libero'] == $campo_libero_figlio['id_campo_libero']) {
                                        $campo_libero['figli'][$key]['valore'][$materia['id_materia']]['id_valore_campo_libero'] = $campo_valutazione['id_valore_campo_libero'];
                                        $campo_libero['figli'][$key]['valore'][$materia['id_materia']]['id_valore_precomp'] = $campo_valutazione['id_valore_precomp'];
                                        $campo_libero['figli'][$key]['valore'][$materia['id_materia']]['valore'] = $campo_valutazione['valore'];
                                        break;
                                    }
                                }
                            }
                        }

                        $array_generale['elenco_materie'][$key_materia]['voti_pagelline'][$studente['id_studente']][$periodo]['elenco_campi_liberi']['parenti'][] = $campo_libero;
                    }
                }
            }
        }

        $array_generale['elenco_studenti'][$key_studente]['ore_assenza_totali_periodo_selezionato'] = ($ore_assenza_totali_periodo_selezionato + floor($minuti_assenza_totali_periodo_selezionato / 60)) . ':' . ($minuti_assenza_totali_periodo_selezionato % 60);
        $array_generale['elenco_studenti'][$key_studente]['monteore_totali_periodo_selezionato'] = ($ore_monteore_totale_periodo_selezionato + floor($minuti_monteore_totale_periodo_selezionato / 60)) . ':' . ($minuti_monteore_totale_periodo_selezionato % 60);

        if (count($competenze_scrutinio['elenco']) > 0) {
            // trasversali
            $array_generale['elenco_materie'][-1]['id_materia'] = -1;
            $array_generale['elenco_materie'][-1]['descrizione'] = 'TRASVERSALI';
            $array_generale['elenco_materie'][-1]['elenco_studenti'][$studente['id_studente']] = $studente['id_studente'];
            foreach ($competenze_scrutinio['elenco'] as $key_comp => $competenza) {
                $compt_tmp_dati_generali = $competenza;
                unset($compt_tmp_dati_generali['valutazioni_figli']);
                unset($compt_tmp_dati_generali['scrutinio']);
                foreach ($compt_tmp_dati_generali['schema_valutazioni'] as $key_schema => $val) {
                    if ($val['tipo'] != 'scrutini') {
                        unset($compt_tmp_dati_generali['schema_valutazioni'][$key_schema]);
                    }
                }
                if (!isset($array_generale['elenco_materie'][-1]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']])) {
                    $array_generale['elenco_materie'][-1]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']] = $compt_tmp_dati_generali;
                }

                // unisco valutazioni della competenze con quelle dei figli raggruppandole per data
                $tot_valutazioni = [];
                $tot_valutazioni_tmp = $competenza['valutazioni_figli'];

                //pulisco le valutazioni di figli presenti in scrutinio
                if (count($tot_valutazioni_tmp > 0)) {
                    foreach ($tot_valutazioni_tmp as $data => $valutazioni) {
                        foreach ($valutazioni as $key => $valutazione) {
                            if (in_array($valutazione['dati_competenza']['id'], $elenco_id_competenze_scrutinio)) {
                                unset($tot_valutazioni_tmp[$data][$key]);
                                if (count($tot_valutazioni_tmp[$data]) == 0) {
                                    unset($tot_valutazioni_tmp[$data]);
                                }
                            }
                        }
                    }
                }

                if (count($competenza['valutazioni']) > 0) {
                    foreach ($competenza['valutazioni'] as $valutazione) {
                        if (!isset($tot_valutazioni_tmp[$valutazione['data']][$competenza['id']])) {
                            $tot_valutazioni_tmp[$valutazione['data']][$competenza['id']]['dati_competenza']['figlia'] = 'NO';
                        }
                        $tot_valutazioni_tmp[$valutazione['data']][$competenza['id']]['valutazioni'][] = $valutazione;
                    }
                }
                ksort($tot_valutazioni_tmp);

                // imposto le date nel formato italiano
                foreach ($tot_valutazioni_tmp as $data_iso => $val) {
                    $tot_valutazioni[date("d/m/Y", strtotime($data_iso))] = $val;
                }

                $array_generale['elenco_materie'][-1]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']]['elenco_studenti'][$studente['id_studente']]['valutazioni_totali'] = $tot_valutazioni;
                $array_generale['elenco_materie'][-1]['elenco_competenze'][$competenze_scrutinio['id_template_origine']][$competenza['id']]['elenco_studenti'][$studente['id_studente']]['scrutinio'] = $competenza['scrutinio'];

                unset($competenze_scrutinio['elenco'][$key_comp]);
            }
        }
    }

    // filtro le materie del docente solo alla fine altrimenti le competenze trasversali si 'sminchiano' avendo una base di materie differente
    // if ($parametro_visualizza_materie_tabellone == 'SOLO_SI_NO'){
    //     // tolgo le materie NV
    //     foreach ($array_generale['elenco_materie'] as $key => $materia){
    //         if ($materia['in_media_pagelle'] == 'NV'){
    //             unset($array_generale['elenco_materie'][$key]);
    //         }
    //     }
    // }

    if ($form_stato == 'professore') {
        $elenco_materie_docente = estrai_materie_multi_classe_del_professore($id_classe, $current_user);
        $elenco_id_materie_docente = array_column($elenco_materie_docente, 'id_materia');
        $elenco_id_materie_docente[] = -1;

        // per le trasversali impostare il blocco a livello di competenza in base ai limiti

        if ($coordinatore == 'NO') {
            // tolgo le materie non del docente
            foreach ($array_generale['elenco_materie'] as $key => $materia) {
                if (!in_array($materia['id_materia'], $elenco_id_materie_docente)) {
                    unset($array_generale['elenco_materie'][$key]);
                }
            }
        } else {
            // blocco le proposte per le materie non del coordinatore
            foreach ($array_generale['elenco_materie'] as $key => $materia) {
                if (!in_array($materia['id_materia'], $elenco_id_materie_docente)) {
                    $array_generale['elenco_materie'][$key]['proposta_bloccata'] = 'SI';
                }
            }
        }
    }

    return $array_generale;
}